## Project Overview:
Full-stack application using:
- **Backend**: Python with pip package management
- **Frontend**: React with JavaScript and npm package management

## VERY IMPORTANT:
Think hard about how to implement, keep the implementation simple, minimal, effective and adapted if not sure always ask for confirmations before rushing to implement on presumptions!
Always provide a comprehensive technical analysis and a plan before we start coding and ask for permission to implement.
