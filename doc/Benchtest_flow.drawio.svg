<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="761px" height="451px" viewBox="-0.5 -0.5 761 451" content="&lt;mxfile&gt;&lt;diagram id=&quot;QDJIYfuRFkdZthKyixd6&quot; name=&quot;Page-1&quot;&gt;7Vpdb+I6EP01vKJ8AnkE2u1Kt7uLypVu99ElJrHqxMgxBfrrd5w45MNpS1macKVISGQmM8Y+53iSSRjY82h/x9Em/MF8TAeW4e8H9s3AssyxM4Ev6TlkHs8yM0fAia+CCseSvGLlNJR3S3ycVAIFY1SQTdW5YnGMV6LiQ5yzXTVszWj1VzcowJpjuUJU9/5HfBFm3ok1LvzfMQnC/JfNkZediVAerFaShMhnu5LLvh3Yc86YyI6i/RxTCV6OS5b37Y2zx4lxHItTEhQRiTjka8M+LFWZMYvhaxaKiIJlwiEMyw+PYBi58VsaQzc3b/blkzeH3NoTkaYNPdNTdpZp2WNlF6nSKGcuMCcRFpgrn75KtfCEbflKLcRSwkA8wCrKyVxyiaU0hcwdZvAb/AABHFMkyEuVbaREExzjClzhQEHbDLPXEsznAuN2BUy+o68WmVFXyKi5vCC6VYPes0ADC+rHJgNKSKB2IRF4uUHpgnZQfKvgPaHVc8DZNvZ/bQUlEtvU7yP+/AuyiEjhGhrue4i9YC7w/l001FlXsavK/NHelYqmcoXlemkYF1CWe03KsnVlmWZX0rI1ad2lopAT5Ftg5n+tMsdsU2XW2Sobjt2K0C4hM6cNmaWpU87RoRSwYSQWSWnkhXQUJDnjKkumZ9RwzkYsUD9O7SQiHE3T/+DDjnEQ9QhFUqHxUyK/pjGih1e4lajzlioW+wruDzR+Adladg2Rka5b02oQ7ugSurXP1+3lZeter2yd2iVsfFnZuppsH3Bwu4c+xjKuVqmW06ZSnfOVal1eqqPrlapZZcl2LyvVkSbVBRLQk8VXrVXbalGrYw0iXbuxP5VPIArp+igJU4DMKhjSrxBOPZZhgzcRnD0fnzpY74H2oR5LkLgNiOS+z8lW06U18YZuhRPHrmGdbTCVV35a8eFQtlcbKtuY2lBn6N36bGt6Uv3Ii0WlgHTWZOazKSl2ziK5hM43sj2p3dbbbV509GL3gJMtlfXXuIX9yxMNoGRHIopSUaxZLHKdSLRWIaH+PTqwrZxjIqBByq1ZyDh5hXiUYwenuVDPPG2jErGUmWpMjhOIWeQQmzXXD7SvBN6jROSzYZSiTUKeaN6ZRSBGEs+YECxqjWC3Vqkdo4Fgp4Hg40Ojv2JYr9X/cjiyjAVLSLbJpmA9wkC/Na5hieJYjOeMMl5UhDWhtOZClASyiFO8lmkSI7JCdKrcEfF9OfIskd1xHNynYTdO4XlQq5cuBulrml5BQkjEMMJM3RvA3NwZfACfubxPd2Guc7DNwoaPDOdizmKYPiIpfxjUscNSITPOBBLo6ajdzwthdLIS8iJ+IvH1y8ZZvE803r8hmvTEd0F8U8f7ZcR7bxL/EweoJ75N4r0WiW9qXDhekYSwWDE+fHx87Bn/gHGN3SYRvMm42dSHfRnl+sX9AQMZtOe7Nb4b3vV8Gd/5GOXaLhvm5Ypx3HPeGueTNve49U6L9j1/VNZ3aH/RoY3GXXZoDa8p+w7tIvdrx61zlR1aw6u8vkPrivg2O7SGl2F9h9YV8a12aHpr3ndol797y0RwJR2a/hiu79Ba5rvVDk1/0dR3aB1w/oUdGpjF/9az963Fv//t2z8=&lt;/diagram&gt;&lt;/mxfile&gt;">
    <defs/>
    <g>
        <path d="M 73.52 113.7 L 174.99 33.94" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 179.12 30.69 L 175.78 37.77 L 174.99 33.94 L 171.45 32.27 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 80 131.43 L 173.77 111.33" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 178.91 110.23 L 172.8 115.12 L 173.77 111.33 L 171.33 108.28 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 80 154.29 L 174 187.86" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 178.95 189.62 L 171.18 190.57 L 174 187.86 L 173.53 183.97 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 0 90 L 50 90 L 80 120 L 80 190 L 0 190 L 0 90 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 50 90 L 50 120 L 80 120 Z" fill-opacity="0.05" fill="#000000" stroke="none" pointer-events="all"/>
        <path d="M 50 90 L 50 120 L 80 120" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 140px; margin-left: 1px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Log
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="40" y="144" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Log
                </text>
            </switch>
        </g>
        <path d="M 80 300 L 323.63 300" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 328.88 300 L 321.88 303.5 L 323.63 300 L 321.88 296.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 0 250 L 50 250 L 80 280 L 80 350 L 0 350 L 0 250 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 50 250 L 50 280 L 80 280 Z" fill-opacity="0.05" fill="#000000" stroke="none" pointer-events="all"/>
        <path d="M 50 250 L 50 280 L 80 280" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 300px; margin-left: 1px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Ground truth
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="40" y="304" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Ground truth
                </text>
            </switch>
        </g>
        <path d="M 300 30 L 410 30 Q 420 30 420 40 L 420 263.63" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 420 268.88 L 416.5 261.88 L 420 263.63 L 423.5 261.88 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="180" y="0" width="120" height="60" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 30px; margin-left: 181px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Keyword Analyzer
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="240" y="34" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Keyword Analyzer
                </text>
            </switch>
        </g>
        <path d="M 300 110 L 380 110 Q 390 110 390 120 L 390 263.63" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 390 268.88 L 386.5 261.88 L 390 263.63 L 393.5 261.88 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="180" y="80" width="120" height="60" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 110px; margin-left: 181px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                RegExps Analyzer
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="240" y="114" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    RegExps Analyzer
                </text>
            </switch>
        </g>
        <path d="M 300 190 L 350 190 Q 360 190 360 200 L 360 263.63" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 360 268.88 L 356.5 261.88 L 360 263.63 L 363.5 261.88 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="180" y="160" width="120" height="60" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 190px; margin-left: 181px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Patterns Analyzer
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="240" y="194" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Patterns Analyzer
                </text>
            </switch>
        </g>
        <path d="M 239.5 270 L 239.5 230" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="2 6" pointer-events="stroke"/>
        <path d="M 450 316.88 L 473.87 323.59" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 478.92 325.01 L 471.24 326.48 L 473.87 323.59 L 473.13 319.75 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="330" y="270" width="120" height="60" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 300px; margin-left: 331px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Compare
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="390" y="304" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Compare
                </text>
            </switch>
        </g>
        <path d="M 480 270 L 480 240 L 620 240 L 620 270" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 480 270 L 480 450 L 620 450 L 620 270" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 480 270 L 620 270" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 138px; height: 1px; padding-top: 255px; margin-left: 481px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                Results Errors
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="550" y="259" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Results Errors
                </text>
            </switch>
        </g>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 130px; height: 1px; padding-top: 285px; margin-left: 486px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left; max-height: 26px; overflow: hidden;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                True Positive: X/Y
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="486" y="289" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">
                    True Positive: X/Y
                </text>
            </switch>
        </g>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 130px; height: 1px; padding-top: 315px; margin-left: 486px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left; max-height: 26px; overflow: hidden;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                False Positive: X/Y
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="486" y="319" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">
                    False Positive: X/Y
                </text>
            </switch>
        </g>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 130px; height: 1px; padding-top: 345px; margin-left: 486px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left; max-height: 26px; overflow: hidden;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                False Negative: X/Y
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="486" y="349" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">
                    False Negative: X/Y
                </text>
            </switch>
        </g>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 130px; height: 1px; padding-top: 375px; margin-left: 486px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left; max-height: 26px; overflow: hidden;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                Precision: X.XXX
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="486" y="379" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">
                    Precision: X.XXX
                </text>
            </switch>
        </g>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 130px; height: 1px; padding-top: 405px; margin-left: 486px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left; max-height: 26px; overflow: hidden;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                Recall: X.XXX
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="486" y="409" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">
                    Recall: X.XXX
                </text>
            </switch>
        </g>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 130px; height: 1px; padding-top: 435px; margin-left: 486px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left; max-height: 26px; overflow: hidden;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                F1 Score: X.XXX
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="486" y="439" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">
                    F1 Score: X.XXX
                </text>
            </switch>
        </g>
        <path d="M 620 270 L 620 240 L 760 240 L 760 270" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 620 270 L 620 450 L 760 450 L 760 270" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 620 270 L 760 270" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 138px; height: 1px; padding-top: 255px; margin-left: 621px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                Results Hints
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="690" y="259" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Results Hints
                </text>
            </switch>
        </g>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 130px; height: 1px; padding-top: 285px; margin-left: 626px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left; max-height: 26px; overflow: hidden;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                True Positive: X/Y
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="626" y="289" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">
                    True Positive: X/Y
                </text>
            </switch>
        </g>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 130px; height: 1px; padding-top: 315px; margin-left: 626px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left; max-height: 26px; overflow: hidden;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                False Positive: X/Y
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="626" y="319" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">
                    False Positive: X/Y
                </text>
            </switch>
        </g>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 130px; height: 1px; padding-top: 345px; margin-left: 626px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left; max-height: 26px; overflow: hidden;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                False Negative: X/Y
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="626" y="349" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">
                    False Negative: X/Y
                </text>
            </switch>
        </g>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 130px; height: 1px; padding-top: 375px; margin-left: 626px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left; max-height: 26px; overflow: hidden;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                Precision: X.XXX
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="626" y="379" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">
                    Precision: X.XXX
                </text>
            </switch>
        </g>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 130px; height: 1px; padding-top: 405px; margin-left: 626px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left; max-height: 26px; overflow: hidden;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                Recall: X.XXX
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="626" y="409" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">
                    Recall: X.XXX
                </text>
            </switch>
        </g>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 130px; height: 1px; padding-top: 435px; margin-left: 626px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left; max-height: 26px; overflow: hidden;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                F1 Score: X.XXX
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="626" y="439" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">
                    F1 Score: X.XXX
                </text>
            </switch>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>