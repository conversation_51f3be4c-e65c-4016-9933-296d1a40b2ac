
**Frontend**

Who: <PERSON> & <PERSON>

- Security  
- Authentication  
- Readability  
- Access/Logout  
- Administration UI interface

---

**Backend**

Who: <PERSON><PERSON>

***Analyzer***  
- Patent  
- Agatic Approach  
  - Branch lines analyzed  
- RAG  
  - File RAG (desired)  
  - Vector database RAG (actual)

***Test Bench***  

Who: <PERSON>hai & Alban

- Measurements → Speed, fail rate, etc.  

- LLM Farm
- Metrics → Accuracy  
- Error file creation 

---

**Costs / Pricing**
 
- Per LLM query  
- Quadrant  
- VM on Azure 
- All solution cost estimation including scaling up plans

---

**Visualizer**

***Additional Features:***  
- Thumbs up/down chart  
- Page categories  
- Color mode  
- Chat feature
- Offline with chat disabled  

---

**Databases**  

- Feedback
- Quadrant info → Creation, Last update  
- User sessions  
- Quadrant custom collection
- Audit/event logs

---