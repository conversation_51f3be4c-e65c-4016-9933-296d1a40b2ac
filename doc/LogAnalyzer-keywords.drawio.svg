<?xml version="1.0" encoding="UTF-8"?>
<!-- Do not edit this file with editors other than draw.io -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" version="1.1" width="782px"
     height="233px" viewBox="-0.5 -0.5 782 233"
     content="&lt;mxfile host=&quot;Electron&quot; modified=&quot;2024-06-02T11:10:44.394Z&quot; agent=&quot;Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/24.4.7 Chrome/124.0.6367.207 Electron/30.0.6 Safari/537.36&quot; etag=&quot;8W_1BGUcLqWYqX5-aFDE&quot; version=&quot;24.4.7&quot; type=&quot;device&quot; scale=&quot;1&quot; border=&quot;0&quot;&gt;&#10;  &lt;diagram name=&quot;Page-1&quot; id=&quot;-BBJnwn80fFtNKehFn2V&quot;&gt;&#10;    &lt;mxGraphModel dx=&quot;2901&quot; dy=&quot;1196&quot; grid=&quot;1&quot; gridSize=&quot;10&quot; guides=&quot;1&quot; tooltips=&quot;1&quot; connect=&quot;1&quot; arrows=&quot;1&quot; fold=&quot;1&quot; page=&quot;1&quot; pageScale=&quot;1&quot; pageWidth=&quot;827&quot; pageHeight=&quot;1169&quot; math=&quot;0&quot; shadow=&quot;0&quot;&gt;&#10;      &lt;root&gt;&#10;        &lt;mxCell id=&quot;0&quot; /&gt;&#10;        &lt;mxCell id=&quot;1&quot; parent=&quot;0&quot; /&gt;&#10;        &lt;mxCell id=&quot;J4BZVxHwjlpHgjuq0SBH-5&quot; value=&quot;&amp;#xa;Analysis&amp;#xa;Result&quot; style=&quot;shape=note;whiteSpace=wrap;backgroundOutline=1;darkOpacity=0.05;fontFamily=&amp;quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&amp;quot;, &amp;quot;Avenir&amp;quot;, &amp;quot;Avenir Next&amp;quot;, &amp;quot;Segoe UI&amp;quot;, &amp;quot;Helvetica&amp;quot;, &amp;quot;Arial&amp;quot;, &amp;quot;sans-serif&amp;quot;;strokeWidth=3;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;240&quot; y=&quot;569&quot; width=&quot;69&quot; height=&quot;80&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;J4BZVxHwjlpHgjuq0SBH-56&quot; value=&quot;&amp;#xa;&amp;#xa;Keywords&amp;#xa;Dictionary&amp;#xa;JSON&quot; style=&quot;shape=note;whiteSpace=wrap;backgroundOutline=1;darkOpacity=0.05;fontFamily=&amp;quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&amp;quot;, &amp;quot;Avenir&amp;quot;, &amp;quot;Avenir Next&amp;quot;, &amp;quot;Segoe UI&amp;quot;, &amp;quot;Helvetica&amp;quot;, &amp;quot;Arial&amp;quot;, &amp;quot;sans-serif&amp;quot;;strokeWidth=3;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-244.5&quot; y=&quot;420&quot; width=&quot;69&quot; height=&quot;90&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;J4BZVxHwjlpHgjuq0SBH-57&quot; value=&quot;&amp;#xa;Log&amp;#xa;File&quot; style=&quot;shape=note;whiteSpace=wrap;backgroundOutline=1;darkOpacity=0.05;fontFamily=&amp;quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&amp;quot;, &amp;quot;Avenir&amp;quot;, &amp;quot;Avenir Next&amp;quot;, &amp;quot;Segoe UI&amp;quot;, &amp;quot;Helvetica&amp;quot;, &amp;quot;Arial&amp;quot;, &amp;quot;sans-serif&amp;quot;;strokeWidth=3;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-470&quot; y=&quot;560&quot; width=&quot;69&quot; height=&quot;90&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;J4BZVxHwjlpHgjuq0SBH-58&quot; value=&quot;   Keywords Method&quot; style=&quot;rounded=0;whiteSpace=wrap;strokeWidth=3;fontStyle=1;fontFamily=&amp;quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&amp;quot;, &amp;quot;Avenir&amp;quot;, &amp;quot;Avenir Next&amp;quot;, &amp;quot;Segoe UI&amp;quot;, &amp;quot;Helvetica&amp;quot;, &amp;quot;Arial&amp;quot;, &amp;quot;sans-serif&amp;quot;;labelPosition=center;verticalLabelPosition=middle;align=left;verticalAlign=middle;fontSize=14;fontColor=#99004D;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-320&quot; y=&quot;570&quot; width=&quot;220&quot; height=&quot;80&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;J4BZVxHwjlpHgjuq0SBH-59&quot; value=&quot;&quot; style=&quot;fillColor=#99004D;verticalLabelPosition=bottom;sketch=0;html=1;strokeColor=#ffffff;verticalAlign=top;align=center;points=[[0.145,0.145,0],[0.5,0,0],[0.855,0.145,0],[1,0.5,0],[0.855,0.855,0],[0.5,1,0],[0.145,0.855,0],[0,0.5,0]];pointerEvents=1;shape=mxgraph.cisco_safe.compositeIcon;bgIcon=ellipse;resIcon=mxgraph.cisco_safe.capability.anomaly_detection;fontColor=#99004D;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-174&quot; y=&quot;579&quot; width=&quot;62.5&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;J4BZVxHwjlpHgjuq0SBH-61&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;hachureGap=4;fontFamily=&amp;quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&amp;quot;, &amp;quot;Avenir&amp;quot;, &amp;quot;Avenir Next&amp;quot;, &amp;quot;Segoe UI&amp;quot;, &amp;quot;Helvetica&amp;quot;, &amp;quot;Arial&amp;quot;, &amp;quot;sans-serif&amp;quot;;fontSize=12;fontColor=default;strokeWidth=3;fontStyle=0;entryX=0.043;entryY=0.76;entryDx=0;entryDy=0;entryPerimeter=0;&quot; parent=&quot;1&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;-400&quot; y=&quot;610&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;-320.00299999999993&quot; y=&quot;609.6300000000001&quot; as=&quot;targetPoint&quot; /&gt;&#10;            &lt;Array as=&quot;points&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;J4BZVxHwjlpHgjuq0SBH-62&quot; value=&quot;Input&quot; style=&quot;edgeLabel;html=1;align=left;verticalAlign=middle;resizable=0;points=[];fontStyle=0;fontSize=12;&quot; parent=&quot;J4BZVxHwjlpHgjuq0SBH-61&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-0.5749&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;3&quot; y=&quot;-10&quot; as=&quot;offset&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;J4BZVxHwjlpHgjuq0SBH-64&quot; value=&quot;      for each log line&amp;#xa;         for each keyword in dictionary&amp;#xa;            match keyword vs log line&quot; style=&quot;dashed=0;whiteSpace=wrap;shape=mxgraph.dfd.loop;fontSize=12;strokeWidth=3;align=left;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-55&quot; y=&quot;570&quot; width=&quot;213&quot; height=&quot;80&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;J4BZVxHwjlpHgjuq0SBH-65&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;hachureGap=4;fontFamily=&amp;quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&amp;quot;, &amp;quot;Avenir&amp;quot;, &amp;quot;Avenir Next&amp;quot;, &amp;quot;Segoe UI&amp;quot;, &amp;quot;Helvetica&amp;quot;, &amp;quot;Arial&amp;quot;, &amp;quot;sans-serif&amp;quot;;fontSize=12;fontColor=default;strokeWidth=3;fontStyle=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;&quot; parent=&quot;1&quot; target=&quot;J4BZVxHwjlpHgjuq0SBH-58&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;-210&quot; y=&quot;510.37&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;-210&quot; y=&quot;560&quot; as=&quot;targetPoint&quot; /&gt;&#10;            &lt;Array as=&quot;points&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;J4BZVxHwjlpHgjuq0SBH-66&quot; value=&quot;Load&quot; style=&quot;edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontStyle=0;fontSize=12;labelPosition=center;verticalLabelPosition=middle;textDirection=vertical-rl;&quot; parent=&quot;J4BZVxHwjlpHgjuq0SBH-65&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-0.5749&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;10&quot; y=&quot;7&quot; as=&quot;offset&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;J4BZVxHwjlpHgjuq0SBH-68&quot; style=&quot;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0.017;entryY=0.502;entryDx=0;entryDy=0;entryPerimeter=0;endArrow=none;endFill=0;strokeWidth=3;&quot; parent=&quot;1&quot; source=&quot;J4BZVxHwjlpHgjuq0SBH-58&quot; target=&quot;J4BZVxHwjlpHgjuq0SBH-64&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;J4BZVxHwjlpHgjuq0SBH-69&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;hachureGap=4;fontFamily=&amp;quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&amp;quot;, &amp;quot;Avenir&amp;quot;, &amp;quot;Avenir Next&amp;quot;, &amp;quot;Segoe UI&amp;quot;, &amp;quot;Helvetica&amp;quot;, &amp;quot;Arial&amp;quot;, &amp;quot;sans-serif&amp;quot;;fontSize=12;fontColor=default;strokeWidth=3;fontStyle=0;entryX=0.043;entryY=0.76;entryDx=0;entryDy=0;entryPerimeter=0;&quot; parent=&quot;1&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;158&quot; y=&quot;609.04&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;237.99700000000007&quot; y=&quot;608.6700000000001&quot; as=&quot;targetPoint&quot; /&gt;&#10;            &lt;Array as=&quot;points&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;J4BZVxHwjlpHgjuq0SBH-70&quot; value=&quot;Output&quot; style=&quot;edgeLabel;html=1;align=left;verticalAlign=middle;resizable=0;points=[];fontStyle=0;fontSize=12;&quot; parent=&quot;J4BZVxHwjlpHgjuq0SBH-69&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-0.5749&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;-5&quot; y=&quot;-10&quot; as=&quot;offset&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;      &lt;/root&gt;&#10;    &lt;/mxGraphModel&gt;&#10;  &lt;/diagram&gt;&#10;&lt;/mxfile&gt;&#10;">
    <defs/>
    <g>
        <g>
            <path d="M 711 150 L 750 150 L 780 180 L 780 230 L 711 230 L 711 150 Z" fill="rgb(255, 255, 255)"
                  stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/>
            <path d="M 750 150 L 750 180 L 780 180 Z" fill-opacity="0.05" fill="#000000" stroke="none"
                  pointer-events="all"/>
            <path d="M 750 150 L 750 180 L 780 180" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3"
                  stroke-miterlimit="10" pointer-events="all"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject pointer-events="none" width="100%" height="100%"
                                   requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"
                                   style="overflow: visible; text-align: left;">
                        <div xmlns="http://www.w3.org/1999/xhtml"
                             style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 67px; height: 1px; padding-top: 190px; margin-left: 712px;">
                            <div data-drawio-colors="color: rgb(0, 0, 0); "
                                 style="box-sizing: border-box; font-size: 0px; text-align: center;">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, Avenir, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, Helvetica, Arial, &quot;sans-serif&quot;; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                    <br/>Analysis<br/>Result
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="746" y="194" fill="rgb(0, 0, 0)"
                          font-family="&quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, &quot;Avenir&quot;, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, &quot;Helvetica&quot;, &quot;Arial&quot;, &quot;sans-serif&quot;"
                          font-size="12px" text-anchor="middle">Analysis...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 226.5 1 L 265.5 1 L 295.5 31 L 295.5 91 L 226.5 91 L 226.5 1 Z" fill="rgb(255, 255, 255)"
                  stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/>
            <path d="M 265.5 1 L 265.5 31 L 295.5 31 Z" fill-opacity="0.05" fill="#000000" stroke="none"
                  pointer-events="all"/>
            <path d="M 265.5 1 L 265.5 31 L 295.5 31" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3"
                  stroke-miterlimit="10" pointer-events="all"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject pointer-events="none" width="100%" height="100%"
                                   requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"
                                   style="overflow: visible; text-align: left;">
                        <div xmlns="http://www.w3.org/1999/xhtml"
                             style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 67px; height: 1px; padding-top: 46px; margin-left: 228px;">
                            <div data-drawio-colors="color: rgb(0, 0, 0); "
                                 style="box-sizing: border-box; font-size: 0px; text-align: center;">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, Avenir, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, Helvetica, Arial, &quot;sans-serif&quot;; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                    <br/>
                                    <br/>Keywords<br/>Dictionary<br/>JSON
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="261" y="50" fill="rgb(0, 0, 0)"
                          font-family="&quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, &quot;Avenir&quot;, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, &quot;Helvetica&quot;, &quot;Arial&quot;, &quot;sans-serif&quot;"
                          font-size="12px" text-anchor="middle">Keywords...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 1 141 L 40 141 L 70 171 L 70 231 L 1 231 L 1 141 Z" fill="rgb(255, 255, 255)"
                  stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/>
            <path d="M 40 141 L 40 171 L 70 171 Z" fill-opacity="0.05" fill="#000000" stroke="none"
                  pointer-events="all"/>
            <path d="M 40 141 L 40 171 L 70 171" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3"
                  stroke-miterlimit="10" pointer-events="all"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject pointer-events="none" width="100%" height="100%"
                                   requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"
                                   style="overflow: visible; text-align: left;">
                        <div xmlns="http://www.w3.org/1999/xhtml"
                             style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 67px; height: 1px; padding-top: 186px; margin-left: 2px;">
                            <div data-drawio-colors="color: rgb(0, 0, 0); "
                                 style="box-sizing: border-box; font-size: 0px; text-align: center;">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, Avenir, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, Helvetica, Arial, &quot;sans-serif&quot;; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                    <br/>Log<br/>File
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="36" y="190" fill="rgb(0, 0, 0)"
                          font-family="&quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, &quot;Avenir&quot;, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, &quot;Helvetica&quot;, &quot;Arial&quot;, &quot;sans-serif&quot;"
                          font-size="12px" text-anchor="middle">Log...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="151" y="151" width="220" height="80" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)"
                  stroke-width="3" pointer-events="all"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject pointer-events="none" width="100%" height="100%"
                                   requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"
                                   style="overflow: visible; text-align: left;">
                        <div xmlns="http://www.w3.org/1999/xhtml"
                             style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 218px; height: 1px; padding-top: 191px; margin-left: 153px;">
                            <div data-drawio-colors="color: #99004D; "
                                 style="box-sizing: border-box; font-size: 0px; text-align: left;">
                                <div style="display: inline-block; font-size: 14px; font-family: &quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, Avenir, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, Helvetica, Arial, &quot;sans-serif&quot;; color: rgb(153, 0, 77); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">
                                       Keywords Method
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="153" y="195" fill="#99004D"
                          font-family="&quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, &quot;Avenir&quot;, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, &quot;Helvetica&quot;, &quot;Arial&quot;, &quot;sans-serif&quot;"
                          font-size="14px" font-weight="bold">   Keywords Method
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="297" y="160" width="62.5" height="60" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 332.51 192.3 C 332.06 192.3 331.73 191.86 331.73 191.56 C 331.73 191.09 332.06 190.82 332.51 190.82 C 332.83 190.82 333.28 191.09 333.28 191.56 C 333.28 191.86 332.83 192.3 332.51 192.3 Z M 331.88 187.5 C 331.88 187.2 332.06 187.07 332.51 187.07 C 332.83 187.07 333.14 187.2 333.14 187.5 C 333.14 189.6 333.14 189.6 333.14 189.6 C 333.14 189.74 332.83 190.04 332.51 190.04 C 332.06 190.04 331.88 189.74 331.88 189.6 L 331.88 187.5 Z M 338.6 192.3 C 338.43 192.13 338.43 192.13 338.43 192.13 C 333.28 184.19 333.28 184.19 333.28 184.19 C 333.14 184.06 332.83 183.75 332.51 183.75 C 332.06 183.75 331.73 184.06 331.73 184.36 C 326.42 192.13 326.42 192.13 326.42 192.13 C 326.24 192.3 326.1 192.61 326.1 192.75 C 326.1 193.05 326.56 193.35 327.19 193.35 C 337.83 193.35 337.83 193.35 337.83 193.35 C 338.29 193.35 338.6 193.05 338.6 192.75 C 338.6 192.61 338.6 192.3 338.6 192.3 Z M 338.12 195.25 C 336.71 196.6 334.53 197.34 332.48 197.34 C 330.3 197.34 328.25 196.6 326.85 195.25 C 325.43 193.9 324.52 191.94 324.52 190.01 C 324.52 188.19 325.43 186.26 326.85 184.91 C 328.25 183.56 330.3 182.78 332.48 182.78 C 334.53 182.78 336.71 183.56 338.12 184.91 C 341.21 187.75 341.21 192.41 338.12 195.25 Z M 351.23 204.1 C 342.49 195.86 342.49 195.86 342.49 195.86 C 345.3 191.63 344.81 186.09 340.9 182.34 C 338.58 180.38 335.44 179.2 332.48 179.2 C 329.52 179.2 326.53 180.38 324.2 182.34 C 319.52 186.53 319.52 193.46 324.2 197.65 C 326.53 199.74 329.52 200.78 332.48 200.78 C 334.67 200.78 336.85 200.35 338.72 199.3 C 347.63 207.41 347.63 207.41 347.63 207.41 C 348.26 207.99 349.5 207.99 350.31 207.41 C 351.23 206.5 351.23 206.5 351.23 206.5 C 352 205.76 352 204.53 351.23 204.1 Z M 316.23 206.04 C 316.06 205.91 315.6 205.6 315.46 205.44 C 315.6 205.6 316.06 205.91 316.23 206.04 Z M 308.24 194.19 C 308.24 194.05 308.24 194.05 308.1 193.74 C 308.24 194.05 308.24 194.05 308.24 194.19 Z M 308.24 185.81 C 308.24 185.94 308.24 186.11 308.1 186.24 C 308.24 186.11 308.24 185.94 308.24 185.81 Z M 310.28 180.53 C 310.45 180.1 310.59 179.97 310.73 179.66 C 310.59 179.97 310.45 180.1 310.28 180.53 Z M 310.91 200.81 C 310.73 200.5 310.59 200.2 310.59 199.89 C 310.59 200.2 310.73 200.5 310.91 200.81 Z M 316.37 174.38 C 317.15 174.38 317.78 174.69 317.78 175.44 C 317.78 176.04 317.33 176.49 316.55 176.49 C 315.46 176.49 315.46 176.49 315.46 176.49 C 315.46 174.69 315.46 174.69 315.46 174.69 C 315.29 174.69 315.15 174.86 314.97 174.99 C 315.46 174.56 316.06 174.09 316.55 173.81 C 316.23 173.95 316.06 174.09 315.74 174.38 L 316.37 174.38 Z M 316.55 180.1 C 315.46 180.1 315.46 180.1 315.46 180.1 C 315.46 177.7 315.46 177.7 315.46 177.7 C 316.69 177.7 316.69 177.7 316.69 177.7 C 317.64 177.7 317.96 178.13 317.96 178.91 C 317.96 179.79 317.64 180.1 316.55 180.1 Z M 328.43 209.79 C 323.88 209.79 319.83 208.44 316.55 206.22 C 316.69 206.51 317.15 206.65 317.47 206.78 C 317.47 200.06 317.47 200.06 317.47 200.06 C 319.97 200.06 319.97 200.06 319.97 200.06 C 319.97 198.54 319.97 198.54 319.97 198.54 C 312.96 198.54 312.96 198.54 312.96 198.54 C 312.96 200.06 312.96 200.06 312.96 200.06 C 315.46 200.06 315.46 200.06 315.46 200.06 C 315.46 205.44 315.46 205.44 315.46 205.44 C 313.73 204.09 312.32 202.47 310.91 200.81 C 311.05 200.81 311.05 200.81 311.05 200.81 C 310.91 200.06 310.59 199.46 310.14 199.01 C 310.14 199.46 310.28 199.59 310.59 199.89 C 309.51 198.24 308.55 196.14 308.24 194.19 C 309.65 194.19 309.65 194.19 309.65 194.19 C 309.65 190.6 309.65 190.6 309.65 190.6 C 312.96 194.19 312.96 194.19 312.96 194.19 C 315.46 194.19 315.46 194.19 315.46 194.19 C 311.38 189.69 311.38 189.69 311.38 189.69 C 315.15 185.81 315.15 185.81 315.15 185.81 C 312.64 185.81 312.64 185.81 312.64 185.81 C 309.65 189.38 309.65 189.38 309.65 189.38 C 309.65 185.81 309.65 185.81 309.65 185.81 C 308.24 185.81 308.24 185.81 308.24 185.81 C 308.55 183.99 309.37 182.19 310.28 180.53 C 310.14 180.84 309.96 181.14 309.82 181.45 C 311.55 181.45 311.55 181.45 311.55 181.45 C 310.73 179.66 310.73 179.66 310.73 179.66 C 312.01 177.84 313.24 176.49 314.82 175.16 C 314.33 175.44 313.87 175.91 313.42 176.49 C 313.42 181.45 313.42 181.45 313.42 181.45 C 316.55 181.45 316.55 181.45 316.55 181.45 C 319.06 181.45 320.14 180.53 320.14 179.05 C 320.14 177.84 319.34 177.26 318.24 176.96 C 319.06 176.78 319.65 176.22 319.65 175.3 C 319.65 174.09 319.06 173.51 317.47 173.21 C 317.15 173.34 316.83 173.64 316.55 173.81 C 319.97 171.55 323.88 170.34 328.43 170.34 C 339.67 170.34 348.89 179.05 348.89 189.99 C 348.89 192.84 348.43 195.53 347.17 197.94 C 349.53 200.2 349.53 200.2 349.53 200.2 C 351.22 197.19 352.03 193.74 352.03 189.99 C 352.03 177.56 341.39 167.19 328.43 167.19 C 322.01 167.19 316.06 169.59 311.55 173.95 C 307.14 178.31 304.5 183.99 304.5 189.99 C 304.5 202.6 315.29 212.8 328.43 212.8 C 334.52 212.8 340.12 210.53 344.35 206.78 C 342.02 204.69 342.02 204.69 342.02 204.69 C 338.43 207.84 333.56 209.79 328.43 209.79 Z M 323.73 199.15 C 323.73 199.15 323.73 199.15 323.73 199.15 C 323.41 198.84 323.1 198.71 322.96 198.4 C 322 198.4 322 198.4 322 198.4 C 322 203.2 322 203.2 322 203.2 C 322 204.69 323.41 205.6 325.92 205.6 C 328.24 205.6 329.51 204.69 329.51 203.2 C 329.51 201.85 329.51 201.85 329.51 201.85 C 328.88 201.72 328.24 201.54 327.64 201.24 C 327.64 203.2 327.64 203.2 327.64 203.2 C 327.64 204.12 327.01 204.55 325.92 204.55 C 324.83 204.55 324.19 204.12 324.19 203.2 C 324.19 199.45 324.19 199.45 324.19 199.45 C 324.05 199.45 323.87 199.31 323.73 199.15 Z M 337.01 200.8 C 336.38 201.11 335.74 201.24 335.14 201.41 C 334.51 204.12 334.51 204.12 334.51 204.12 C 333.88 201.72 333.88 201.72 333.88 201.72 C 333.24 201.85 332.96 201.85 332.47 201.85 C 332.01 201.85 332.01 201.85 332.01 201.85 C 333.24 205.6 333.24 205.6 333.24 205.6 C 335.6 205.6 335.6 205.6 335.6 205.6 L 337.01 200.8 Z M 339.34 179.2 C 339.51 178.76 339.51 177.99 339.51 177.24 C 339.51 173.79 338.1 172 335.29 172 C 332.01 172 332.01 172 332.01 172 C 332.01 177.24 332.01 177.24 332.01 177.24 C 332.78 177.24 332.78 177.24 332.78 177.24 C 333.1 177.24 333.56 177.24 334.05 177.24 C 334.05 173.79 334.05 173.79 334.05 173.79 C 335.29 173.79 335.29 173.79 335.29 173.79 C 336.69 173.79 337.47 174.84 337.47 177.24 C 337.47 177.54 337.47 177.85 337.47 178.28 C 338.1 178.59 338.73 178.89 339.34 179.2 Z M 325.92 173.66 C 326.87 173.66 327.46 174.24 327.64 175.59 C 329.51 175.59 329.51 175.59 329.51 175.59 C 329.19 173.19 327.78 172 325.92 172 C 323.55 172 322 173.97 322 177.24 C 322 179.34 322.64 180.86 323.55 181.6 C 324.05 181.16 324.37 180.69 324.83 180.38 C 324.37 179.81 324.05 178.76 324.05 177.24 C 324.05 174.71 324.83 173.66 325.92 173.66 Z M 317.01 186.4 C 317.01 193.6 317.01 193.6 317.01 193.6 C 318.56 193.6 318.56 193.6 318.56 193.6 C 318.56 188.63 318.56 188.63 318.56 188.63 C 319.19 189.85 319.19 189.85 319.19 189.85 C 319.37 189.11 319.51 188.37 319.51 187.59 C 319.05 186.4 319.05 186.4 319.05 186.4 L 317.01 186.4 Z M 297 190 C 297 173.51 311.05 160 328.25 160 C 345.62 160 359.5 173.51 359.5 190 C 359.5 206.66 345.62 220 328.25 220 C 311.05 220 297 206.66 297 190 Z"
                  fill="#99004d" stroke="none" pointer-events="all"/>
            <ellipse cx="328.25" cy="190" rx="30.625" ry="29.4" fill="#ffffff" stroke="none" pointer-events="all"/>
            <rect x="297" y="160" width="62.5" height="60" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 332.51 192.3 C 332.06 192.3 331.73 191.86 331.73 191.56 C 331.73 191.09 332.06 190.82 332.51 190.82 C 332.83 190.82 333.28 191.09 333.28 191.56 C 333.28 191.86 332.83 192.3 332.51 192.3 Z M 331.88 187.5 C 331.88 187.2 332.06 187.07 332.51 187.07 C 332.83 187.07 333.14 187.2 333.14 187.5 C 333.14 189.6 333.14 189.6 333.14 189.6 C 333.14 189.74 332.83 190.04 332.51 190.04 C 332.06 190.04 331.88 189.74 331.88 189.6 L 331.88 187.5 Z M 338.6 192.3 C 338.43 192.13 338.43 192.13 338.43 192.13 C 333.28 184.19 333.28 184.19 333.28 184.19 C 333.14 184.06 332.83 183.75 332.51 183.75 C 332.06 183.75 331.73 184.06 331.73 184.36 C 326.42 192.13 326.42 192.13 326.42 192.13 C 326.24 192.3 326.1 192.61 326.1 192.75 C 326.1 193.05 326.56 193.35 327.19 193.35 C 337.83 193.35 337.83 193.35 337.83 193.35 C 338.29 193.35 338.6 193.05 338.6 192.75 C 338.6 192.61 338.6 192.3 338.6 192.3 Z M 338.12 195.25 C 336.71 196.6 334.53 197.34 332.48 197.34 C 330.3 197.34 328.25 196.6 326.85 195.25 C 325.43 193.9 324.52 191.94 324.52 190.01 C 324.52 188.19 325.43 186.26 326.85 184.91 C 328.25 183.56 330.3 182.78 332.48 182.78 C 334.53 182.78 336.71 183.56 338.12 184.91 C 341.21 187.75 341.21 192.41 338.12 195.25 Z M 351.23 204.1 C 342.49 195.86 342.49 195.86 342.49 195.86 C 345.3 191.63 344.81 186.09 340.9 182.34 C 338.58 180.38 335.44 179.2 332.48 179.2 C 329.52 179.2 326.53 180.38 324.2 182.34 C 319.52 186.53 319.52 193.46 324.2 197.65 C 326.53 199.74 329.52 200.78 332.48 200.78 C 334.67 200.78 336.85 200.35 338.72 199.3 C 347.63 207.41 347.63 207.41 347.63 207.41 C 348.26 207.99 349.5 207.99 350.31 207.41 C 351.23 206.5 351.23 206.5 351.23 206.5 C 352 205.76 352 204.53 351.23 204.1 Z M 316.23 206.04 C 316.06 205.91 315.6 205.6 315.46 205.44 C 315.6 205.6 316.06 205.91 316.23 206.04 Z M 308.24 194.19 C 308.24 194.05 308.24 194.05 308.1 193.74 C 308.24 194.05 308.24 194.05 308.24 194.19 Z M 308.24 185.81 C 308.24 185.94 308.24 186.11 308.1 186.24 C 308.24 186.11 308.24 185.94 308.24 185.81 Z M 310.28 180.53 C 310.45 180.1 310.59 179.97 310.73 179.66 C 310.59 179.97 310.45 180.1 310.28 180.53 Z M 310.91 200.81 C 310.73 200.5 310.59 200.2 310.59 199.89 C 310.59 200.2 310.73 200.5 310.91 200.81 Z M 316.37 174.38 C 317.15 174.38 317.78 174.69 317.78 175.44 C 317.78 176.04 317.33 176.49 316.55 176.49 C 315.46 176.49 315.46 176.49 315.46 176.49 C 315.46 174.69 315.46 174.69 315.46 174.69 C 315.29 174.69 315.15 174.86 314.97 174.99 C 315.46 174.56 316.06 174.09 316.55 173.81 C 316.23 173.95 316.06 174.09 315.74 174.38 L 316.37 174.38 Z M 316.55 180.1 C 315.46 180.1 315.46 180.1 315.46 180.1 C 315.46 177.7 315.46 177.7 315.46 177.7 C 316.69 177.7 316.69 177.7 316.69 177.7 C 317.64 177.7 317.96 178.13 317.96 178.91 C 317.96 179.79 317.64 180.1 316.55 180.1 Z M 328.43 209.79 C 323.88 209.79 319.83 208.44 316.55 206.22 C 316.69 206.51 317.15 206.65 317.47 206.78 C 317.47 200.06 317.47 200.06 317.47 200.06 C 319.97 200.06 319.97 200.06 319.97 200.06 C 319.97 198.54 319.97 198.54 319.97 198.54 C 312.96 198.54 312.96 198.54 312.96 198.54 C 312.96 200.06 312.96 200.06 312.96 200.06 C 315.46 200.06 315.46 200.06 315.46 200.06 C 315.46 205.44 315.46 205.44 315.46 205.44 C 313.73 204.09 312.32 202.47 310.91 200.81 C 311.05 200.81 311.05 200.81 311.05 200.81 C 310.91 200.06 310.59 199.46 310.14 199.01 C 310.14 199.46 310.28 199.59 310.59 199.89 C 309.51 198.24 308.55 196.14 308.24 194.19 C 309.65 194.19 309.65 194.19 309.65 194.19 C 309.65 190.6 309.65 190.6 309.65 190.6 C 312.96 194.19 312.96 194.19 312.96 194.19 C 315.46 194.19 315.46 194.19 315.46 194.19 C 311.38 189.69 311.38 189.69 311.38 189.69 C 315.15 185.81 315.15 185.81 315.15 185.81 C 312.64 185.81 312.64 185.81 312.64 185.81 C 309.65 189.38 309.65 189.38 309.65 189.38 C 309.65 185.81 309.65 185.81 309.65 185.81 C 308.24 185.81 308.24 185.81 308.24 185.81 C 308.55 183.99 309.37 182.19 310.28 180.53 C 310.14 180.84 309.96 181.14 309.82 181.45 C 311.55 181.45 311.55 181.45 311.55 181.45 C 310.73 179.66 310.73 179.66 310.73 179.66 C 312.01 177.84 313.24 176.49 314.82 175.16 C 314.33 175.44 313.87 175.91 313.42 176.49 C 313.42 181.45 313.42 181.45 313.42 181.45 C 316.55 181.45 316.55 181.45 316.55 181.45 C 319.06 181.45 320.14 180.53 320.14 179.05 C 320.14 177.84 319.34 177.26 318.24 176.96 C 319.06 176.78 319.65 176.22 319.65 175.3 C 319.65 174.09 319.06 173.51 317.47 173.21 C 317.15 173.34 316.83 173.64 316.55 173.81 C 319.97 171.55 323.88 170.34 328.43 170.34 C 339.67 170.34 348.89 179.05 348.89 189.99 C 348.89 192.84 348.43 195.53 347.17 197.94 C 349.53 200.2 349.53 200.2 349.53 200.2 C 351.22 197.19 352.03 193.74 352.03 189.99 C 352.03 177.56 341.39 167.19 328.43 167.19 C 322.01 167.19 316.06 169.59 311.55 173.95 C 307.14 178.31 304.5 183.99 304.5 189.99 C 304.5 202.6 315.29 212.8 328.43 212.8 C 334.52 212.8 340.12 210.53 344.35 206.78 C 342.02 204.69 342.02 204.69 342.02 204.69 C 338.43 207.84 333.56 209.79 328.43 209.79 Z M 323.73 199.15 C 323.73 199.15 323.73 199.15 323.73 199.15 C 323.41 198.84 323.1 198.71 322.96 198.4 C 322 198.4 322 198.4 322 198.4 C 322 203.2 322 203.2 322 203.2 C 322 204.69 323.41 205.6 325.92 205.6 C 328.24 205.6 329.51 204.69 329.51 203.2 C 329.51 201.85 329.51 201.85 329.51 201.85 C 328.88 201.72 328.24 201.54 327.64 201.24 C 327.64 203.2 327.64 203.2 327.64 203.2 C 327.64 204.12 327.01 204.55 325.92 204.55 C 324.83 204.55 324.19 204.12 324.19 203.2 C 324.19 199.45 324.19 199.45 324.19 199.45 C 324.05 199.45 323.87 199.31 323.73 199.15 Z M 337.01 200.8 C 336.38 201.11 335.74 201.24 335.14 201.41 C 334.51 204.12 334.51 204.12 334.51 204.12 C 333.88 201.72 333.88 201.72 333.88 201.72 C 333.24 201.85 332.96 201.85 332.47 201.85 C 332.01 201.85 332.01 201.85 332.01 201.85 C 333.24 205.6 333.24 205.6 333.24 205.6 C 335.6 205.6 335.6 205.6 335.6 205.6 L 337.01 200.8 Z M 339.34 179.2 C 339.51 178.76 339.51 177.99 339.51 177.24 C 339.51 173.79 338.1 172 335.29 172 C 332.01 172 332.01 172 332.01 172 C 332.01 177.24 332.01 177.24 332.01 177.24 C 332.78 177.24 332.78 177.24 332.78 177.24 C 333.1 177.24 333.56 177.24 334.05 177.24 C 334.05 173.79 334.05 173.79 334.05 173.79 C 335.29 173.79 335.29 173.79 335.29 173.79 C 336.69 173.79 337.47 174.84 337.47 177.24 C 337.47 177.54 337.47 177.85 337.47 178.28 C 338.1 178.59 338.73 178.89 339.34 179.2 Z M 325.92 173.66 C 326.87 173.66 327.46 174.24 327.64 175.59 C 329.51 175.59 329.51 175.59 329.51 175.59 C 329.19 173.19 327.78 172 325.92 172 C 323.55 172 322 173.97 322 177.24 C 322 179.34 322.64 180.86 323.55 181.6 C 324.05 181.16 324.37 180.69 324.83 180.38 C 324.37 179.81 324.05 178.76 324.05 177.24 C 324.05 174.71 324.83 173.66 325.92 173.66 Z M 317.01 186.4 C 317.01 193.6 317.01 193.6 317.01 193.6 C 318.56 193.6 318.56 193.6 318.56 193.6 C 318.56 188.63 318.56 188.63 318.56 188.63 C 319.19 189.85 319.19 189.85 319.19 189.85 C 319.37 189.11 319.51 188.37 319.51 187.59 C 319.05 186.4 319.05 186.4 319.05 186.4 L 317.01 186.4 Z M 297 190 C 297 173.51 311.05 160 328.25 160 C 345.62 160 359.5 173.51 359.5 190 C 359.5 206.66 345.62 220 328.25 220 C 311.05 220 297 206.66 297 190 Z"
                  fill="#99004d" stroke="none" pointer-events="all"/>
        </g>
        <g>
            <path d="M 71 191 L 140.89 190.68" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10"
                  pointer-events="stroke"/>
            <path d="M 147.64 190.65 L 138.66 195.19 L 140.89 190.68 L 138.62 186.19 Z" fill="rgb(0, 0, 0)"
                  stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject pointer-events="none" width="100%" height="100%"
                                   requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"
                                   style="overflow: visible; text-align: left;">
                        <div xmlns="http://www.w3.org/1999/xhtml"
                             style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 181px; margin-left: 93px;">
                            <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); "
                                 style="box-sizing: border-box; font-size: 0px; text-align: left;">
                                <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                    Input
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="93" y="185" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Input</text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 613 151 C 631.72 174.38 631.72 207.62 613 231 L 432 231 C 413.28 207.62 413.28 174.38 432 151 Z"
                  fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10"
                  pointer-events="all"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject pointer-events="none" width="100%" height="100%"
                                   requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"
                                   style="overflow: visible; text-align: left;">
                        <div xmlns="http://www.w3.org/1999/xhtml"
                             style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 211px; height: 1px; padding-top: 191px; margin-left: 418px;">
                            <div data-drawio-colors="color: rgb(0, 0, 0); "
                                 style="box-sizing: border-box; font-size: 0px; text-align: left;">
                                <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                          for each log line<br/>         for each keyword in dictionary<br/>         
                                      match keyword vs log line
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="418" y="195" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">for each log
                        line...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 261 91.37 L 261 140.9" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10"
                  pointer-events="stroke"/>
            <path d="M 261 147.65 L 256.5 138.65 L 261 140.9 L 265.5 138.65 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)"
                  stroke-width="3" stroke-miterlimit="10" pointer-events="all"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject pointer-events="none" width="100%" height="100%"
                                   requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"
                                   style="overflow: visible; text-align: left;">
                        <div xmlns="http://www.w3.org/1999/xhtml"
                             style="display: flex; align-items: unsafe center; justify-content: unsafe center; writing-mode: vertical-rl; width: 1px; height: 1px; padding-top: 112px; margin-left: 272px;">
                            <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); "
                                 style="box-sizing: border-box; font-size: 0px; text-align: center;">
                                <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                    Load
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="272" y="115" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px"
                          text-anchor="middle">Load
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 371 191 L 419.62 191.16" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3"
                  stroke-miterlimit="10" pointer-events="stroke"/>
        </g>
        <g>
            <path d="M 629 190.04 L 698.89 189.72" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3"
                  stroke-miterlimit="10" pointer-events="stroke"/>
            <path d="M 705.64 189.69 L 696.66 194.23 L 698.89 189.72 L 696.62 185.23 Z" fill="rgb(0, 0, 0)"
                  stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject pointer-events="none" width="100%" height="100%"
                                   requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"
                                   style="overflow: visible; text-align: left;">
                        <div xmlns="http://www.w3.org/1999/xhtml"
                             style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 180px; margin-left: 643px;">
                            <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); "
                                 style="box-sizing: border-box; font-size: 0px; text-align: left;">
                                <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                    Output
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="643" y="184" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Output</text>
                </switch>
            </g>
        </g>
    </g>
</svg>
