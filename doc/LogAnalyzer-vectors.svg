<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" version="1.1" width="892px"
     height="337px" viewBox="-0.5 -0.5 892 337">
    <defs>
        <style type="text/css">@import url(https://fonts.googleapis.com/css2?family=Architects+Daughter:wght@400;500);&#xa;</style>
    </defs>
    <g>
        <g>
            <rect x="394" y="45" width="347" height="290" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3"
                  pointer-events="all"/>
        </g>
        <g>
            <path d="M 821 146 L 860 146 L 890 176 L 890 236 L 821 236 L 821 146 Z" fill="rgb(255, 255, 255)"
                  stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/>
            <path d="M 860 146 L 860 176 L 890 176 Z" fill-opacity="0.05" fill="#000000" stroke="none"
                  pointer-events="all"/>
            <path d="M 860 146 L 860 176 L 890 176" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3"
                  stroke-miterlimit="10" pointer-events="all"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject pointer-events="none" width="100%" height="100%"
                                   requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"
                                   style="overflow: visible; text-align: left;">
                        <div xmlns="http://www.w3.org/1999/xhtml"
                             style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 67px; height: 1px; padding-top: 191px; margin-left: 822px;">
                            <div data-drawio-colors="color: rgb(0, 0, 0); "
                                 style="box-sizing: border-box; font-size: 0px; text-align: center;">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, Avenir, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, Helvetica, Arial, &quot;sans-serif&quot;; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                    <div>
                                        <br/>
                                    </div>
                                    Analysis
                                    <div>Result</div>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="856" y="195" fill="rgb(0, 0, 0)"
                          font-family="&quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, &quot;Avenir&quot;, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, &quot;Helvetica&quot;, &quot;Arial&quot;, &quot;sans-serif&quot;"
                          font-size="12px" text-anchor="middle">Analysis...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 226.5 1 L 265.5 1 L 295.5 31 L 295.5 91 L 226.5 91 L 226.5 1 Z" fill="rgb(255, 255, 255)"
                  stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/>
            <path d="M 265.5 1 L 265.5 31 L 295.5 31 Z" fill-opacity="0.05" fill="#000000" stroke="none"
                  pointer-events="all"/>
            <path d="M 265.5 1 L 265.5 31 L 295.5 31" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3"
                  stroke-miterlimit="10" pointer-events="all"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject pointer-events="none" width="100%" height="100%"
                                   requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"
                                   style="overflow: visible; text-align: left;">
                        <div xmlns="http://www.w3.org/1999/xhtml"
                             style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 67px; height: 1px; padding-top: 46px; margin-left: 228px;">
                            <div data-drawio-colors="color: rgb(0, 0, 0); "
                                 style="box-sizing: border-box; font-size: 0px; text-align: center;">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, Avenir, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, Helvetica, Arial, &quot;sans-serif&quot;; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                    <div>
                                        <br/>
                                    </div>
                                    <div>
                                        <br/>
                                    </div>
                                    <div>Patterns</div>
                                    <div>Dictionary</div>
                                    <div>JSON</div>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="261" y="50" fill="rgb(0, 0, 0)"
                          font-family="&quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, &quot;Avenir&quot;, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, &quot;Helvetica&quot;, &quot;Arial&quot;, &quot;sans-serif&quot;"
                          font-size="12px" text-anchor="middle">Patterns...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 1 141 L 40 141 L 70 171 L 70 231 L 1 231 L 1 141 Z" fill="rgb(255, 255, 255)"
                  stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/>
            <path d="M 40 141 L 40 171 L 70 171 Z" fill-opacity="0.05" fill="#000000" stroke="none"
                  pointer-events="all"/>
            <path d="M 40 141 L 40 171 L 70 171" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3"
                  stroke-miterlimit="10" pointer-events="all"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject pointer-events="none" width="100%" height="100%"
                                   requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"
                                   style="overflow: visible; text-align: left;">
                        <div xmlns="http://www.w3.org/1999/xhtml"
                             style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 67px; height: 1px; padding-top: 186px; margin-left: 2px;">
                            <div data-drawio-colors="color: rgb(0, 0, 0); "
                                 style="box-sizing: border-box; font-size: 0px; text-align: center;">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, Avenir, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, Helvetica, Arial, &quot;sans-serif&quot;; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                    <div>
                                        <br/>
                                    </div>
                                    <div>Log</div>
                                    <div>File</div>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="36" y="190" fill="rgb(0, 0, 0)"
                          font-family="&quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, &quot;Avenir&quot;, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, &quot;Helvetica&quot;, &quot;Arial&quot;, &quot;sans-serif&quot;"
                          font-size="12px" text-anchor="middle">Log...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="151" y="151" width="220" height="80" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)"
                  stroke-width="3" pointer-events="all"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject pointer-events="none" width="100%" height="100%"
                                   requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"
                                   style="overflow: visible; text-align: left;">
                        <div xmlns="http://www.w3.org/1999/xhtml"
                             style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 218px; height: 1px; padding-top: 191px; margin-left: 153px;">
                            <div data-drawio-colors="color: #99004D; "
                                 style="box-sizing: border-box; font-size: 0px; text-align: left;">
                                <div style="display: inline-block; font-size: 14px; font-family: &quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, Avenir, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, Helvetica, Arial, &quot;sans-serif&quot;; color: rgb(153, 0, 77); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">
                                       Vectors Method
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="153" y="195" fill="#99004D"
                          font-family="&quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, &quot;Avenir&quot;, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, &quot;Helvetica&quot;, &quot;Arial&quot;, &quot;sans-serif&quot;"
                          font-size="14px" font-weight="bold">   Vectors Method
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="299" y="160" width="62.5" height="60" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 334.51 192.3 C 334.06 192.3 333.73 191.86 333.73 191.56 C 333.73 191.09 334.06 190.82 334.51 190.82 C 334.83 190.82 335.28 191.09 335.28 191.56 C 335.28 191.86 334.83 192.3 334.51 192.3 Z M 333.88 187.5 C 333.88 187.2 334.06 187.07 334.51 187.07 C 334.83 187.07 335.14 187.2 335.14 187.5 C 335.14 189.6 335.14 189.6 335.14 189.6 C 335.14 189.74 334.83 190.04 334.51 190.04 C 334.06 190.04 333.88 189.74 333.88 189.6 L 333.88 187.5 Z M 340.6 192.3 C 340.43 192.13 340.43 192.13 340.43 192.13 C 335.28 184.19 335.28 184.19 335.28 184.19 C 335.14 184.06 334.83 183.75 334.51 183.75 C 334.06 183.75 333.73 184.06 333.73 184.36 C 328.42 192.13 328.42 192.13 328.42 192.13 C 328.24 192.3 328.1 192.61 328.1 192.75 C 328.1 193.05 328.56 193.35 329.19 193.35 C 339.83 193.35 339.83 193.35 339.83 193.35 C 340.29 193.35 340.6 193.05 340.6 192.75 C 340.6 192.61 340.6 192.3 340.6 192.3 Z M 340.12 195.25 C 338.71 196.6 336.53 197.34 334.48 197.34 C 332.3 197.34 330.25 196.6 328.85 195.25 C 327.43 193.9 326.52 191.94 326.52 190.01 C 326.52 188.19 327.43 186.26 328.85 184.91 C 330.25 183.56 332.3 182.78 334.48 182.78 C 336.53 182.78 338.71 183.56 340.12 184.91 C 343.21 187.75 343.21 192.41 340.12 195.25 Z M 353.23 204.1 C 344.49 195.86 344.49 195.86 344.49 195.86 C 347.3 191.63 346.81 186.09 342.9 182.34 C 340.58 180.38 337.44 179.2 334.48 179.2 C 331.52 179.2 328.53 180.38 326.2 182.34 C 321.52 186.53 321.52 193.46 326.2 197.65 C 328.53 199.74 331.52 200.78 334.48 200.78 C 336.67 200.78 338.85 200.35 340.72 199.3 C 349.63 207.41 349.63 207.41 349.63 207.41 C 350.26 207.99 351.5 207.99 352.31 207.41 C 353.23 206.5 353.23 206.5 353.23 206.5 C 354 205.76 354 204.53 353.23 204.1 Z M 318.23 206.04 C 318.06 205.91 317.6 205.6 317.46 205.44 C 317.6 205.6 318.06 205.91 318.23 206.04 Z M 310.24 194.19 C 310.24 194.05 310.24 194.05 310.1 193.74 C 310.24 194.05 310.24 194.05 310.24 194.19 Z M 310.24 185.81 C 310.24 185.94 310.24 186.11 310.1 186.24 C 310.24 186.11 310.24 185.94 310.24 185.81 Z M 312.28 180.53 C 312.45 180.1 312.59 179.97 312.73 179.66 C 312.59 179.97 312.45 180.1 312.28 180.53 Z M 312.91 200.81 C 312.73 200.5 312.59 200.2 312.59 199.89 C 312.59 200.2 312.73 200.5 312.91 200.81 Z M 318.37 174.38 C 319.15 174.38 319.78 174.69 319.78 175.44 C 319.78 176.04 319.33 176.49 318.55 176.49 C 317.46 176.49 317.46 176.49 317.46 176.49 C 317.46 174.69 317.46 174.69 317.46 174.69 C 317.29 174.69 317.15 174.86 316.97 174.99 C 317.46 174.56 318.06 174.09 318.55 173.81 C 318.23 173.95 318.06 174.09 317.74 174.38 L 318.37 174.38 Z M 318.55 180.1 C 317.46 180.1 317.46 180.1 317.46 180.1 C 317.46 177.7 317.46 177.7 317.46 177.7 C 318.69 177.7 318.69 177.7 318.69 177.7 C 319.64 177.7 319.96 178.13 319.96 178.91 C 319.96 179.79 319.64 180.1 318.55 180.1 Z M 330.43 209.79 C 325.88 209.79 321.83 208.44 318.55 206.22 C 318.69 206.51 319.15 206.65 319.47 206.78 C 319.47 200.06 319.47 200.06 319.47 200.06 C 321.97 200.06 321.97 200.06 321.97 200.06 C 321.97 198.54 321.97 198.54 321.97 198.54 C 314.96 198.54 314.96 198.54 314.96 198.54 C 314.96 200.06 314.96 200.06 314.96 200.06 C 317.46 200.06 317.46 200.06 317.46 200.06 C 317.46 205.44 317.46 205.44 317.46 205.44 C 315.73 204.09 314.32 202.47 312.91 200.81 C 313.05 200.81 313.05 200.81 313.05 200.81 C 312.91 200.06 312.59 199.46 312.14 199.01 C 312.14 199.46 312.28 199.59 312.59 199.89 C 311.51 198.24 310.55 196.14 310.24 194.19 C 311.65 194.19 311.65 194.19 311.65 194.19 C 311.65 190.6 311.65 190.6 311.65 190.6 C 314.96 194.19 314.96 194.19 314.96 194.19 C 317.46 194.19 317.46 194.19 317.46 194.19 C 313.38 189.69 313.38 189.69 313.38 189.69 C 317.15 185.81 317.15 185.81 317.15 185.81 C 314.64 185.81 314.64 185.81 314.64 185.81 C 311.65 189.38 311.65 189.38 311.65 189.38 C 311.65 185.81 311.65 185.81 311.65 185.81 C 310.24 185.81 310.24 185.81 310.24 185.81 C 310.55 183.99 311.37 182.19 312.28 180.53 C 312.14 180.84 311.96 181.14 311.82 181.45 C 313.55 181.45 313.55 181.45 313.55 181.45 C 312.73 179.66 312.73 179.66 312.73 179.66 C 314.01 177.84 315.24 176.49 316.82 175.16 C 316.33 175.44 315.87 175.91 315.42 176.49 C 315.42 181.45 315.42 181.45 315.42 181.45 C 318.55 181.45 318.55 181.45 318.55 181.45 C 321.06 181.45 322.14 180.53 322.14 179.05 C 322.14 177.84 321.34 177.26 320.24 176.96 C 321.06 176.78 321.65 176.22 321.65 175.3 C 321.65 174.09 321.06 173.51 319.47 173.21 C 319.15 173.34 318.83 173.64 318.55 173.81 C 321.97 171.55 325.88 170.34 330.43 170.34 C 341.67 170.34 350.89 179.05 350.89 189.99 C 350.89 192.84 350.43 195.53 349.17 197.94 C 351.53 200.2 351.53 200.2 351.53 200.2 C 353.22 197.19 354.03 193.74 354.03 189.99 C 354.03 177.56 343.39 167.19 330.43 167.19 C 324.01 167.19 318.06 169.59 313.55 173.95 C 309.14 178.31 306.5 183.99 306.5 189.99 C 306.5 202.6 317.29 212.8 330.43 212.8 C 336.52 212.8 342.12 210.53 346.35 206.78 C 344.02 204.69 344.02 204.69 344.02 204.69 C 340.43 207.84 335.56 209.79 330.43 209.79 Z M 325.73 199.15 C 325.73 199.15 325.73 199.15 325.73 199.15 C 325.41 198.84 325.1 198.71 324.96 198.4 C 324 198.4 324 198.4 324 198.4 C 324 203.2 324 203.2 324 203.2 C 324 204.69 325.41 205.6 327.92 205.6 C 330.24 205.6 331.51 204.69 331.51 203.2 C 331.51 201.85 331.51 201.85 331.51 201.85 C 330.88 201.72 330.24 201.54 329.64 201.24 C 329.64 203.2 329.64 203.2 329.64 203.2 C 329.64 204.12 329.01 204.55 327.92 204.55 C 326.83 204.55 326.19 204.12 326.19 203.2 C 326.19 199.45 326.19 199.45 326.19 199.45 C 326.05 199.45 325.87 199.31 325.73 199.15 Z M 339.01 200.8 C 338.38 201.11 337.74 201.24 337.14 201.41 C 336.51 204.12 336.51 204.12 336.51 204.12 C 335.88 201.72 335.88 201.72 335.88 201.72 C 335.24 201.85 334.96 201.85 334.47 201.85 C 334.01 201.85 334.01 201.85 334.01 201.85 C 335.24 205.6 335.24 205.6 335.24 205.6 C 337.6 205.6 337.6 205.6 337.6 205.6 L 339.01 200.8 Z M 341.34 179.2 C 341.51 178.76 341.51 177.99 341.51 177.24 C 341.51 173.79 340.1 172 337.29 172 C 334.01 172 334.01 172 334.01 172 C 334.01 177.24 334.01 177.24 334.01 177.24 C 334.78 177.24 334.78 177.24 334.78 177.24 C 335.1 177.24 335.56 177.24 336.05 177.24 C 336.05 173.79 336.05 173.79 336.05 173.79 C 337.29 173.79 337.29 173.79 337.29 173.79 C 338.69 173.79 339.47 174.84 339.47 177.24 C 339.47 177.54 339.47 177.85 339.47 178.28 C 340.1 178.59 340.73 178.89 341.34 179.2 Z M 327.92 173.66 C 328.87 173.66 329.46 174.24 329.64 175.59 C 331.51 175.59 331.51 175.59 331.51 175.59 C 331.19 173.19 329.78 172 327.92 172 C 325.55 172 324 173.97 324 177.24 C 324 179.34 324.64 180.86 325.55 181.6 C 326.05 181.16 326.37 180.69 326.83 180.38 C 326.37 179.81 326.05 178.76 326.05 177.24 C 326.05 174.71 326.83 173.66 327.92 173.66 Z M 319.01 186.4 C 319.01 193.6 319.01 193.6 319.01 193.6 C 320.56 193.6 320.56 193.6 320.56 193.6 C 320.56 188.63 320.56 188.63 320.56 188.63 C 321.19 189.85 321.19 189.85 321.19 189.85 C 321.37 189.11 321.51 188.37 321.51 187.59 C 321.05 186.4 321.05 186.4 321.05 186.4 L 319.01 186.4 Z M 299 190 C 299 173.51 313.05 160 330.25 160 C 347.62 160 361.5 173.51 361.5 190 C 361.5 206.66 347.62 220 330.25 220 C 313.05 220 299 206.66 299 190 Z"
                  fill="#99004d" stroke="none" pointer-events="all"/>
            <ellipse cx="330.25" cy="190" rx="30.625" ry="29.4" fill="#ffffff" stroke="none" pointer-events="all"/>
            <rect x="299" y="160" width="62.5" height="60" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 334.51 192.3 C 334.06 192.3 333.73 191.86 333.73 191.56 C 333.73 191.09 334.06 190.82 334.51 190.82 C 334.83 190.82 335.28 191.09 335.28 191.56 C 335.28 191.86 334.83 192.3 334.51 192.3 Z M 333.88 187.5 C 333.88 187.2 334.06 187.07 334.51 187.07 C 334.83 187.07 335.14 187.2 335.14 187.5 C 335.14 189.6 335.14 189.6 335.14 189.6 C 335.14 189.74 334.83 190.04 334.51 190.04 C 334.06 190.04 333.88 189.74 333.88 189.6 L 333.88 187.5 Z M 340.6 192.3 C 340.43 192.13 340.43 192.13 340.43 192.13 C 335.28 184.19 335.28 184.19 335.28 184.19 C 335.14 184.06 334.83 183.75 334.51 183.75 C 334.06 183.75 333.73 184.06 333.73 184.36 C 328.42 192.13 328.42 192.13 328.42 192.13 C 328.24 192.3 328.1 192.61 328.1 192.75 C 328.1 193.05 328.56 193.35 329.19 193.35 C 339.83 193.35 339.83 193.35 339.83 193.35 C 340.29 193.35 340.6 193.05 340.6 192.75 C 340.6 192.61 340.6 192.3 340.6 192.3 Z M 340.12 195.25 C 338.71 196.6 336.53 197.34 334.48 197.34 C 332.3 197.34 330.25 196.6 328.85 195.25 C 327.43 193.9 326.52 191.94 326.52 190.01 C 326.52 188.19 327.43 186.26 328.85 184.91 C 330.25 183.56 332.3 182.78 334.48 182.78 C 336.53 182.78 338.71 183.56 340.12 184.91 C 343.21 187.75 343.21 192.41 340.12 195.25 Z M 353.23 204.1 C 344.49 195.86 344.49 195.86 344.49 195.86 C 347.3 191.63 346.81 186.09 342.9 182.34 C 340.58 180.38 337.44 179.2 334.48 179.2 C 331.52 179.2 328.53 180.38 326.2 182.34 C 321.52 186.53 321.52 193.46 326.2 197.65 C 328.53 199.74 331.52 200.78 334.48 200.78 C 336.67 200.78 338.85 200.35 340.72 199.3 C 349.63 207.41 349.63 207.41 349.63 207.41 C 350.26 207.99 351.5 207.99 352.31 207.41 C 353.23 206.5 353.23 206.5 353.23 206.5 C 354 205.76 354 204.53 353.23 204.1 Z M 318.23 206.04 C 318.06 205.91 317.6 205.6 317.46 205.44 C 317.6 205.6 318.06 205.91 318.23 206.04 Z M 310.24 194.19 C 310.24 194.05 310.24 194.05 310.1 193.74 C 310.24 194.05 310.24 194.05 310.24 194.19 Z M 310.24 185.81 C 310.24 185.94 310.24 186.11 310.1 186.24 C 310.24 186.11 310.24 185.94 310.24 185.81 Z M 312.28 180.53 C 312.45 180.1 312.59 179.97 312.73 179.66 C 312.59 179.97 312.45 180.1 312.28 180.53 Z M 312.91 200.81 C 312.73 200.5 312.59 200.2 312.59 199.89 C 312.59 200.2 312.73 200.5 312.91 200.81 Z M 318.37 174.38 C 319.15 174.38 319.78 174.69 319.78 175.44 C 319.78 176.04 319.33 176.49 318.55 176.49 C 317.46 176.49 317.46 176.49 317.46 176.49 C 317.46 174.69 317.46 174.69 317.46 174.69 C 317.29 174.69 317.15 174.86 316.97 174.99 C 317.46 174.56 318.06 174.09 318.55 173.81 C 318.23 173.95 318.06 174.09 317.74 174.38 L 318.37 174.38 Z M 318.55 180.1 C 317.46 180.1 317.46 180.1 317.46 180.1 C 317.46 177.7 317.46 177.7 317.46 177.7 C 318.69 177.7 318.69 177.7 318.69 177.7 C 319.64 177.7 319.96 178.13 319.96 178.91 C 319.96 179.79 319.64 180.1 318.55 180.1 Z M 330.43 209.79 C 325.88 209.79 321.83 208.44 318.55 206.22 C 318.69 206.51 319.15 206.65 319.47 206.78 C 319.47 200.06 319.47 200.06 319.47 200.06 C 321.97 200.06 321.97 200.06 321.97 200.06 C 321.97 198.54 321.97 198.54 321.97 198.54 C 314.96 198.54 314.96 198.54 314.96 198.54 C 314.96 200.06 314.96 200.06 314.96 200.06 C 317.46 200.06 317.46 200.06 317.46 200.06 C 317.46 205.44 317.46 205.44 317.46 205.44 C 315.73 204.09 314.32 202.47 312.91 200.81 C 313.05 200.81 313.05 200.81 313.05 200.81 C 312.91 200.06 312.59 199.46 312.14 199.01 C 312.14 199.46 312.28 199.59 312.59 199.89 C 311.51 198.24 310.55 196.14 310.24 194.19 C 311.65 194.19 311.65 194.19 311.65 194.19 C 311.65 190.6 311.65 190.6 311.65 190.6 C 314.96 194.19 314.96 194.19 314.96 194.19 C 317.46 194.19 317.46 194.19 317.46 194.19 C 313.38 189.69 313.38 189.69 313.38 189.69 C 317.15 185.81 317.15 185.81 317.15 185.81 C 314.64 185.81 314.64 185.81 314.64 185.81 C 311.65 189.38 311.65 189.38 311.65 189.38 C 311.65 185.81 311.65 185.81 311.65 185.81 C 310.24 185.81 310.24 185.81 310.24 185.81 C 310.55 183.99 311.37 182.19 312.28 180.53 C 312.14 180.84 311.96 181.14 311.82 181.45 C 313.55 181.45 313.55 181.45 313.55 181.45 C 312.73 179.66 312.73 179.66 312.73 179.66 C 314.01 177.84 315.24 176.49 316.82 175.16 C 316.33 175.44 315.87 175.91 315.42 176.49 C 315.42 181.45 315.42 181.45 315.42 181.45 C 318.55 181.45 318.55 181.45 318.55 181.45 C 321.06 181.45 322.14 180.53 322.14 179.05 C 322.14 177.84 321.34 177.26 320.24 176.96 C 321.06 176.78 321.65 176.22 321.65 175.3 C 321.65 174.09 321.06 173.51 319.47 173.21 C 319.15 173.34 318.83 173.64 318.55 173.81 C 321.97 171.55 325.88 170.34 330.43 170.34 C 341.67 170.34 350.89 179.05 350.89 189.99 C 350.89 192.84 350.43 195.53 349.17 197.94 C 351.53 200.2 351.53 200.2 351.53 200.2 C 353.22 197.19 354.03 193.74 354.03 189.99 C 354.03 177.56 343.39 167.19 330.43 167.19 C 324.01 167.19 318.06 169.59 313.55 173.95 C 309.14 178.31 306.5 183.99 306.5 189.99 C 306.5 202.6 317.29 212.8 330.43 212.8 C 336.52 212.8 342.12 210.53 346.35 206.78 C 344.02 204.69 344.02 204.69 344.02 204.69 C 340.43 207.84 335.56 209.79 330.43 209.79 Z M 325.73 199.15 C 325.73 199.15 325.73 199.15 325.73 199.15 C 325.41 198.84 325.1 198.71 324.96 198.4 C 324 198.4 324 198.4 324 198.4 C 324 203.2 324 203.2 324 203.2 C 324 204.69 325.41 205.6 327.92 205.6 C 330.24 205.6 331.51 204.69 331.51 203.2 C 331.51 201.85 331.51 201.85 331.51 201.85 C 330.88 201.72 330.24 201.54 329.64 201.24 C 329.64 203.2 329.64 203.2 329.64 203.2 C 329.64 204.12 329.01 204.55 327.92 204.55 C 326.83 204.55 326.19 204.12 326.19 203.2 C 326.19 199.45 326.19 199.45 326.19 199.45 C 326.05 199.45 325.87 199.31 325.73 199.15 Z M 339.01 200.8 C 338.38 201.11 337.74 201.24 337.14 201.41 C 336.51 204.12 336.51 204.12 336.51 204.12 C 335.88 201.72 335.88 201.72 335.88 201.72 C 335.24 201.85 334.96 201.85 334.47 201.85 C 334.01 201.85 334.01 201.85 334.01 201.85 C 335.24 205.6 335.24 205.6 335.24 205.6 C 337.6 205.6 337.6 205.6 337.6 205.6 L 339.01 200.8 Z M 341.34 179.2 C 341.51 178.76 341.51 177.99 341.51 177.24 C 341.51 173.79 340.1 172 337.29 172 C 334.01 172 334.01 172 334.01 172 C 334.01 177.24 334.01 177.24 334.01 177.24 C 334.78 177.24 334.78 177.24 334.78 177.24 C 335.1 177.24 335.56 177.24 336.05 177.24 C 336.05 173.79 336.05 173.79 336.05 173.79 C 337.29 173.79 337.29 173.79 337.29 173.79 C 338.69 173.79 339.47 174.84 339.47 177.24 C 339.47 177.54 339.47 177.85 339.47 178.28 C 340.1 178.59 340.73 178.89 341.34 179.2 Z M 327.92 173.66 C 328.87 173.66 329.46 174.24 329.64 175.59 C 331.51 175.59 331.51 175.59 331.51 175.59 C 331.19 173.19 329.78 172 327.92 172 C 325.55 172 324 173.97 324 177.24 C 324 179.34 324.64 180.86 325.55 181.6 C 326.05 181.16 326.37 180.69 326.83 180.38 C 326.37 179.81 326.05 178.76 326.05 177.24 C 326.05 174.71 326.83 173.66 327.92 173.66 Z M 319.01 186.4 C 319.01 193.6 319.01 193.6 319.01 193.6 C 320.56 193.6 320.56 193.6 320.56 193.6 C 320.56 188.63 320.56 188.63 320.56 188.63 C 321.19 189.85 321.19 189.85 321.19 189.85 C 321.37 189.11 321.51 188.37 321.51 187.59 C 321.05 186.4 321.05 186.4 321.05 186.4 L 319.01 186.4 Z M 299 190 C 299 173.51 313.05 160 330.25 160 C 347.62 160 361.5 173.51 361.5 190 C 361.5 206.66 347.62 220 330.25 220 C 313.05 220 299 206.66 299 190 Z"
                  fill="#99004d" stroke="none" pointer-events="all"/>
        </g>
        <g>
            <path d="M 71 191 L 140.89 190.68" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10"
                  pointer-events="stroke"/>
            <path d="M 147.64 190.65 L 138.66 195.19 L 140.89 190.68 L 138.62 186.19 Z" fill="rgb(0, 0, 0)"
                  stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject pointer-events="none" width="100%" height="100%"
                                   requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"
                                   style="overflow: visible; text-align: left;">
                        <div xmlns="http://www.w3.org/1999/xhtml"
                             style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 181px; margin-left: 93px;">
                            <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); "
                                 style="box-sizing: border-box; font-size: 0px; text-align: left;">
                                <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                    Input
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="93" y="185" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Input</text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 261 91.37 L 261 140.9" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10"
                  pointer-events="stroke"/>
            <path d="M 261 147.65 L 256.5 138.65 L 261 140.9 L 265.5 138.65 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)"
                  stroke-width="3" stroke-miterlimit="10" pointer-events="all"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject pointer-events="none" width="100%" height="100%"
                                   requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"
                                   style="overflow: visible; text-align: left;">
                        <div xmlns="http://www.w3.org/1999/xhtml"
                             style="display: flex; align-items: unsafe center; justify-content: unsafe center; writing-mode: vertical-rl; width: 1px; height: 1px; padding-top: 112px; margin-left: 272px;">
                            <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); "
                                 style="box-sizing: border-box; font-size: 0px; text-align: center;">
                                <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                    Load
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="272" y="115" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px"
                          text-anchor="middle">Load
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 372 191 L 394 191.16" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10"
                  pointer-events="stroke"/>
        </g>
        <g>
            <path d="M 750 190.77 L 741 190.77 L 809.9 190.77" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3"
                  stroke-miterlimit="10" pointer-events="stroke"/>
            <path d="M 816.65 190.77 L 807.65 195.27 L 809.9 190.77 L 807.65 186.27 Z" fill="rgb(0, 0, 0)"
                  stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject pointer-events="none" width="100%" height="100%"
                                   requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"
                                   style="overflow: visible; text-align: left;">
                        <div xmlns="http://www.w3.org/1999/xhtml"
                             style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 181px; margin-left: 754px;">
                            <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); "
                                 style="box-sizing: border-box; font-size: 0px; text-align: left;">
                                <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                    Output
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="754" y="185" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Output</text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="404" y="61" width="327" height="60" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)"
                  stroke-width="3" pointer-events="all"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject pointer-events="none" width="100%" height="100%"
                                   requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"
                                   style="overflow: visible; text-align: left;">
                        <div xmlns="http://www.w3.org/1999/xhtml"
                             style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 325px; height: 1px; padding-top: 91px; margin-left: 406px;">
                            <div data-drawio-colors="color: #000000; "
                                 style="box-sizing: border-box; font-size: 0px; text-align: left;">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, Avenir, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, Helvetica, Arial, &quot;sans-serif&quot;; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                          <b>load</b> SentenceTransformer
                                    <div><b>      connect</b>to Qdrant Vector DB
                                    </div>
                                    <div>  <b>    create</b>patterns collection
                                    </div>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="406" y="95" fill="#000000"
                          font-family="&quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, &quot;Avenir&quot;, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, &quot;Helvetica&quot;, &quot;Arial&quot;, &quot;sans-serif&quot;"
                          font-size="12px">load SentenceTransformer...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 567.5 121 L 570 121 L 570 140.9" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3"
                  stroke-miterlimit="10" pointer-events="stroke"/>
            <path d="M 570 147.65 L 565.5 138.65 L 570 140.9 L 574.5 138.65 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)"
                  stroke-width="3" stroke-miterlimit="10" pointer-events="all"/>
        </g>
        <g>
            <path d="M 715.8 241 C 733.58 263.21 733.58 294.79 715.8 317 L 420.2 317 C 402.42 294.79 402.42 263.21 420.2 241 Z"
                  fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10"
                  pointer-events="all"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject pointer-events="none" width="100%" height="100%"
                                   requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"
                                   style="overflow: visible; text-align: left;">
                        <div xmlns="http://www.w3.org/1999/xhtml"
                             style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 324px; height: 1px; padding-top: 279px; margin-left: 407px;">
                            <div data-drawio-colors="color: rgb(0, 0, 0); "
                                 style="box-sizing: border-box; font-size: 0px; text-align: left;">
                                <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                    <b>      for</b>each log line embedding
                                    <div>         <b>search</b>in 
                                        <span style="font-family: &quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, Avenir, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, Helvetica, Arial, &quot;sans-serif&quot;; background-color: initial;">
                                            patterns collection
                                        </span>
                                    </div>
                                    <div>
                                        <span style="font-family: &quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, Avenir, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, Helvetica, Arial, &quot;sans-serif&quot;; background-color: initial;">
                                                     <b>get</b> cosine distance score
                                        </span>
                                    </div>
                                    <div>
                                        <span style="font-family: &quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, Avenir, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, Helvetica, Arial, &quot;sans-serif&quot;; background-color: initial;">
                                                     <b>classify</b>log line based on result
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="407" y="283" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">for each log line
                        embedding...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 567.45 211 L 567.45 230.9" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3"
                  stroke-miterlimit="10" pointer-events="stroke"/>
            <path d="M 567.45 237.65 L 562.95 228.65 L 567.45 230.9 L 571.95 228.65 Z" fill="rgb(0, 0, 0)"
                  stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/>
        </g>
        <g>
            <rect x="404" y="151" width="327" height="60" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)"
                  stroke-width="3" pointer-events="all"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject pointer-events="none" width="100%" height="100%"
                                   requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"
                                   style="overflow: visible; text-align: left;">
                        <div xmlns="http://www.w3.org/1999/xhtml"
                             style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 325px; height: 1px; padding-top: 181px; margin-left: 406px;">
                            <div data-drawio-colors="color: #000000; "
                                 style="box-sizing: border-box; font-size: 0px; text-align: left;">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, Avenir, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, Helvetica, Arial, &quot;sans-serif&quot;; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                    <div style="font-family: Helvetica;"><b>      generate</b> embeddings for Patterns
                                        Dictionary items
                                    </div>
                                    <div style="font-family: Helvetica;">      <b>upload </b>embeddings into 
                                        <span style="font-family: &quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, Avenir, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, Helvetica, Arial, &quot;sans-serif&quot;; background-color: initial;">
                                            patterns collection
                                        </span>
                                    </div>
                                    <div style="font-family: Helvetica;"><b>      generate</b> embeddings for all log
                                        lines
                                        <span style="font-family: &quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, Avenir, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, Helvetica, Arial, &quot;sans-serif&quot;; background-color: initial;">
                                            <br/>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="406" y="185" fill="#000000"
                          font-family="&quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, &quot;Avenir&quot;, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, &quot;Helvetica&quot;, &quot;Arial&quot;, &quot;sans-serif&quot;"
                          font-size="12px">generate embeddings for Patterns Dictionary item...
                    </text>
                </switch>
            </g>
        </g>
    </g>
</svg>
