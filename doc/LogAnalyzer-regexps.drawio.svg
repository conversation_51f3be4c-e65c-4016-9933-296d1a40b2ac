<?xml version="1.0" encoding="UTF-8"?>
<!-- Do not edit this file with editors other than draw.io -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" version="1.1" width="782px"
     height="233px" viewBox="-0.5 -0.5 782 233"
     content="&lt;mxfile host=&quot;Electron&quot; modified=&quot;2024-06-02T11:10:00.716Z&quot; agent=&quot;Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/24.4.7 Chrome/124.0.6367.207 Electron/30.0.6 Safari/537.36&quot; etag=&quot;-tA-rPJx7Hb-huYI4mOX&quot; version=&quot;24.4.7&quot; type=&quot;device&quot; scale=&quot;1&quot; border=&quot;0&quot;&gt;&#10;  &lt;diagram name=&quot;Page-1&quot; id=&quot;-BBJnwn80fFtNKehFn2V&quot;&gt;&#10;    &lt;mxGraphModel dx=&quot;2901&quot; dy=&quot;1196&quot; grid=&quot;1&quot; gridSize=&quot;10&quot; guides=&quot;1&quot; tooltips=&quot;1&quot; connect=&quot;1&quot; arrows=&quot;1&quot; fold=&quot;1&quot; page=&quot;1&quot; pageScale=&quot;1&quot; pageWidth=&quot;827&quot; pageHeight=&quot;1169&quot; math=&quot;0&quot; shadow=&quot;0&quot;&gt;&#10;      &lt;root&gt;&#10;        &lt;mxCell id=&quot;0&quot; /&gt;&#10;        &lt;mxCell id=&quot;1&quot; parent=&quot;0&quot; /&gt;&#10;        &lt;mxCell id=&quot;J4BZVxHwjlpHgjuq0SBH-5&quot; value=&quot;&amp;#xa;Analysis&amp;#xa;Result&quot; style=&quot;shape=note;whiteSpace=wrap;backgroundOutline=1;darkOpacity=0.05;fontFamily=&amp;quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&amp;quot;, &amp;quot;Avenir&amp;quot;, &amp;quot;Avenir Next&amp;quot;, &amp;quot;Segoe UI&amp;quot;, &amp;quot;Helvetica&amp;quot;, &amp;quot;Arial&amp;quot;, &amp;quot;sans-serif&amp;quot;;strokeWidth=3;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;240&quot; y=&quot;569&quot; width=&quot;69&quot; height=&quot;80&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;J4BZVxHwjlpHgjuq0SBH-56&quot; value=&quot;&amp;#xa;&amp;#xa;RegExps&amp;#xa;Dictionary&amp;#xa;JSON&quot; style=&quot;shape=note;whiteSpace=wrap;backgroundOutline=1;darkOpacity=0.05;fontFamily=&amp;quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&amp;quot;, &amp;quot;Avenir&amp;quot;, &amp;quot;Avenir Next&amp;quot;, &amp;quot;Segoe UI&amp;quot;, &amp;quot;Helvetica&amp;quot;, &amp;quot;Arial&amp;quot;, &amp;quot;sans-serif&amp;quot;;strokeWidth=3;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-244.5&quot; y=&quot;420&quot; width=&quot;69&quot; height=&quot;90&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;J4BZVxHwjlpHgjuq0SBH-57&quot; value=&quot;&amp;#xa;Log&amp;#xa;File&quot; style=&quot;shape=note;whiteSpace=wrap;backgroundOutline=1;darkOpacity=0.05;fontFamily=&amp;quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&amp;quot;, &amp;quot;Avenir&amp;quot;, &amp;quot;Avenir Next&amp;quot;, &amp;quot;Segoe UI&amp;quot;, &amp;quot;Helvetica&amp;quot;, &amp;quot;Arial&amp;quot;, &amp;quot;sans-serif&amp;quot;;strokeWidth=3;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-470&quot; y=&quot;560&quot; width=&quot;69&quot; height=&quot;90&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;J4BZVxHwjlpHgjuq0SBH-58&quot; value=&quot;   Regular Expressions&amp;#xa;   Method&quot; style=&quot;rounded=0;whiteSpace=wrap;strokeWidth=3;fontStyle=1;fontFamily=&amp;quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&amp;quot;, &amp;quot;Avenir&amp;quot;, &amp;quot;Avenir Next&amp;quot;, &amp;quot;Segoe UI&amp;quot;, &amp;quot;Helvetica&amp;quot;, &amp;quot;Arial&amp;quot;, &amp;quot;sans-serif&amp;quot;;labelPosition=center;verticalLabelPosition=middle;align=left;verticalAlign=middle;fontSize=14;fontColor=#99004D;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-320&quot; y=&quot;570&quot; width=&quot;220&quot; height=&quot;80&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;J4BZVxHwjlpHgjuq0SBH-59&quot; value=&quot;&quot; style=&quot;fillColor=#99004D;verticalLabelPosition=bottom;sketch=0;html=1;strokeColor=#ffffff;verticalAlign=top;align=center;points=[[0.145,0.145,0],[0.5,0,0],[0.855,0.145,0],[1,0.5,0],[0.855,0.855,0],[0.5,1,0],[0.145,0.855,0],[0,0.5,0]];pointerEvents=1;shape=mxgraph.cisco_safe.compositeIcon;bgIcon=ellipse;resIcon=mxgraph.cisco_safe.capability.anomaly_detection;fontColor=#99004D;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-170&quot; y=&quot;579&quot; width=&quot;62.5&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;J4BZVxHwjlpHgjuq0SBH-61&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;hachureGap=4;fontFamily=&amp;quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&amp;quot;, &amp;quot;Avenir&amp;quot;, &amp;quot;Avenir Next&amp;quot;, &amp;quot;Segoe UI&amp;quot;, &amp;quot;Helvetica&amp;quot;, &amp;quot;Arial&amp;quot;, &amp;quot;sans-serif&amp;quot;;fontSize=12;fontColor=default;strokeWidth=3;fontStyle=0;entryX=0.043;entryY=0.76;entryDx=0;entryDy=0;entryPerimeter=0;&quot; parent=&quot;1&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;-400&quot; y=&quot;610&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;-320.00299999999993&quot; y=&quot;609.6300000000001&quot; as=&quot;targetPoint&quot; /&gt;&#10;            &lt;Array as=&quot;points&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;J4BZVxHwjlpHgjuq0SBH-62&quot; value=&quot;Input&quot; style=&quot;edgeLabel;html=1;align=left;verticalAlign=middle;resizable=0;points=[];fontStyle=0;fontSize=12;&quot; parent=&quot;J4BZVxHwjlpHgjuq0SBH-61&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-0.5749&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;3&quot; y=&quot;-10&quot; as=&quot;offset&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;J4BZVxHwjlpHgjuq0SBH-64&quot; value=&quot;      precompile RegExp dictionary&amp;#xa;      for each log line&amp;#xa;         for each RegExp in dictionary&amp;#xa;            search RegExp in log line&quot; style=&quot;dashed=0;whiteSpace=wrap;shape=mxgraph.dfd.loop;fontSize=12;strokeWidth=3;align=left;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-55&quot; y=&quot;570&quot; width=&quot;213&quot; height=&quot;80&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;J4BZVxHwjlpHgjuq0SBH-65&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;hachureGap=4;fontFamily=&amp;quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&amp;quot;, &amp;quot;Avenir&amp;quot;, &amp;quot;Avenir Next&amp;quot;, &amp;quot;Segoe UI&amp;quot;, &amp;quot;Helvetica&amp;quot;, &amp;quot;Arial&amp;quot;, &amp;quot;sans-serif&amp;quot;;fontSize=12;fontColor=default;strokeWidth=3;fontStyle=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;&quot; parent=&quot;1&quot; target=&quot;J4BZVxHwjlpHgjuq0SBH-58&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;-210&quot; y=&quot;510.37&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;-210&quot; y=&quot;560&quot; as=&quot;targetPoint&quot; /&gt;&#10;            &lt;Array as=&quot;points&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;J4BZVxHwjlpHgjuq0SBH-66&quot; value=&quot;Load&quot; style=&quot;edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontStyle=0;fontSize=12;labelPosition=center;verticalLabelPosition=middle;textDirection=vertical-rl;&quot; parent=&quot;J4BZVxHwjlpHgjuq0SBH-65&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-0.5749&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;10&quot; y=&quot;7&quot; as=&quot;offset&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;J4BZVxHwjlpHgjuq0SBH-68&quot; style=&quot;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0.017;entryY=0.502;entryDx=0;entryDy=0;entryPerimeter=0;endArrow=none;endFill=0;strokeWidth=3;&quot; parent=&quot;1&quot; source=&quot;J4BZVxHwjlpHgjuq0SBH-58&quot; target=&quot;J4BZVxHwjlpHgjuq0SBH-64&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;J4BZVxHwjlpHgjuq0SBH-69&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;hachureGap=4;fontFamily=&amp;quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&amp;quot;, &amp;quot;Avenir&amp;quot;, &amp;quot;Avenir Next&amp;quot;, &amp;quot;Segoe UI&amp;quot;, &amp;quot;Helvetica&amp;quot;, &amp;quot;Arial&amp;quot;, &amp;quot;sans-serif&amp;quot;;fontSize=12;fontColor=default;strokeWidth=3;fontStyle=0;entryX=0.043;entryY=0.76;entryDx=0;entryDy=0;entryPerimeter=0;&quot; parent=&quot;1&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;158&quot; y=&quot;609.04&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;237.99700000000007&quot; y=&quot;608.6700000000001&quot; as=&quot;targetPoint&quot; /&gt;&#10;            &lt;Array as=&quot;points&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;J4BZVxHwjlpHgjuq0SBH-70&quot; value=&quot;Output&quot; style=&quot;edgeLabel;html=1;align=left;verticalAlign=middle;resizable=0;points=[];fontStyle=0;fontSize=12;&quot; parent=&quot;J4BZVxHwjlpHgjuq0SBH-69&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-0.5749&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;-5&quot; y=&quot;-10&quot; as=&quot;offset&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;      &lt;/root&gt;&#10;    &lt;/mxGraphModel&gt;&#10;  &lt;/diagram&gt;&#10;&lt;/mxfile&gt;&#10;">
    <defs/>
    <g>
        <g>
            <path d="M 711 150 L 750 150 L 780 180 L 780 230 L 711 230 L 711 150 Z" fill="rgb(255, 255, 255)"
                  stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/>
            <path d="M 750 150 L 750 180 L 780 180 Z" fill-opacity="0.05" fill="#000000" stroke="none"
                  pointer-events="all"/>
            <path d="M 750 150 L 750 180 L 780 180" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3"
                  stroke-miterlimit="10" pointer-events="all"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject pointer-events="none" width="100%" height="100%"
                                   requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"
                                   style="overflow: visible; text-align: left;">
                        <div xmlns="http://www.w3.org/1999/xhtml"
                             style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 67px; height: 1px; padding-top: 190px; margin-left: 712px;">
                            <div data-drawio-colors="color: rgb(0, 0, 0); "
                                 style="box-sizing: border-box; font-size: 0px; text-align: center;">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, Avenir, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, Helvetica, Arial, &quot;sans-serif&quot;; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                    <br/>Analysis<br/>Result
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="746" y="194" fill="rgb(0, 0, 0)"
                          font-family="&quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, &quot;Avenir&quot;, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, &quot;Helvetica&quot;, &quot;Arial&quot;, &quot;sans-serif&quot;"
                          font-size="12px" text-anchor="middle">Analysis...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 226.5 1 L 265.5 1 L 295.5 31 L 295.5 91 L 226.5 91 L 226.5 1 Z" fill="rgb(255, 255, 255)"
                  stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/>
            <path d="M 265.5 1 L 265.5 31 L 295.5 31 Z" fill-opacity="0.05" fill="#000000" stroke="none"
                  pointer-events="all"/>
            <path d="M 265.5 1 L 265.5 31 L 295.5 31" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3"
                  stroke-miterlimit="10" pointer-events="all"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject pointer-events="none" width="100%" height="100%"
                                   requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"
                                   style="overflow: visible; text-align: left;">
                        <div xmlns="http://www.w3.org/1999/xhtml"
                             style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 67px; height: 1px; padding-top: 46px; margin-left: 228px;">
                            <div data-drawio-colors="color: rgb(0, 0, 0); "
                                 style="box-sizing: border-box; font-size: 0px; text-align: center;">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, Avenir, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, Helvetica, Arial, &quot;sans-serif&quot;; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                    <br/>
                                    <br/>RegExps<br/>Dictionary<br/>JSON
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="261" y="50" fill="rgb(0, 0, 0)"
                          font-family="&quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, &quot;Avenir&quot;, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, &quot;Helvetica&quot;, &quot;Arial&quot;, &quot;sans-serif&quot;"
                          font-size="12px" text-anchor="middle">RegExps...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 1 141 L 40 141 L 70 171 L 70 231 L 1 231 L 1 141 Z" fill="rgb(255, 255, 255)"
                  stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/>
            <path d="M 40 141 L 40 171 L 70 171 Z" fill-opacity="0.05" fill="#000000" stroke="none"
                  pointer-events="all"/>
            <path d="M 40 141 L 40 171 L 70 171" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3"
                  stroke-miterlimit="10" pointer-events="all"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject pointer-events="none" width="100%" height="100%"
                                   requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"
                                   style="overflow: visible; text-align: left;">
                        <div xmlns="http://www.w3.org/1999/xhtml"
                             style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 67px; height: 1px; padding-top: 186px; margin-left: 2px;">
                            <div data-drawio-colors="color: rgb(0, 0, 0); "
                                 style="box-sizing: border-box; font-size: 0px; text-align: center;">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, Avenir, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, Helvetica, Arial, &quot;sans-serif&quot;; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                    <br/>Log<br/>File
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="36" y="190" fill="rgb(0, 0, 0)"
                          font-family="&quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, &quot;Avenir&quot;, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, &quot;Helvetica&quot;, &quot;Arial&quot;, &quot;sans-serif&quot;"
                          font-size="12px" text-anchor="middle">Log...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="151" y="151" width="220" height="80" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)"
                  stroke-width="3" pointer-events="all"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject pointer-events="none" width="100%" height="100%"
                                   requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"
                                   style="overflow: visible; text-align: left;">
                        <div xmlns="http://www.w3.org/1999/xhtml"
                             style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 218px; height: 1px; padding-top: 191px; margin-left: 153px;">
                            <div data-drawio-colors="color: #99004D; "
                                 style="box-sizing: border-box; font-size: 0px; text-align: left;">
                                <div style="display: inline-block; font-size: 14px; font-family: &quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, Avenir, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, Helvetica, Arial, &quot;sans-serif&quot;; color: rgb(153, 0, 77); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">
                                       Regular Expressions<br/>   Method
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="153" y="195" fill="#99004D"
                          font-family="&quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, &quot;Avenir&quot;, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, &quot;Helvetica&quot;, &quot;Arial&quot;, &quot;sans-serif&quot;"
                          font-size="14px" font-weight="bold">Regular Expressions...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="301" y="160" width="62.5" height="60" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 336.51 192.3 C 336.06 192.3 335.73 191.86 335.73 191.56 C 335.73 191.09 336.06 190.82 336.51 190.82 C 336.83 190.82 337.28 191.09 337.28 191.56 C 337.28 191.86 336.83 192.3 336.51 192.3 Z M 335.88 187.5 C 335.88 187.2 336.06 187.07 336.51 187.07 C 336.83 187.07 337.14 187.2 337.14 187.5 C 337.14 189.6 337.14 189.6 337.14 189.6 C 337.14 189.74 336.83 190.04 336.51 190.04 C 336.06 190.04 335.88 189.74 335.88 189.6 L 335.88 187.5 Z M 342.6 192.3 C 342.43 192.13 342.43 192.13 342.43 192.13 C 337.28 184.19 337.28 184.19 337.28 184.19 C 337.14 184.06 336.83 183.75 336.51 183.75 C 336.06 183.75 335.73 184.06 335.73 184.36 C 330.42 192.13 330.42 192.13 330.42 192.13 C 330.24 192.3 330.1 192.61 330.1 192.75 C 330.1 193.05 330.56 193.35 331.19 193.35 C 341.83 193.35 341.83 193.35 341.83 193.35 C 342.29 193.35 342.6 193.05 342.6 192.75 C 342.6 192.61 342.6 192.3 342.6 192.3 Z M 342.12 195.25 C 340.71 196.6 338.53 197.34 336.48 197.34 C 334.3 197.34 332.25 196.6 330.85 195.25 C 329.43 193.9 328.52 191.94 328.52 190.01 C 328.52 188.19 329.43 186.26 330.85 184.91 C 332.25 183.56 334.3 182.78 336.48 182.78 C 338.53 182.78 340.71 183.56 342.12 184.91 C 345.21 187.75 345.21 192.41 342.12 195.25 Z M 355.23 204.1 C 346.49 195.86 346.49 195.86 346.49 195.86 C 349.3 191.63 348.81 186.09 344.9 182.34 C 342.58 180.38 339.44 179.2 336.48 179.2 C 333.52 179.2 330.53 180.38 328.2 182.34 C 323.52 186.53 323.52 193.46 328.2 197.65 C 330.53 199.74 333.52 200.78 336.48 200.78 C 338.67 200.78 340.85 200.35 342.72 199.3 C 351.63 207.41 351.63 207.41 351.63 207.41 C 352.26 207.99 353.5 207.99 354.31 207.41 C 355.23 206.5 355.23 206.5 355.23 206.5 C 356 205.76 356 204.53 355.23 204.1 Z M 320.23 206.04 C 320.06 205.91 319.6 205.6 319.46 205.44 C 319.6 205.6 320.06 205.91 320.23 206.04 Z M 312.24 194.19 C 312.24 194.05 312.24 194.05 312.1 193.74 C 312.24 194.05 312.24 194.05 312.24 194.19 Z M 312.24 185.81 C 312.24 185.94 312.24 186.11 312.1 186.24 C 312.24 186.11 312.24 185.94 312.24 185.81 Z M 314.28 180.53 C 314.45 180.1 314.59 179.97 314.73 179.66 C 314.59 179.97 314.45 180.1 314.28 180.53 Z M 314.91 200.81 C 314.73 200.5 314.59 200.2 314.59 199.89 C 314.59 200.2 314.73 200.5 314.91 200.81 Z M 320.37 174.38 C 321.15 174.38 321.78 174.69 321.78 175.44 C 321.78 176.04 321.33 176.49 320.55 176.49 C 319.46 176.49 319.46 176.49 319.46 176.49 C 319.46 174.69 319.46 174.69 319.46 174.69 C 319.29 174.69 319.15 174.86 318.97 174.99 C 319.46 174.56 320.06 174.09 320.55 173.81 C 320.23 173.95 320.06 174.09 319.74 174.38 L 320.37 174.38 Z M 320.55 180.1 C 319.46 180.1 319.46 180.1 319.46 180.1 C 319.46 177.7 319.46 177.7 319.46 177.7 C 320.69 177.7 320.69 177.7 320.69 177.7 C 321.64 177.7 321.96 178.13 321.96 178.91 C 321.96 179.79 321.64 180.1 320.55 180.1 Z M 332.43 209.79 C 327.88 209.79 323.83 208.44 320.55 206.22 C 320.69 206.51 321.15 206.65 321.47 206.78 C 321.47 200.06 321.47 200.06 321.47 200.06 C 323.97 200.06 323.97 200.06 323.97 200.06 C 323.97 198.54 323.97 198.54 323.97 198.54 C 316.96 198.54 316.96 198.54 316.96 198.54 C 316.96 200.06 316.96 200.06 316.96 200.06 C 319.46 200.06 319.46 200.06 319.46 200.06 C 319.46 205.44 319.46 205.44 319.46 205.44 C 317.73 204.09 316.32 202.47 314.91 200.81 C 315.05 200.81 315.05 200.81 315.05 200.81 C 314.91 200.06 314.59 199.46 314.14 199.01 C 314.14 199.46 314.28 199.59 314.59 199.89 C 313.51 198.24 312.55 196.14 312.24 194.19 C 313.65 194.19 313.65 194.19 313.65 194.19 C 313.65 190.6 313.65 190.6 313.65 190.6 C 316.96 194.19 316.96 194.19 316.96 194.19 C 319.46 194.19 319.46 194.19 319.46 194.19 C 315.38 189.69 315.38 189.69 315.38 189.69 C 319.15 185.81 319.15 185.81 319.15 185.81 C 316.64 185.81 316.64 185.81 316.64 185.81 C 313.65 189.38 313.65 189.38 313.65 189.38 C 313.65 185.81 313.65 185.81 313.65 185.81 C 312.24 185.81 312.24 185.81 312.24 185.81 C 312.55 183.99 313.37 182.19 314.28 180.53 C 314.14 180.84 313.96 181.14 313.82 181.45 C 315.55 181.45 315.55 181.45 315.55 181.45 C 314.73 179.66 314.73 179.66 314.73 179.66 C 316.01 177.84 317.24 176.49 318.82 175.16 C 318.33 175.44 317.87 175.91 317.42 176.49 C 317.42 181.45 317.42 181.45 317.42 181.45 C 320.55 181.45 320.55 181.45 320.55 181.45 C 323.06 181.45 324.14 180.53 324.14 179.05 C 324.14 177.84 323.34 177.26 322.24 176.96 C 323.06 176.78 323.65 176.22 323.65 175.3 C 323.65 174.09 323.06 173.51 321.47 173.21 C 321.15 173.34 320.83 173.64 320.55 173.81 C 323.97 171.55 327.88 170.34 332.43 170.34 C 343.67 170.34 352.89 179.05 352.89 189.99 C 352.89 192.84 352.43 195.53 351.17 197.94 C 353.53 200.2 353.53 200.2 353.53 200.2 C 355.22 197.19 356.03 193.74 356.03 189.99 C 356.03 177.56 345.39 167.19 332.43 167.19 C 326.01 167.19 320.06 169.59 315.55 173.95 C 311.14 178.31 308.5 183.99 308.5 189.99 C 308.5 202.6 319.29 212.8 332.43 212.8 C 338.52 212.8 344.12 210.53 348.35 206.78 C 346.02 204.69 346.02 204.69 346.02 204.69 C 342.43 207.84 337.56 209.79 332.43 209.79 Z M 327.73 199.15 C 327.73 199.15 327.73 199.15 327.73 199.15 C 327.41 198.84 327.1 198.71 326.96 198.4 C 326 198.4 326 198.4 326 198.4 C 326 203.2 326 203.2 326 203.2 C 326 204.69 327.41 205.6 329.92 205.6 C 332.24 205.6 333.51 204.69 333.51 203.2 C 333.51 201.85 333.51 201.85 333.51 201.85 C 332.88 201.72 332.24 201.54 331.64 201.24 C 331.64 203.2 331.64 203.2 331.64 203.2 C 331.64 204.12 331.01 204.55 329.92 204.55 C 328.83 204.55 328.19 204.12 328.19 203.2 C 328.19 199.45 328.19 199.45 328.19 199.45 C 328.05 199.45 327.87 199.31 327.73 199.15 Z M 341.01 200.8 C 340.38 201.11 339.74 201.24 339.14 201.41 C 338.51 204.12 338.51 204.12 338.51 204.12 C 337.88 201.72 337.88 201.72 337.88 201.72 C 337.24 201.85 336.96 201.85 336.47 201.85 C 336.01 201.85 336.01 201.85 336.01 201.85 C 337.24 205.6 337.24 205.6 337.24 205.6 C 339.6 205.6 339.6 205.6 339.6 205.6 L 341.01 200.8 Z M 343.34 179.2 C 343.51 178.76 343.51 177.99 343.51 177.24 C 343.51 173.79 342.1 172 339.29 172 C 336.01 172 336.01 172 336.01 172 C 336.01 177.24 336.01 177.24 336.01 177.24 C 336.78 177.24 336.78 177.24 336.78 177.24 C 337.1 177.24 337.56 177.24 338.05 177.24 C 338.05 173.79 338.05 173.79 338.05 173.79 C 339.29 173.79 339.29 173.79 339.29 173.79 C 340.69 173.79 341.47 174.84 341.47 177.24 C 341.47 177.54 341.47 177.85 341.47 178.28 C 342.1 178.59 342.73 178.89 343.34 179.2 Z M 329.92 173.66 C 330.87 173.66 331.46 174.24 331.64 175.59 C 333.51 175.59 333.51 175.59 333.51 175.59 C 333.19 173.19 331.78 172 329.92 172 C 327.55 172 326 173.97 326 177.24 C 326 179.34 326.64 180.86 327.55 181.6 C 328.05 181.16 328.37 180.69 328.83 180.38 C 328.37 179.81 328.05 178.76 328.05 177.24 C 328.05 174.71 328.83 173.66 329.92 173.66 Z M 321.01 186.4 C 321.01 193.6 321.01 193.6 321.01 193.6 C 322.56 193.6 322.56 193.6 322.56 193.6 C 322.56 188.63 322.56 188.63 322.56 188.63 C 323.19 189.85 323.19 189.85 323.19 189.85 C 323.37 189.11 323.51 188.37 323.51 187.59 C 323.05 186.4 323.05 186.4 323.05 186.4 L 321.01 186.4 Z M 301 190 C 301 173.51 315.05 160 332.25 160 C 349.62 160 363.5 173.51 363.5 190 C 363.5 206.66 349.62 220 332.25 220 C 315.05 220 301 206.66 301 190 Z"
                  fill="#99004d" stroke="none" pointer-events="all"/>
            <ellipse cx="332.25" cy="190" rx="30.625" ry="29.4" fill="#ffffff" stroke="none" pointer-events="all"/>
            <rect x="301" y="160" width="62.5" height="60" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 336.51 192.3 C 336.06 192.3 335.73 191.86 335.73 191.56 C 335.73 191.09 336.06 190.82 336.51 190.82 C 336.83 190.82 337.28 191.09 337.28 191.56 C 337.28 191.86 336.83 192.3 336.51 192.3 Z M 335.88 187.5 C 335.88 187.2 336.06 187.07 336.51 187.07 C 336.83 187.07 337.14 187.2 337.14 187.5 C 337.14 189.6 337.14 189.6 337.14 189.6 C 337.14 189.74 336.83 190.04 336.51 190.04 C 336.06 190.04 335.88 189.74 335.88 189.6 L 335.88 187.5 Z M 342.6 192.3 C 342.43 192.13 342.43 192.13 342.43 192.13 C 337.28 184.19 337.28 184.19 337.28 184.19 C 337.14 184.06 336.83 183.75 336.51 183.75 C 336.06 183.75 335.73 184.06 335.73 184.36 C 330.42 192.13 330.42 192.13 330.42 192.13 C 330.24 192.3 330.1 192.61 330.1 192.75 C 330.1 193.05 330.56 193.35 331.19 193.35 C 341.83 193.35 341.83 193.35 341.83 193.35 C 342.29 193.35 342.6 193.05 342.6 192.75 C 342.6 192.61 342.6 192.3 342.6 192.3 Z M 342.12 195.25 C 340.71 196.6 338.53 197.34 336.48 197.34 C 334.3 197.34 332.25 196.6 330.85 195.25 C 329.43 193.9 328.52 191.94 328.52 190.01 C 328.52 188.19 329.43 186.26 330.85 184.91 C 332.25 183.56 334.3 182.78 336.48 182.78 C 338.53 182.78 340.71 183.56 342.12 184.91 C 345.21 187.75 345.21 192.41 342.12 195.25 Z M 355.23 204.1 C 346.49 195.86 346.49 195.86 346.49 195.86 C 349.3 191.63 348.81 186.09 344.9 182.34 C 342.58 180.38 339.44 179.2 336.48 179.2 C 333.52 179.2 330.53 180.38 328.2 182.34 C 323.52 186.53 323.52 193.46 328.2 197.65 C 330.53 199.74 333.52 200.78 336.48 200.78 C 338.67 200.78 340.85 200.35 342.72 199.3 C 351.63 207.41 351.63 207.41 351.63 207.41 C 352.26 207.99 353.5 207.99 354.31 207.41 C 355.23 206.5 355.23 206.5 355.23 206.5 C 356 205.76 356 204.53 355.23 204.1 Z M 320.23 206.04 C 320.06 205.91 319.6 205.6 319.46 205.44 C 319.6 205.6 320.06 205.91 320.23 206.04 Z M 312.24 194.19 C 312.24 194.05 312.24 194.05 312.1 193.74 C 312.24 194.05 312.24 194.05 312.24 194.19 Z M 312.24 185.81 C 312.24 185.94 312.24 186.11 312.1 186.24 C 312.24 186.11 312.24 185.94 312.24 185.81 Z M 314.28 180.53 C 314.45 180.1 314.59 179.97 314.73 179.66 C 314.59 179.97 314.45 180.1 314.28 180.53 Z M 314.91 200.81 C 314.73 200.5 314.59 200.2 314.59 199.89 C 314.59 200.2 314.73 200.5 314.91 200.81 Z M 320.37 174.38 C 321.15 174.38 321.78 174.69 321.78 175.44 C 321.78 176.04 321.33 176.49 320.55 176.49 C 319.46 176.49 319.46 176.49 319.46 176.49 C 319.46 174.69 319.46 174.69 319.46 174.69 C 319.29 174.69 319.15 174.86 318.97 174.99 C 319.46 174.56 320.06 174.09 320.55 173.81 C 320.23 173.95 320.06 174.09 319.74 174.38 L 320.37 174.38 Z M 320.55 180.1 C 319.46 180.1 319.46 180.1 319.46 180.1 C 319.46 177.7 319.46 177.7 319.46 177.7 C 320.69 177.7 320.69 177.7 320.69 177.7 C 321.64 177.7 321.96 178.13 321.96 178.91 C 321.96 179.79 321.64 180.1 320.55 180.1 Z M 332.43 209.79 C 327.88 209.79 323.83 208.44 320.55 206.22 C 320.69 206.51 321.15 206.65 321.47 206.78 C 321.47 200.06 321.47 200.06 321.47 200.06 C 323.97 200.06 323.97 200.06 323.97 200.06 C 323.97 198.54 323.97 198.54 323.97 198.54 C 316.96 198.54 316.96 198.54 316.96 198.54 C 316.96 200.06 316.96 200.06 316.96 200.06 C 319.46 200.06 319.46 200.06 319.46 200.06 C 319.46 205.44 319.46 205.44 319.46 205.44 C 317.73 204.09 316.32 202.47 314.91 200.81 C 315.05 200.81 315.05 200.81 315.05 200.81 C 314.91 200.06 314.59 199.46 314.14 199.01 C 314.14 199.46 314.28 199.59 314.59 199.89 C 313.51 198.24 312.55 196.14 312.24 194.19 C 313.65 194.19 313.65 194.19 313.65 194.19 C 313.65 190.6 313.65 190.6 313.65 190.6 C 316.96 194.19 316.96 194.19 316.96 194.19 C 319.46 194.19 319.46 194.19 319.46 194.19 C 315.38 189.69 315.38 189.69 315.38 189.69 C 319.15 185.81 319.15 185.81 319.15 185.81 C 316.64 185.81 316.64 185.81 316.64 185.81 C 313.65 189.38 313.65 189.38 313.65 189.38 C 313.65 185.81 313.65 185.81 313.65 185.81 C 312.24 185.81 312.24 185.81 312.24 185.81 C 312.55 183.99 313.37 182.19 314.28 180.53 C 314.14 180.84 313.96 181.14 313.82 181.45 C 315.55 181.45 315.55 181.45 315.55 181.45 C 314.73 179.66 314.73 179.66 314.73 179.66 C 316.01 177.84 317.24 176.49 318.82 175.16 C 318.33 175.44 317.87 175.91 317.42 176.49 C 317.42 181.45 317.42 181.45 317.42 181.45 C 320.55 181.45 320.55 181.45 320.55 181.45 C 323.06 181.45 324.14 180.53 324.14 179.05 C 324.14 177.84 323.34 177.26 322.24 176.96 C 323.06 176.78 323.65 176.22 323.65 175.3 C 323.65 174.09 323.06 173.51 321.47 173.21 C 321.15 173.34 320.83 173.64 320.55 173.81 C 323.97 171.55 327.88 170.34 332.43 170.34 C 343.67 170.34 352.89 179.05 352.89 189.99 C 352.89 192.84 352.43 195.53 351.17 197.94 C 353.53 200.2 353.53 200.2 353.53 200.2 C 355.22 197.19 356.03 193.74 356.03 189.99 C 356.03 177.56 345.39 167.19 332.43 167.19 C 326.01 167.19 320.06 169.59 315.55 173.95 C 311.14 178.31 308.5 183.99 308.5 189.99 C 308.5 202.6 319.29 212.8 332.43 212.8 C 338.52 212.8 344.12 210.53 348.35 206.78 C 346.02 204.69 346.02 204.69 346.02 204.69 C 342.43 207.84 337.56 209.79 332.43 209.79 Z M 327.73 199.15 C 327.73 199.15 327.73 199.15 327.73 199.15 C 327.41 198.84 327.1 198.71 326.96 198.4 C 326 198.4 326 198.4 326 198.4 C 326 203.2 326 203.2 326 203.2 C 326 204.69 327.41 205.6 329.92 205.6 C 332.24 205.6 333.51 204.69 333.51 203.2 C 333.51 201.85 333.51 201.85 333.51 201.85 C 332.88 201.72 332.24 201.54 331.64 201.24 C 331.64 203.2 331.64 203.2 331.64 203.2 C 331.64 204.12 331.01 204.55 329.92 204.55 C 328.83 204.55 328.19 204.12 328.19 203.2 C 328.19 199.45 328.19 199.45 328.19 199.45 C 328.05 199.45 327.87 199.31 327.73 199.15 Z M 341.01 200.8 C 340.38 201.11 339.74 201.24 339.14 201.41 C 338.51 204.12 338.51 204.12 338.51 204.12 C 337.88 201.72 337.88 201.72 337.88 201.72 C 337.24 201.85 336.96 201.85 336.47 201.85 C 336.01 201.85 336.01 201.85 336.01 201.85 C 337.24 205.6 337.24 205.6 337.24 205.6 C 339.6 205.6 339.6 205.6 339.6 205.6 L 341.01 200.8 Z M 343.34 179.2 C 343.51 178.76 343.51 177.99 343.51 177.24 C 343.51 173.79 342.1 172 339.29 172 C 336.01 172 336.01 172 336.01 172 C 336.01 177.24 336.01 177.24 336.01 177.24 C 336.78 177.24 336.78 177.24 336.78 177.24 C 337.1 177.24 337.56 177.24 338.05 177.24 C 338.05 173.79 338.05 173.79 338.05 173.79 C 339.29 173.79 339.29 173.79 339.29 173.79 C 340.69 173.79 341.47 174.84 341.47 177.24 C 341.47 177.54 341.47 177.85 341.47 178.28 C 342.1 178.59 342.73 178.89 343.34 179.2 Z M 329.92 173.66 C 330.87 173.66 331.46 174.24 331.64 175.59 C 333.51 175.59 333.51 175.59 333.51 175.59 C 333.19 173.19 331.78 172 329.92 172 C 327.55 172 326 173.97 326 177.24 C 326 179.34 326.64 180.86 327.55 181.6 C 328.05 181.16 328.37 180.69 328.83 180.38 C 328.37 179.81 328.05 178.76 328.05 177.24 C 328.05 174.71 328.83 173.66 329.92 173.66 Z M 321.01 186.4 C 321.01 193.6 321.01 193.6 321.01 193.6 C 322.56 193.6 322.56 193.6 322.56 193.6 C 322.56 188.63 322.56 188.63 322.56 188.63 C 323.19 189.85 323.19 189.85 323.19 189.85 C 323.37 189.11 323.51 188.37 323.51 187.59 C 323.05 186.4 323.05 186.4 323.05 186.4 L 321.01 186.4 Z M 301 190 C 301 173.51 315.05 160 332.25 160 C 349.62 160 363.5 173.51 363.5 190 C 363.5 206.66 349.62 220 332.25 220 C 315.05 220 301 206.66 301 190 Z"
                  fill="#99004d" stroke="none" pointer-events="all"/>
        </g>
        <g>
            <path d="M 71 191 L 140.89 190.68" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10"
                  pointer-events="stroke"/>
            <path d="M 147.64 190.65 L 138.66 195.19 L 140.89 190.68 L 138.62 186.19 Z" fill="rgb(0, 0, 0)"
                  stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject pointer-events="none" width="100%" height="100%"
                                   requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"
                                   style="overflow: visible; text-align: left;">
                        <div xmlns="http://www.w3.org/1999/xhtml"
                             style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 181px; margin-left: 93px;">
                            <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); "
                                 style="box-sizing: border-box; font-size: 0px; text-align: left;">
                                <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                    Input
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="93" y="185" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Input</text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 613 151 C 631.72 174.38 631.72 207.62 613 231 L 432 231 C 413.28 207.62 413.28 174.38 432 151 Z"
                  fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10"
                  pointer-events="all"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject pointer-events="none" width="100%" height="100%"
                                   requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"
                                   style="overflow: visible; text-align: left;">
                        <div xmlns="http://www.w3.org/1999/xhtml"
                             style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 211px; height: 1px; padding-top: 191px; margin-left: 418px;">
                            <div data-drawio-colors="color: rgb(0, 0, 0); "
                                 style="box-sizing: border-box; font-size: 0px; text-align: left;">
                                <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                          precompile RegExp dictionary<br/>      for each log line<br/>         for each
                                    RegExp in dictionary<br/>            search RegExp in log line
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="418" y="195" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">precompile RegExp
                        dictionary...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 261 91.37 L 261 140.9" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10"
                  pointer-events="stroke"/>
            <path d="M 261 147.65 L 256.5 138.65 L 261 140.9 L 265.5 138.65 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)"
                  stroke-width="3" stroke-miterlimit="10" pointer-events="all"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject pointer-events="none" width="100%" height="100%"
                                   requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"
                                   style="overflow: visible; text-align: left;">
                        <div xmlns="http://www.w3.org/1999/xhtml"
                             style="display: flex; align-items: unsafe center; justify-content: unsafe center; writing-mode: vertical-rl; width: 1px; height: 1px; padding-top: 112px; margin-left: 272px;">
                            <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); "
                                 style="box-sizing: border-box; font-size: 0px; text-align: center;">
                                <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                    Load
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="272" y="115" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px"
                          text-anchor="middle">Load
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 371 191 L 419.62 191.16" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3"
                  stroke-miterlimit="10" pointer-events="stroke"/>
        </g>
        <g>
            <path d="M 629 190.04 L 698.89 189.72" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3"
                  stroke-miterlimit="10" pointer-events="stroke"/>
            <path d="M 705.64 189.69 L 696.66 194.23 L 698.89 189.72 L 696.62 185.23 Z" fill="rgb(0, 0, 0)"
                  stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject pointer-events="none" width="100%" height="100%"
                                   requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"
                                   style="overflow: visible; text-align: left;">
                        <div xmlns="http://www.w3.org/1999/xhtml"
                             style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 180px; margin-left: 643px;">
                            <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); "
                                 style="box-sizing: border-box; font-size: 0px; text-align: left;">
                                <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                    Output
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="643" y="184" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Output</text>
                </switch>
            </g>
        </g>
    </g>
</svg>
