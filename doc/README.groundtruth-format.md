This is how the ground truth data should be structured.

The following file naming pattern should be used:

<logfile>                      The original log file

<logfile>.context.json A json file containing context information that will be provided to the log analyzer user command
handling and AI prompts. If no file matches the logfile, the tool will traverse up the folder structure and append
default_context.json untl it reaches the workspace directory.

<logfile>.groundtruth.json A ground truth json file for the corresponding log file. If a logfile does not have matching
.groundtruth.json files it will be skipped from training and validation.

Format of .context.json files:
TBD
Supports inheritance.

Format of .groundtruth.json files:
{
"exit_code": 0, // (mandatory) The overall exit code of the build / test execution. 0 means success, non-zero is failed.
"root_causes": [ // (optional) An array with all root causes that the log file contains. Can be empty or skipped for
successful executions
{
"name": "sporadic repo fetch error" // A unique, short sentence used as key for this specific root cause. Usually ends
with " error", " problem" or " bug". It should be understandable for humans and natural to use in human dialogs. You
should be able to say: "Aha! it was that damn .... again!"
"description": "Caused when the repo tool internally fails to perform the fetch operation. The repo command finishes
success" // A human description of the reason for the error, why it happens. Do not write the solution here, that should
be written in the 'solution' value. Example: 'Occurs when repo tool fails during its fetch operation. Unfortunately it
does not print an error when it occurs, but hides it and returns successfully. The result is varied, for example
compilation errors due to missing .h files.'",
"solution": "No known solution. Try to reduce the size of files in your commit to minimize the risk of this error." //
Description of the solution, basically what you would recommend your developer colleague to do to resolve the problem.
"
error_lines": [  // Array with lines where the root cause is to be found. The format is an array of single line numbers and/or ranges.
443,
462,
481
],
"
hint_lines": [ // (optional) Array with line numbers and ranges where the log contains lines that hint of a root cause elsewhere
"9904-9909",
"9916-9921",
"9924-9929"
]
}
],
"success_lines": [] // (optional) Used only for successful logs, e.g. when exit code is 0. An array with lines that
proves that the execution was successful.
}

Example .groundtruth.json for a successful execution:
{

"exit_code": 0,
"root_causes": [],
"success_lines": [
85431
]
}
