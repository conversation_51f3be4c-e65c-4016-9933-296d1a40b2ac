<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" version="1.1" width="1084px"
     height="599px" viewBox="-0.5 -0.5 1084 599">
    <defs>
        <style type="text/css">@import url(https://fonts.googleapis.com/css2?family=Architects+Daughter:wght@400;500);&#xa;</style>
    </defs>
    <g>
        <g>
            <path d="M 360 254 L 400 254 L 420 254 L 430 244 L 434.16 214.65" fill="none" stroke="rgb(0, 0, 0)"
                  stroke-width="2" stroke-miterlimit="10" stroke-dasharray="2 6" pointer-events="stroke"/>
        </g>
        <g>
            <rect x="80" y="207" width="200" height="100" rx="15" ry="15" fill="rgb(255, 255, 255)"
                  stroke="rgb(0, 0, 0)" stroke-width="3" pointer-events="all"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject pointer-events="none" width="100%" height="100%"
                                   requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"
                                   style="overflow: visible; text-align: left;">
                        <div xmlns="http://www.w3.org/1999/xhtml"
                             style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 198px; height: 1px; padding-top: 257px; margin-left: 81px;">
                            <div data-drawio-colors="color: rgb(0, 0, 0); "
                                 style="box-sizing: border-box; font-size: 0px; text-align: center;">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, Avenir, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, Helvetica, Arial, &quot;sans-serif&quot;; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                    <span style="font-size: 18px;">Analysis + </span>
                                    <div>
                                        <span style="font-size: 18px;">Logfile lines</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="180" y="261" fill="rgb(0, 0, 0)"
                          font-family="&quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, &quot;Avenir&quot;, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, &quot;Helvetica&quot;, &quot;Arial&quot;, &quot;sans-serif&quot;"
                          font-size="12px" text-anchor="middle">Analysis +...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 280.9 255.67 L 336.9 255.95" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3"
                  stroke-miterlimit="10" pointer-events="stroke"/>
            <path d="M 343.65 255.98 L 334.62 260.44 L 336.9 255.95 L 334.67 251.44 Z" fill="rgb(0, 0, 0)"
                  stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/>
        </g>
        <g>
            <rect x="330" y="212" width="210" height="130" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 451.4 333.82 C 453.43 333.82 454.84 332.99 454.44 332.35 L 450.43 326.17 C 449.92 325.23 449.2 325.04 448.01 325.04 L 421.99 325.04 C 420.73 325.04 420.08 325.27 419.46 326.21 L 415.74 332.32 C 415.02 333.3 417.19 333.82 418.74 333.82 Z M 512.18 313.74 L 512.18 223.3 L 357.71 223.3 L 357.71 313.74 Z M 340.88 342 C 336.76 341.96 333.07 340.53 331.52 338.31 C 330 336.12 330.61 333.94 331.92 332.43 L 346.87 316.57 L 346.87 222.81 C 346.87 218.1 350.92 212 357.64 212 L 512.21 212 C 517.45 212 523.02 216.37 523.02 223.42 L 523.02 316.57 L 538.12 332.58 C 539.39 334.09 540 336.08 538.48 338.23 C 536.5 340.94 532.74 341.81 529.2 342 Z"
                  fill="#647687" stroke="#000000" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/>
        </g>
        <g>
            <rect x="573" y="52" width="200" height="100" rx="15" ry="15" fill="rgb(255, 255, 255)"
                  stroke="rgb(0, 0, 0)" stroke-width="3" pointer-events="all"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject pointer-events="none" width="100%" height="100%"
                                   requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"
                                   style="overflow: visible; text-align: left;">
                        <div xmlns="http://www.w3.org/1999/xhtml"
                             style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 198px; height: 1px; padding-top: 102px; margin-left: 574px;">
                            <div data-drawio-colors="color: rgb(0, 0, 0); "
                                 style="box-sizing: border-box; font-size: 0px; text-align: center;">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, Avenir, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, Helvetica, Arial, &quot;sans-serif&quot;; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                    <font style="font-size: 18px;">Search for similar </font>
                                    <div style="font-size: 18px;">
                                        <font style="font-size: 18px;">Information</font>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="673" y="106" fill="rgb(0, 0, 0)"
                          font-family="&quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, &quot;Avenir&quot;, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, &quot;Helvetica&quot;, &quot;Arial&quot;, &quot;sans-serif&quot;"
                          font-size="12px" text-anchor="middle">Search for similar...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 435 212 L 435 102 L 559.9 102" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3"
                  stroke-miterlimit="10" pointer-events="stroke"/>
            <path d="M 566.65 102 L 557.65 106.5 L 559.9 102 L 557.65 97.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)"
                  stroke-width="3" stroke-miterlimit="10" pointer-events="all"/>
        </g>
        <g>
            <path d="M 990 272 L 900 272 L 900 2 L 990 2" fill="none" stroke="rgb(0, 0, 0)" stroke-width="5"
                  stroke-miterlimit="10" pointer-events="stroke"/>
        </g>
        <g>
            <path d="M 774 101.47 L 853.9 101.91" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3"
                  stroke-miterlimit="10" pointer-events="stroke"/>
            <path d="M 860.65 101.95 L 851.62 106.4 L 853.9 101.91 L 851.67 97.4 Z" fill="rgb(0, 0, 0)"
                  stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/>
        </g>
        <g>
            <path d="M 870 101.88 L 900 101.88" fill="none" stroke="rgb(0, 0, 0)" stroke-width="5"
                  stroke-miterlimit="10" pointer-events="stroke"/>
        </g>
        <g>
            <path d="M 673 152 L 673 252 L 533.1 252" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3"
                  stroke-miterlimit="10" pointer-events="stroke"/>
            <path d="M 526.35 252 L 535.35 247.5 L 533.1 252 L 535.35 256.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)"
                  stroke-width="3" stroke-miterlimit="10" pointer-events="all"/>
        </g>
        <g>
            <rect x="716" y="537" width="220" height="30" fill="none" stroke="none" pointer-events="all"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject pointer-events="none" width="100%" height="100%"
                                   requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"
                                   style="overflow: visible; text-align: left;">
                        <div xmlns="http://www.w3.org/1999/xhtml"
                             style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 218px; height: 1px; padding-top: 552px; margin-left: 717px;">
                            <div data-drawio-colors="color: rgb(0, 0, 0); "
                                 style="box-sizing: border-box; font-size: 0px; text-align: center;">
                                <div style="display: inline-block; font-size: 18px; font-family: &quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, Avenir, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, Helvetica, Arial, &quot;sans-serif&quot;; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                    LLM EP
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="826" y="557" fill="rgb(0, 0, 0)"
                          font-family="&quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, &quot;Avenir&quot;, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, &quot;Helvetica&quot;, &quot;Arial&quot;, &quot;sans-serif&quot;"
                          font-size="18px" text-anchor="middle">LLM EP
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="768" y="402" width="118" height="118" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 842.97 476.32 C 842.97 477.62 841.91 478.68 840.62 478.68 C 839.32 478.68 838.26 477.62 838.26 476.32 C 838.26 475.03 839.32 473.98 840.62 473.98 C 841.91 473.98 842.97 475.03 842.97 476.32 Z M 850.65 428.51 C 849.35 428.51 848.3 427.46 848.3 426.16 C 848.3 424.86 849.35 423.81 850.65 423.81 C 851.94 423.81 853 424.86 853 426.16 C 853 427.46 851.94 428.51 850.65 428.51 Z M 841.53 451.96 C 841.53 453.26 840.48 454.31 839.18 454.31 C 837.89 454.31 836.83 453.26 836.83 451.96 C 836.83 450.66 837.89 449.61 839.18 449.61 C 840.48 449.61 841.53 450.66 841.53 451.96 Z M 854.03 493.52 C 854.03 492.23 855.08 491.17 856.38 491.17 C 857.68 491.17 858.73 492.23 858.73 493.52 C 858.73 494.82 857.68 495.87 856.38 495.87 C 855.08 495.87 854.03 494.82 854.03 493.52 Z M 865.82 480.25 C 864.98 480.72 864.46 481.61 864.46 482.57 L 864.46 499.08 L 845.43 510.33 L 845.43 496.91 L 849.52 496.91 C 850.78 499.44 853.36 501.21 856.38 501.21 C 860.62 501.21 864.06 497.76 864.06 493.52 C 864.06 489.29 860.62 485.84 856.38 485.84 C 852.82 485.84 849.85 488.29 848.98 491.58 L 842.77 491.58 C 841.29 491.58 840.1 492.77 840.1 494.24 L 840.1 513.48 L 839.02 514.12 L 829.67 508.7 L 829.67 439.58 L 837.23 439.58 L 837.23 444.56 C 833.94 445.43 831.5 448.4 831.5 451.96 C 831.5 456.2 834.95 459.64 839.18 459.64 C 843.42 459.64 846.87 456.2 846.87 451.96 C 846.87 448.94 845.1 446.35 842.57 445.1 L 842.57 439.58 L 851.36 439.58 C 852.84 439.58 854.03 438.38 854.03 436.91 L 854.03 433.02 C 856.57 431.77 858.33 429.18 858.33 426.16 C 858.33 421.93 854.88 418.48 850.65 418.48 C 846.41 418.48 842.97 421.93 842.97 426.16 C 842.97 429.72 845.41 432.69 848.7 433.56 L 848.7 434.24 L 829.67 434.24 L 829.67 414.94 L 841.39 407.92 L 865.9 422.86 L 865.9 439.29 C 865.9 440.25 866.41 441.14 867.25 441.61 L 880.23 448.97 L 880.23 457.17 L 855.67 457.17 C 854.19 457.17 853 458.37 853 459.84 L 853 472.94 L 847.48 472.94 C 846.22 470.41 843.63 468.64 840.62 468.64 C 836.38 468.64 832.93 472.09 832.93 476.32 C 832.93 480.56 836.38 484.01 840.62 484.01 C 844.18 484.01 847.15 481.56 848.02 478.27 L 855.67 478.27 C 857.14 478.27 858.33 477.08 858.33 475.61 L 858.33 462.51 L 880.23 462.51 L 880.23 472.14 Z M 809.08 472.54 C 810.38 472.54 811.43 473.6 811.43 474.89 C 811.43 476.19 810.38 477.24 809.08 477.24 C 807.79 477.24 806.73 476.19 806.73 474.89 C 806.73 473.6 807.79 472.54 809.08 472.54 Z M 805.3 430.46 C 805.3 429.17 806.36 428.11 807.65 428.11 C 808.94 428.11 810 429.17 810 430.46 C 810 431.76 808.94 432.81 807.65 432.81 C 806.36 432.81 805.3 431.76 805.3 430.46 Z M 795.67 451.96 C 795.67 453.26 794.61 454.31 793.32 454.31 C 792.02 454.31 790.97 453.26 790.97 451.96 C 790.97 450.66 792.02 449.61 793.32 449.61 C 794.61 449.61 795.67 450.66 795.67 451.96 Z M 809.6 497.82 C 809.6 496.53 810.65 495.47 811.95 495.47 C 813.25 495.47 814.3 496.53 814.3 497.82 C 814.3 499.12 813.25 500.17 811.95 500.17 C 810.65 500.17 809.6 499.12 809.6 497.82 Z M 813.52 514.11 L 788.1 499.07 L 788.1 482.57 C 788.1 481.6 787.57 480.7 786.72 480.23 L 785.77 479.71 L 792.8 479.71 L 792.8 498.54 C 792.8 500.01 793.99 501.21 795.47 501.21 L 805.09 501.21 C 806.34 503.74 808.93 505.51 811.95 505.51 C 816.19 505.51 819.63 502.06 819.63 497.82 C 819.63 493.59 816.19 490.14 811.95 490.14 C 808.39 490.14 805.42 492.59 804.55 495.87 L 798.13 495.87 L 798.13 477.04 C 798.13 475.57 796.94 474.38 795.47 474.38 L 776.07 474.38 L 773.77 473.11 L 773.77 448.97 L 786.75 441.61 C 787.59 441.13 788.1 440.25 788.1 439.28 L 788.1 432.41 L 791.37 432.41 L 791.37 444.56 C 788.08 445.43 785.64 448.4 785.64 451.96 C 785.64 456.2 789.08 459.64 793.32 459.64 C 797.55 459.64 801 456.2 801 451.96 C 801 448.94 799.24 446.36 796.7 445.1 L 796.7 432.41 L 800.25 432.41 C 801.12 435.7 804.09 438.14 807.65 438.14 C 811.89 438.14 815.33 434.7 815.33 430.46 C 815.33 426.23 811.89 422.78 807.65 422.78 C 804.64 422.78 802.05 424.54 800.79 427.08 L 788.1 427.08 L 788.1 422.85 L 812.61 407.91 L 824.34 414.94 L 824.34 445.71 L 808.37 445.71 C 806.89 445.71 805.7 446.9 805.7 448.38 L 805.7 468.03 C 803.17 469.28 801.4 471.87 801.4 474.89 C 801.4 479.13 804.85 482.58 809.08 482.58 C 813.32 482.58 816.77 479.13 816.77 474.89 C 816.77 471.33 814.32 468.36 811.03 467.49 L 811.03 451.04 L 824.34 451.04 L 824.34 507.68 Z M 885.56 447.42 C 885.56 446.46 885.04 445.57 884.21 445.1 L 871.23 437.74 L 871.23 421.36 C 871.23 420.43 870.74 419.57 869.95 419.08 L 842.79 402.53 C 841.95 402.01 840.89 402 840.04 402.51 L 827 410.32 L 813.96 402.51 C 813.11 402 812.05 402.01 811.21 402.52 L 784.05 419.08 C 783.26 419.56 782.77 420.42 782.77 421.35 L 782.77 437.73 L 769.79 445.09 C 768.95 445.57 768.44 446.45 768.44 447.41 L 768.44 474.69 C 768.44 475.66 768.97 476.55 769.82 477.02 L 782.77 484.15 L 782.77 500.59 C 782.77 501.54 783.26 502.41 784.08 502.89 L 812.17 519.5 C 813.01 520 814.05 520 814.89 519.5 L 826.13 512.82 L 837.69 519.52 C 838.11 519.76 838.57 519.88 839.03 519.88 C 839.5 519.88 839.97 519.75 840.39 519.5 L 868.49 502.89 C 869.3 502.41 869.8 501.54 869.8 500.6 L 869.8 484.13 L 884.2 476.02 C 885.04 475.55 885.56 474.66 885.56 473.7 Z"
                  fill="#6d8764" stroke="none" pointer-events="all"/>
        </g>
        <g>
            <rect x="920" y="212" width="163" height="30" fill="none" stroke="none" pointer-events="all"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject pointer-events="none" width="100%" height="100%"
                                   requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"
                                   style="overflow: visible; text-align: left;">
                        <div xmlns="http://www.w3.org/1999/xhtml"
                             style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 161px; height: 1px; padding-top: 227px; margin-left: 921px;">
                            <div data-drawio-colors="color: rgb(0, 0, 0); "
                                 style="box-sizing: border-box; font-size: 0px; text-align: center;">
                                <div style="display: inline-block; font-size: 18px; font-family: &quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, Avenir, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, Helvetica, Arial, &quot;sans-serif&quot;; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                    Qdrant vector db
                                    <div>(ground-truths)</div>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1002" y="232" fill="rgb(0, 0, 0)"
                          font-family="&quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, &quot;Avenir&quot;, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, &quot;Helvetica&quot;, &quot;Arial&quot;, &quot;sans-serif&quot;"
                          font-size="18px" text-anchor="middle">Qdrant vector db...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="684" y="154" width="190" height="100" fill="none" stroke="none" pointer-events="all"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject pointer-events="none" width="100%" height="100%"
                                   requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"
                                   style="overflow: visible; text-align: left;">
                        <div xmlns="http://www.w3.org/1999/xhtml"
                             style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 188px; height: 1px; padding-top: 204px; margin-left: 686px;">
                            <div data-drawio-colors="color: #99004D; "
                                 style="box-sizing: border-box; font-size: 0px; text-align: left;">
                                <div style="display: inline-block; font-size: 18px; font-family: &quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, Avenir, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, Helvetica, Arial, &quot;sans-serif&quot;; color: rgb(153, 0, 77); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                    Retrieval
                                    <font color="#000000">of</font>
                                    <div>
                                        <font color="#000000">Relevant Information </font>
                                        <div>
                                            <font color="#000000">for </font>
                                        </div>
                                        <div>
                                            <span>
                                                <font color="#000000">Enhancing Context</font>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="686" y="209" fill="#99004D"
                          font-family="&quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, &quot;Avenir&quot;, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, &quot;Helvetica&quot;, &quot;Arial&quot;, &quot;sans-serif&quot;"
                          font-size="18px">Retrieval of...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="340" y="472" width="180" height="125" rx="18.75" ry="18.75" fill="rgb(255, 255, 255)"
                  stroke="rgb(0, 0, 0)" stroke-width="3" pointer-events="all"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject pointer-events="none" width="100%" height="100%"
                                   requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"
                                   style="overflow: visible; text-align: left;">
                        <div xmlns="http://www.w3.org/1999/xhtml"
                             style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 178px; height: 1px; padding-top: 535px; margin-left: 341px;">
                            <div data-drawio-colors="color: rgb(0, 0, 0); "
                                 style="box-sizing: border-box; font-size: 0px; text-align: center;">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, Avenir, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, Helvetica, Arial, &quot;sans-serif&quot;; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                    <p style="line-height: 90%;">
                                        <span style="font-size: 18px;">
                                            <font color="#99004d">Augumented </font>
                                        </span>
                                    </p>
                                    <p style="line-height: 90%;">
                                        <span style="font-size: 18px;">Prompt 
                                            <br/>
                                        </span>
                                        <span style="background-color: initial; font-size: 18px;">+
                                            <br/>
                                        </span>
                                        <span style="background-color: initial; font-size: 18px;">Enhanced Context
                                        </span>
                                    </p>
                                    <p></p>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="430" y="538" fill="rgb(0, 0, 0)"
                          font-family="&quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, &quot;Avenir&quot;, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, &quot;Helvetica&quot;, &quot;Arial&quot;, &quot;sans-serif&quot;"
                          font-size="12px" text-anchor="middle">Augumented...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 521.24 494.38 L 770.9 494.01" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3"
                  stroke-miterlimit="10" pointer-events="stroke"/>
            <path d="M 777.65 494 L 768.65 498.52 L 770.9 494.01 L 768.64 489.52 Z" fill="rgb(0, 0, 0)"
                  stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/>
        </g>
        <g>
            <path d="M 434.89 342 L 434.7 461.9" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3"
                  stroke-miterlimit="10" pointer-events="stroke"/>
            <path d="M 434.69 468.65 L 430.2 459.64 L 434.7 461.9 L 439.2 459.65 Z" fill="rgb(0, 0, 0)"
                  stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/>
        </g>
        <g>
            <path d="M 430 312 L 430 272 L 450 252 L 510 252" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2"
                  stroke-miterlimit="10" stroke-dasharray="2 6" pointer-events="stroke"/>
        </g>
        <g>
            <path d="M 768 462 L 500 462 L 500 362.1" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3"
                  stroke-miterlimit="10" pointer-events="stroke"/>
            <path d="M 500 355.35 L 504.5 364.35 L 500 362.1 L 495.5 364.35 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)"
                  stroke-width="3" stroke-miterlimit="10" pointer-events="all"/>
        </g>
        <g>
            <rect x="520" y="398" width="160" height="50" fill="none" stroke="none" pointer-events="all"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject pointer-events="none" width="100%" height="100%"
                                   requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"
                                   style="overflow: visible; text-align: left;">
                        <div xmlns="http://www.w3.org/1999/xhtml"
                             style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 158px; height: 1px; padding-top: 423px; margin-left: 522px;">
                            <div data-drawio-colors="color: #99004D; "
                                 style="box-sizing: border-box; font-size: 0px; text-align: left;">
                                <div style="display: inline-block; font-size: 18px; font-family: &quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, Avenir, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, Helvetica, Arial, &quot;sans-serif&quot;; color: rgb(153, 0, 77); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                    Generation
                                    <br/>
                                    <div>
                                        <font><font color="#000000">of Text Response</font> 
                                        </font>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="522" y="428" fill="#99004D"
                          font-family="&quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, &quot;Avenir&quot;, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, &quot;Helvetica&quot;, &quot;Arial&quot;, &quot;sans-serif&quot;"
                          font-size="18px">Generation...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 484.96 312 L 484.96 292" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2"
                  stroke-miterlimit="10" stroke-dasharray="2 6" pointer-events="stroke"/>
        </g>
        <g>
            <ellipse cx="30" cy="242" rx="29.999999999999996" ry="20" fill="#fff2cc" stroke="#d6b656"
                     pointer-events="all"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject pointer-events="none" width="100%" height="100%"
                                   requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"
                                   style="overflow: visible; text-align: left;">
                        <div xmlns="http://www.w3.org/1999/xhtml"
                             style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 242px; margin-left: 1px;">
                            <div data-drawio-colors="color: rgb(0, 0, 0); "
                                 style="box-sizing: border-box; font-size: 0px; text-align: center;">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, Avenir, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, Helvetica, Arial, &quot;sans-serif&quot;; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                    <font size="1" style="">
                                        <span style="font-size: 25px;">1</span>
                                    </font>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="30" y="246" fill="rgb(0, 0, 0)"
                          font-family="&quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, &quot;Avenir&quot;, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, &quot;Helvetica&quot;, &quot;Arial&quot;, &quot;sans-serif&quot;"
                          font-size="12px" text-anchor="middle">1
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="450" y="112" width="70" height="30" fill="none" stroke="none" pointer-events="all"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject pointer-events="none" width="100%" height="100%"
                                   requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"
                                   style="overflow: visible; text-align: left;">
                        <div xmlns="http://www.w3.org/1999/xhtml"
                             style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 68px; height: 1px; padding-top: 127px; margin-left: 452px;">
                            <div data-drawio-colors="color: rgb(0, 0, 0); "
                                 style="box-sizing: border-box; font-size: 0px; text-align: left;">
                                <div style="display: inline-block; font-size: 18px; font-family: &quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, Avenir, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, Helvetica, Arial, &quot;sans-serif&quot;; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                    Query
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="452" y="132" fill="rgb(0, 0, 0)"
                          font-family="&quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, &quot;Avenir&quot;, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, &quot;Helvetica&quot;, &quot;Arial&quot;, &quot;sans-serif&quot;"
                          font-size="18px">Query
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <ellipse cx="460" cy="72" rx="29.999999999999996" ry="20" fill="#fff2cc" stroke="#d6b656"
                     pointer-events="all"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject pointer-events="none" width="100%" height="100%"
                                   requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"
                                   style="overflow: visible; text-align: left;">
                        <div xmlns="http://www.w3.org/1999/xhtml"
                             style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 72px; margin-left: 431px;">
                            <div data-drawio-colors="color: rgb(0, 0, 0); "
                                 style="box-sizing: border-box; font-size: 0px; text-align: center;">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, Avenir, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, Helvetica, Arial, &quot;sans-serif&quot;; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                    <font size="1" style="">
                                        <span style="font-size: 25px;">2</span>
                                    </font>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="460" y="76" fill="rgb(0, 0, 0)"
                          font-family="&quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, &quot;Avenir&quot;, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, &quot;Helvetica&quot;, &quot;Arial&quot;, &quot;sans-serif&quot;"
                          font-size="12px" text-anchor="middle">2
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <ellipse cx="710" cy="282" rx="29.999999999999996" ry="20" fill="#fff2cc" stroke="#d6b656"
                     pointer-events="all"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject pointer-events="none" width="100%" height="100%"
                                   requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"
                                   style="overflow: visible; text-align: left;">
                        <div xmlns="http://www.w3.org/1999/xhtml"
                             style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 282px; margin-left: 681px;">
                            <div data-drawio-colors="color: rgb(0, 0, 0); "
                                 style="box-sizing: border-box; font-size: 0px; text-align: center;">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, Avenir, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, Helvetica, Arial, &quot;sans-serif&quot;; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                    <font size="1" style="">
                                        <span style="font-size: 25px;">3</span>
                                    </font>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="710" y="286" fill="rgb(0, 0, 0)"
                          font-family="&quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, &quot;Avenir&quot;, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, &quot;Helvetica&quot;, &quot;Arial&quot;, &quot;sans-serif&quot;"
                          font-size="12px" text-anchor="middle">3
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <ellipse cx="560" cy="533" rx="29.999999999999996" ry="20" fill="#fff2cc" stroke="#d6b656"
                     pointer-events="all"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject pointer-events="none" width="100%" height="100%"
                                   requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"
                                   style="overflow: visible; text-align: left;">
                        <div xmlns="http://www.w3.org/1999/xhtml"
                             style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 533px; margin-left: 531px;">
                            <div data-drawio-colors="color: rgb(0, 0, 0); "
                                 style="box-sizing: border-box; font-size: 0px; text-align: center;">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, Avenir, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, Helvetica, Arial, &quot;sans-serif&quot;; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                    <font size="1" style="">
                                        <span style="font-size: 25px;">4</span>
                                    </font>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="560" y="537" fill="rgb(0, 0, 0)"
                          font-family="&quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, &quot;Avenir&quot;, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, &quot;Helvetica&quot;, &quot;Arial&quot;, &quot;sans-serif&quot;"
                          font-size="12px" text-anchor="middle">4
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <ellipse cx="710" cy="422" rx="29.999999999999996" ry="20" fill="#fff2cc" stroke="#d6b656"
                     pointer-events="all"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject pointer-events="none" width="100%" height="100%"
                                   requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"
                                   style="overflow: visible; text-align: left;">
                        <div xmlns="http://www.w3.org/1999/xhtml"
                             style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 422px; margin-left: 681px;">
                            <div data-drawio-colors="color: rgb(0, 0, 0); "
                                 style="box-sizing: border-box; font-size: 0px; text-align: center;">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, Avenir, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, Helvetica, Arial, &quot;sans-serif&quot;; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                    <font size="1" style="">
                                        <span style="font-size: 25px;">5</span>
                                    </font>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="710" y="426" fill="rgb(0, 0, 0)"
                          font-family="&quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, &quot;Avenir&quot;, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, &quot;Helvetica&quot;, &quot;Arial&quot;, &quot;sans-serif&quot;"
                          font-size="12px" text-anchor="middle">5
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 951.5 69 C 951.5 39.67 1051.5 39.67 1051.5 69 L 1051.5 165 C 1051.5 194.33 951.5 194.33 951.5 165 Z"
                  fill="#e1d5e7" stroke="#9673a6" stroke-width="5" stroke-miterlimit="10" pointer-events="all"/>
            <path d="M 951.5 69 C 951.5 91 1051.5 91 1051.5 69 M 951.5 80 C 951.5 102 1051.5 102 1051.5 80 M 951.5 91 C 951.5 113 1051.5 113 1051.5 91"
                  fill="none" stroke="#9673a6" stroke-width="5" stroke-miterlimit="10" pointer-events="all"/>
        </g>
        <g>
            <path d="M 60 262 L 90 262 L 120 292 L 120 342 L 60 342 L 60 262 Z" fill="rgb(255, 255, 255)"
                  stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/>
            <path d="M 90 262 L 90 292 L 120 292 Z" fill-opacity="0.05" fill="#000000" stroke="none"
                  pointer-events="all"/>
            <path d="M 90 262 L 90 292 L 120 292" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3"
                  stroke-miterlimit="10" pointer-events="all"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject pointer-events="none" width="100%" height="100%"
                                   requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"
                                   style="overflow: visible; text-align: left;">
                        <div xmlns="http://www.w3.org/1999/xhtml"
                             style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 302px; margin-left: 61px;">
                            <div data-drawio-colors="color: rgb(0, 0, 0); "
                                 style="box-sizing: border-box; font-size: 0px; text-align: center;">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, Avenir, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, Helvetica, Arial, &quot;sans-serif&quot;; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                    Log file
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="90" y="306" fill="rgb(0, 0, 0)"
                          font-family="&quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, &quot;Avenir&quot;, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, &quot;Helvetica&quot;, &quot;Arial&quot;, &quot;sans-serif&quot;"
                          font-size="12px" text-anchor="middle">Log file
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 69.18 187 L 150.82 187 C 155.89 187 160 195.95 160 207 C 160 218.05 155.89 227 150.82 227 L 69.18 227 C 64.11 227 60 218.05 60 207 C 60 195.95 64.11 187 69.18 187 Z"
                  fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10"
                  pointer-events="all"/>
            <path d="M 150.82 187 C 145.74 187 141.63 195.95 141.63 207 C 141.63 218.05 145.74 227 150.82 227"
                  fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject pointer-events="none" width="100%" height="100%"
                                   requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"
                                   style="overflow: visible; text-align: left;">
                        <div xmlns="http://www.w3.org/1999/xhtml"
                             style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 207px; margin-left: 61px;">
                            <div data-drawio-colors="color: rgb(0, 0, 0); "
                                 style="box-sizing: border-box; font-size: 0px; text-align: center;">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, Avenir, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, Helvetica, Arial, &quot;sans-serif&quot;; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                    Analysis
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="110" y="211" fill="rgb(0, 0, 0)"
                          font-family="&quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, &quot;Avenir&quot;, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, &quot;Helvetica&quot;, &quot;Arial&quot;, &quot;sans-serif&quot;"
                          font-size="12px" text-anchor="middle">Analysis
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 450 267 L 500 267 L 500 289 L 490 289 L 490 302 L 480 289 L 450 289 Z" fill="rgb(255, 255, 255)"
                  stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        </g>
    </g>
</svg>
