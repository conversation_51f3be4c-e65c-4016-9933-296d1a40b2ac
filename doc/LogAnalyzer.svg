<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" version="1.1" width="871px"
     height="303px" viewBox="-0.5 -0.5 871 303">
    <defs>
        <style type="text/css">@import url(https://fonts.googleapis.com/css2?family=Architects+Daughter:wght@400;500);&#xa;</style>
    </defs>
    <g>
        <g>
            <rect x="96" y="0" width="550" height="300" fill="#fff7fb" stroke="#99004d" pointer-events="all"/>
        </g>
        <g>
            <rect x="416" y="40" width="200" height="80" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)"
                  stroke-width="3" pointer-events="all"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject pointer-events="none" width="100%" height="100%"
                                   requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"
                                   style="overflow: visible; text-align: left;">
                        <div xmlns="http://www.w3.org/1999/xhtml"
                             style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 198px; height: 1px; padding-top: 80px; margin-left: 417px;">
                            <div data-drawio-colors="color: rgb(0, 0, 0); "
                                 style="box-sizing: border-box; font-size: 0px; text-align: center;">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, Avenir, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, Helvetica, Arial, &quot;sans-serif&quot;; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                    <p style="line-height: 90%;">
                                        <br/>
                                    </p>
                                    <p></p>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="516" y="84" fill="rgb(0, 0, 0)"
                          font-family="&quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, &quot;Avenir&quot;, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, &quot;Helvetica&quot;, &quot;Arial&quot;, &quot;sans-serif&quot;"
                          font-size="12px" text-anchor="middle">&#xa;
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="406" y="50" width="200" height="80" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)"
                  stroke-width="3" pointer-events="all"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject pointer-events="none" width="100%" height="100%"
                                   requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"
                                   style="overflow: visible; text-align: left;">
                        <div xmlns="http://www.w3.org/1999/xhtml"
                             style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 198px; height: 1px; padding-top: 90px; margin-left: 407px;">
                            <div data-drawio-colors="color: rgb(0, 0, 0); "
                                 style="box-sizing: border-box; font-size: 0px; text-align: center;">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, Avenir, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, Helvetica, Arial, &quot;sans-serif&quot;; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                    <p style="line-height: 90%;">
                                        <br/>
                                    </p>
                                    <p></p>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="506" y="94" fill="rgb(0, 0, 0)"
                          font-family="&quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, &quot;Avenir&quot;, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, &quot;Helvetica&quot;, &quot;Arial&quot;, &quot;sans-serif&quot;"
                          font-size="12px" text-anchor="middle">&#xa;
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="146" y="40" width="200" height="125" rx="18.75" ry="18.75" fill="rgb(255, 255, 255)"
                  stroke="rgb(0, 0, 0)" stroke-width="3" pointer-events="all"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject pointer-events="none" width="100%" height="100%"
                                   requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"
                                   style="overflow: visible; text-align: left;">
                        <div xmlns="http://www.w3.org/1999/xhtml"
                             style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 198px; height: 1px; padding-top: 103px; margin-left: 147px;">
                            <div data-drawio-colors="color: rgb(0, 0, 0); "
                                 style="box-sizing: border-box; font-size: 0px; text-align: center;">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, Avenir, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, Helvetica, Arial, &quot;sans-serif&quot;; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                    <p style="line-height: 90%;">
                                        <span style="font-size: 18px;">
                                            <font color="#99004d">LogAnalyzer Client</font>
                                        </span>
                                    </p>
                                    <p></p>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="246" y="106" fill="rgb(0, 0, 0)"
                          font-family="&quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, &quot;Avenir&quot;, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, &quot;Helvetica&quot;, &quot;Arial&quot;, &quot;sans-serif&quot;"
                          font-size="12px" text-anchor="middle">LogAnalyzer Client&#xa;
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 346 102.5 L 384.9 102.9" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3"
                  stroke-miterlimit="10" pointer-events="stroke"/>
            <path d="M 391.65 102.97 L 382.6 107.37 L 384.9 102.9 L 382.69 98.37 Z" fill="rgb(0, 0, 0)"
                  stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/>
        </g>
        <g>
            <rect x="186" y="190" width="160" height="80" rx="12" ry="12" fill="rgb(255, 255, 255)"
                  stroke="rgb(0, 0, 0)" stroke-width="3" pointer-events="all"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject pointer-events="none" width="100%" height="100%"
                                   requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"
                                   style="overflow: visible; text-align: left;">
                        <div xmlns="http://www.w3.org/1999/xhtml"
                             style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 158px; height: 1px; padding-top: 230px; margin-left: 187px;">
                            <div data-drawio-colors="color: #99004D; "
                                 style="box-sizing: border-box; font-size: 0px; text-align: center;">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, Avenir, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, Helvetica, Arial, &quot;sans-serif&quot;; color: rgb(153, 0, 77); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                    <span style="font-size: 18px;">LogVizualizer</span>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="266" y="234" fill="#99004D"
                          font-family="&quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, &quot;Avenir&quot;, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, &quot;Helvetica&quot;, &quot;Arial&quot;, &quot;sans-serif&quot;"
                          font-size="12px" text-anchor="middle">LogVizualizer
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 683 14 L 722 14 L 752 44 L 752 94 L 683 94 L 683 14 Z" fill="rgb(255, 255, 255)"
                  stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/>
            <path d="M 722 14 L 722 44 L 752 44 Z" fill-opacity="0.05" fill="#000000" stroke="none"
                  pointer-events="all"/>
            <path d="M 722 14 L 722 44 L 752 44" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3"
                  stroke-miterlimit="10" pointer-events="all"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject pointer-events="none" width="100%" height="100%"
                                   requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"
                                   style="overflow: visible; text-align: left;">
                        <div xmlns="http://www.w3.org/1999/xhtml"
                             style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 67px; height: 1px; padding-top: 54px; margin-left: 684px;">
                            <div data-drawio-colors="color: rgb(0, 0, 0); "
                                 style="box-sizing: border-box; font-size: 0px; text-align: center;">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, Avenir, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, Helvetica, Arial, &quot;sans-serif&quot;; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                    <div>
                                        <br/>
                                    </div>
                                    Analysis
                                    <div>Result</div>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="717" y="58" fill="rgb(0, 0, 0)"
                          font-family="&quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, &quot;Avenir&quot;, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, &quot;Helvetica&quot;, &quot;Arial&quot;, &quot;sans-serif&quot;"
                          font-size="12px" text-anchor="middle">Analysis...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 683 114 L 722 114 L 752 144 L 752 194 L 683 194 L 683 114 Z" fill="rgb(255, 255, 255)"
                  stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/>
            <path d="M 722 114 L 722 144 L 752 144 Z" fill-opacity="0.05" fill="#000000" stroke="none"
                  pointer-events="all"/>
            <path d="M 722 114 L 722 144 L 752 144" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3"
                  stroke-miterlimit="10" pointer-events="all"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject pointer-events="none" width="100%" height="100%"
                                   requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"
                                   style="overflow: visible; text-align: left;">
                        <div xmlns="http://www.w3.org/1999/xhtml"
                             style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 67px; height: 1px; padding-top: 154px; margin-left: 684px;">
                            <div data-drawio-colors="color: rgb(0, 0, 0); "
                                 style="box-sizing: border-box; font-size: 0px; text-align: center;">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, Avenir, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, Helvetica, Arial, &quot;sans-serif&quot;; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                    <div>
                                        <br/>
                                    </div>
                                    <div>LLM</div>
                                    <div>Prompt</div>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="717" y="158" fill="rgb(0, 0, 0)"
                          font-family="&quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, &quot;Avenir&quot;, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, &quot;Helvetica&quot;, &quot;Arial&quot;, &quot;sans-serif&quot;"
                          font-size="12px" text-anchor="middle">LLM...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 801.5 180 L 838.5 180 L 868.5 210 L 868.5 260 L 801.5 260 L 801.5 180 Z"
                  fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10"
                  pointer-events="all"/>
            <path d="M 838.5 180 L 838.5 210 L 868.5 210 Z" fill-opacity="0.05" fill="#000000" stroke="none"
                  pointer-events="all"/>
            <path d="M 838.5 180 L 838.5 210 L 868.5 210" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3"
                  stroke-miterlimit="10" pointer-events="all"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject pointer-events="none" width="100%" height="100%"
                                   requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"
                                   style="overflow: visible; text-align: left;">
                        <div xmlns="http://www.w3.org/1999/xhtml"
                             style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 65px; height: 1px; padding-top: 220px; margin-left: 803px;">
                            <div data-drawio-colors="color: rgb(0, 0, 0); "
                                 style="box-sizing: border-box; font-size: 0px; text-align: center;">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, Avenir, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, Helvetica, Arial, &quot;sans-serif&quot;; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                    <div>
                                        <br/>
                                    </div>
                                    <div>Analysis</div>
                                    <div>
                                        <font style="font-size: 10px;">Visualization</font>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="835" y="224" fill="rgb(0, 0, 0)"
                          font-family="&quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, &quot;Avenir&quot;, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, &quot;Helvetica&quot;, &quot;Arial&quot;, &quot;sans-serif&quot;"
                          font-size="12px" text-anchor="middle">Analysis...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 1 20 L 40 20 L 70 50 L 70 110 L 1 110 L 1 20 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)"
                  stroke-width="3" stroke-miterlimit="10" pointer-events="all"/>
            <path d="M 40 20 L 40 50 L 70 50 Z" fill-opacity="0.05" fill="#000000" stroke="none" pointer-events="all"/>
            <path d="M 40 20 L 40 50 L 70 50" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10"
                  pointer-events="all"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject pointer-events="none" width="100%" height="100%"
                                   requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"
                                   style="overflow: visible; text-align: left;">
                        <div xmlns="http://www.w3.org/1999/xhtml"
                             style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 67px; height: 1px; padding-top: 65px; margin-left: 2px;">
                            <div data-drawio-colors="color: rgb(0, 0, 0); "
                                 style="box-sizing: border-box; font-size: 0px; text-align: center;">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, Avenir, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, Helvetica, Arial, &quot;sans-serif&quot;; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                    <div>
                                        <br/>
                                    </div>
                                    <div>Log</div>
                                    <div>File</div>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="35" y="69" fill="rgb(0, 0, 0)"
                          font-family="&quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, &quot;Avenir&quot;, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, &quot;Helvetica&quot;, &quot;Arial&quot;, &quot;sans-serif&quot;"
                          font-size="12px" text-anchor="middle">Log...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="394.5" y="60" width="200" height="80" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)"
                  stroke-width="3" pointer-events="all"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject pointer-events="none" width="100%" height="100%"
                                   requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"
                                   style="overflow: visible; text-align: left;">
                        <div xmlns="http://www.w3.org/1999/xhtml"
                             style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 198px; height: 1px; padding-top: 100px; margin-left: 397px;">
                            <div data-drawio-colors="color: #99004D; "
                                 style="box-sizing: border-box; font-size: 0px; text-align: left;">
                                <div style="display: inline-block; font-size: 14px; font-family: &quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, Avenir, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, Helvetica, Arial, &quot;sans-serif&quot;; color: rgb(153, 0, 77); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                       Analisys Method
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="397" y="104" fill="#99004D"
                          font-family="&quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, &quot;Avenir&quot;, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, &quot;Helvetica&quot;, &quot;Arial&quot;, &quot;sans-serif&quot;"
                          font-size="14px">   Analisys Method
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 594.5 100 L 626 100 L 626 54 L 672.9 54" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3"
                  stroke-miterlimit="10" pointer-events="stroke"/>
            <path d="M 679.65 54 L 670.65 58.5 L 672.9 54 L 670.65 49.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)"
                  stroke-width="3" stroke-miterlimit="10" pointer-events="all"/>
        </g>
        <g>
            <path d="M 754 70 L 776 70 L 776 220 L 454 220 L 355.94 220.29" fill="none" stroke="rgb(0, 0, 0)"
                  stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/>
            <path d="M 349.19 220.31 L 358.18 215.78 L 355.94 220.29 L 358.21 224.78 Z" fill="rgb(0, 0, 0)"
                  stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/>
        </g>
        <g>
            <path d="M 347 240 L 790.53 240" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10"
                  pointer-events="stroke"/>
            <path d="M 797.28 240 L 788.28 244.5 L 790.53 240 L 788.28 235.5 Z" fill="rgb(0, 0, 0)"
                  stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/>
        </g>
        <g>
            <path d="M 70.69 76.07 L 133.7 76.12" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3"
                  stroke-miterlimit="10" pointer-events="stroke"/>
            <path d="M 140.45 76.12 L 131.44 80.62 L 133.7 76.12 L 131.45 71.62 Z" fill="rgb(0, 0, 0)"
                  stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/>
        </g>
        <g>
            <path d="M 116 77 L 116 227 L 175.9 227" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3"
                  stroke-miterlimit="10" pointer-events="stroke"/>
            <path d="M 182.65 227 L 173.65 231.5 L 175.9 227 L 173.65 222.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)"
                  stroke-width="3" stroke-miterlimit="10" pointer-events="all"/>
        </g>
        <g>
            <path d="M 266.4 166.25 L 266 190" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10"
                  pointer-events="stroke"/>
        </g>
        <g>
            <rect x="523.25" y="70" width="62.5" height="60" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 558.76 102.3 C 558.31 102.3 557.98 101.86 557.98 101.56 C 557.98 101.09 558.31 100.82 558.76 100.82 C 559.08 100.82 559.53 101.09 559.53 101.56 C 559.53 101.86 559.08 102.3 558.76 102.3 Z M 558.13 97.5 C 558.13 97.2 558.31 97.07 558.76 97.07 C 559.08 97.07 559.39 97.2 559.39 97.5 C 559.39 99.6 559.39 99.6 559.39 99.6 C 559.39 99.74 559.08 100.04 558.76 100.04 C 558.31 100.04 558.13 99.74 558.13 99.6 L 558.13 97.5 Z M 564.85 102.3 C 564.68 102.13 564.68 102.13 564.68 102.13 C 559.53 94.2 559.53 94.2 559.53 94.2 C 559.39 94.06 559.08 93.75 558.76 93.75 C 558.31 93.75 557.98 94.06 557.98 94.36 C 552.67 102.13 552.67 102.13 552.67 102.13 C 552.49 102.3 552.35 102.61 552.35 102.75 C 552.35 103.05 552.81 103.35 553.44 103.35 C 564.08 103.35 564.08 103.35 564.08 103.35 C 564.54 103.35 564.85 103.05 564.85 102.75 C 564.85 102.61 564.85 102.3 564.85 102.3 Z M 564.37 105.25 C 562.96 106.6 560.78 107.34 558.73 107.34 C 556.55 107.34 554.5 106.6 553.1 105.25 C 551.68 103.9 550.77 101.94 550.77 100.02 C 550.77 98.19 551.68 96.27 553.1 94.91 C 554.5 93.56 556.55 92.79 558.73 92.79 C 560.78 92.79 562.96 93.56 564.37 94.91 C 567.46 97.75 567.46 102.41 564.37 105.25 Z M 577.48 114.1 C 568.74 105.86 568.74 105.86 568.74 105.86 C 571.55 101.64 571.06 96.09 567.15 92.34 C 564.83 90.39 561.69 89.2 558.73 89.2 C 555.77 89.2 552.78 90.39 550.45 92.34 C 545.77 96.54 545.77 103.46 550.45 107.65 C 552.78 109.74 555.77 110.79 558.73 110.79 C 560.92 110.79 563.1 110.35 564.97 109.3 C 573.88 117.41 573.88 117.41 573.88 117.41 C 574.51 117.99 575.75 117.99 576.56 117.41 C 577.48 116.5 577.48 116.5 577.48 116.5 C 578.25 115.76 578.25 114.54 577.48 114.1 Z M 542.48 116.04 C 542.31 115.91 541.85 115.6 541.71 115.44 C 541.85 115.6 542.31 115.91 542.48 116.04 Z M 534.49 104.19 C 534.49 104.05 534.49 104.05 534.35 103.74 C 534.49 104.05 534.49 104.05 534.49 104.19 Z M 534.49 95.81 C 534.49 95.94 534.49 96.11 534.35 96.24 C 534.49 96.11 534.49 95.94 534.49 95.81 Z M 536.53 90.54 C 536.7 90.1 536.84 89.97 536.98 89.66 C 536.84 89.97 536.7 90.1 536.53 90.54 Z M 537.16 110.81 C 536.98 110.5 536.84 110.2 536.84 109.89 C 536.84 110.2 536.98 110.5 537.16 110.81 Z M 542.62 84.39 C 543.4 84.39 544.03 84.69 544.03 85.44 C 544.03 86.04 543.58 86.49 542.8 86.49 C 541.71 86.49 541.71 86.49 541.71 86.49 C 541.71 84.69 541.71 84.69 541.71 84.69 C 541.54 84.69 541.4 84.86 541.22 84.99 C 541.71 84.56 542.31 84.09 542.8 83.82 C 542.48 83.95 542.31 84.09 541.99 84.39 L 542.62 84.39 Z M 542.8 90.1 C 541.71 90.1 541.71 90.1 541.71 90.1 C 541.71 87.7 541.71 87.7 541.71 87.7 C 542.94 87.7 542.94 87.7 542.94 87.7 C 543.89 87.7 544.21 88.14 544.21 88.92 C 544.21 89.79 543.89 90.1 542.8 90.1 Z M 554.68 119.79 C 550.13 119.79 546.08 118.44 542.8 116.22 C 542.94 116.52 543.4 116.65 543.72 116.79 C 543.72 110.07 543.72 110.07 543.72 110.07 C 546.22 110.07 546.22 110.07 546.22 110.07 C 546.22 108.54 546.22 108.54 546.22 108.54 C 539.21 108.54 539.21 108.54 539.21 108.54 C 539.21 110.07 539.21 110.07 539.21 110.07 C 541.71 110.07 541.71 110.07 541.71 110.07 C 541.71 115.44 541.71 115.44 541.71 115.44 C 539.98 114.09 538.57 112.47 537.16 110.81 C 537.3 110.81 537.3 110.81 537.3 110.81 C 537.16 110.07 536.84 109.46 536.39 109.02 C 536.39 109.46 536.53 109.59 536.84 109.89 C 535.76 108.24 534.8 106.14 534.49 104.19 C 535.9 104.19 535.9 104.19 535.9 104.19 C 535.9 100.6 535.9 100.6 535.9 100.6 C 539.21 104.19 539.21 104.19 539.21 104.19 C 541.71 104.19 541.71 104.19 541.71 104.19 C 537.63 99.69 537.63 99.69 537.63 99.69 C 541.4 95.81 541.4 95.81 541.4 95.81 C 538.89 95.81 538.89 95.81 538.89 95.81 C 535.9 99.39 535.9 99.39 535.9 99.39 C 535.9 95.81 535.9 95.81 535.9 95.81 C 534.49 95.81 534.49 95.81 534.49 95.81 C 534.8 93.99 535.62 92.19 536.53 90.54 C 536.39 90.84 536.21 91.14 536.07 91.45 C 537.8 91.45 537.8 91.45 537.8 91.45 C 536.98 89.66 536.98 89.66 536.98 89.66 C 538.26 87.84 539.49 86.49 541.07 85.17 C 540.58 85.44 540.12 85.91 539.67 86.49 C 539.67 91.45 539.67 91.45 539.67 91.45 C 542.8 91.45 542.8 91.45 542.8 91.45 C 545.31 91.45 546.39 90.54 546.39 89.05 C 546.39 87.84 545.59 87.26 544.49 86.96 C 545.31 86.79 545.9 86.22 545.9 85.3 C 545.9 84.09 545.31 83.51 543.72 83.21 C 543.4 83.34 543.08 83.64 542.8 83.82 C 546.22 81.55 550.13 80.34 554.68 80.34 C 565.92 80.34 575.14 89.05 575.14 99.99 C 575.14 102.84 574.68 105.54 573.42 107.94 C 575.78 110.2 575.78 110.2 575.78 110.2 C 577.47 107.19 578.28 103.74 578.28 99.99 C 578.28 87.57 567.64 77.19 554.68 77.19 C 548.26 77.19 542.31 79.59 537.8 83.95 C 533.39 88.31 530.75 93.99 530.75 99.99 C 530.75 112.6 541.54 122.8 554.68 122.8 C 560.77 122.8 566.37 120.54 570.6 116.79 C 568.27 114.69 568.27 114.69 568.27 114.69 C 564.68 117.84 559.81 119.79 554.68 119.79 Z M 549.98 109.15 C 549.98 109.15 549.98 109.15 549.98 109.15 C 549.66 108.84 549.35 108.71 549.21 108.4 C 548.25 108.4 548.25 108.4 548.25 108.4 C 548.25 113.2 548.25 113.2 548.25 113.2 C 548.25 114.69 549.66 115.6 552.17 115.6 C 554.49 115.6 555.76 114.69 555.76 113.2 C 555.76 111.85 555.76 111.85 555.76 111.85 C 555.13 111.72 554.49 111.54 553.89 111.24 C 553.89 113.2 553.89 113.2 553.89 113.2 C 553.89 114.12 553.26 114.55 552.17 114.55 C 551.08 114.55 550.44 114.12 550.44 113.2 C 550.44 109.45 550.44 109.45 550.44 109.45 C 550.3 109.45 550.12 109.32 549.98 109.15 Z M 563.26 110.8 C 562.63 111.11 561.99 111.24 561.39 111.41 C 560.76 114.12 560.76 114.12 560.76 114.12 C 560.13 111.72 560.13 111.72 560.13 111.72 C 559.49 111.85 559.21 111.85 558.72 111.85 C 558.26 111.85 558.26 111.85 558.26 111.85 C 559.49 115.6 559.49 115.6 559.49 115.6 C 561.85 115.6 561.85 115.6 561.85 115.6 L 563.26 110.8 Z M 565.59 89.2 C 565.76 88.76 565.76 87.99 565.76 87.24 C 565.76 83.79 564.35 82 561.54 82 C 558.26 82 558.26 82 558.26 82 C 558.26 87.24 558.26 87.24 558.26 87.24 C 559.03 87.24 559.03 87.24 559.03 87.24 C 559.35 87.24 559.81 87.24 560.3 87.24 C 560.3 83.79 560.3 83.79 560.3 83.79 C 561.54 83.79 561.54 83.79 561.54 83.79 C 562.94 83.79 563.72 84.84 563.72 87.24 C 563.72 87.54 563.72 87.85 563.72 88.29 C 564.35 88.59 564.98 88.89 565.59 89.2 Z M 552.17 83.66 C 553.12 83.66 553.71 84.24 553.89 85.59 C 555.76 85.59 555.76 85.59 555.76 85.59 C 555.44 83.19 554.03 82 552.17 82 C 549.8 82 548.25 83.97 548.25 87.24 C 548.25 89.34 548.89 90.86 549.8 91.6 C 550.3 91.16 550.62 90.69 551.08 90.39 C 550.62 89.81 550.3 88.76 550.3 87.24 C 550.3 84.71 551.08 83.66 552.17 83.66 Z M 543.26 96.4 C 543.26 103.6 543.26 103.6 543.26 103.6 C 544.81 103.6 544.81 103.6 544.81 103.6 C 544.81 98.64 544.81 98.64 544.81 98.64 C 545.44 99.85 545.44 99.85 545.44 99.85 C 545.62 99.11 545.76 98.37 545.76 97.59 C 545.3 96.4 545.3 96.4 545.3 96.4 L 543.26 96.4 Z M 523.25 100 C 523.25 83.52 537.3 70 554.5 70 C 571.87 70 585.75 83.52 585.75 100 C 585.75 116.66 571.87 130 554.5 130 C 537.3 130 523.25 116.66 523.25 100 Z"
                  fill="#99004d" stroke="none" pointer-events="all"/>
            <ellipse cx="554.5" cy="100" rx="30.624999999999996" ry="29.399999999999995" fill="#ffffff" stroke="none"
                     pointer-events="all"/>
            <rect x="523.25" y="70" width="62.5" height="60" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 558.76 102.3 C 558.31 102.3 557.98 101.86 557.98 101.56 C 557.98 101.09 558.31 100.82 558.76 100.82 C 559.08 100.82 559.53 101.09 559.53 101.56 C 559.53 101.86 559.08 102.3 558.76 102.3 Z M 558.13 97.5 C 558.13 97.2 558.31 97.07 558.76 97.07 C 559.08 97.07 559.39 97.2 559.39 97.5 C 559.39 99.6 559.39 99.6 559.39 99.6 C 559.39 99.74 559.08 100.04 558.76 100.04 C 558.31 100.04 558.13 99.74 558.13 99.6 L 558.13 97.5 Z M 564.85 102.3 C 564.68 102.13 564.68 102.13 564.68 102.13 C 559.53 94.2 559.53 94.2 559.53 94.2 C 559.39 94.06 559.08 93.75 558.76 93.75 C 558.31 93.75 557.98 94.06 557.98 94.36 C 552.67 102.13 552.67 102.13 552.67 102.13 C 552.49 102.3 552.35 102.61 552.35 102.75 C 552.35 103.05 552.81 103.35 553.44 103.35 C 564.08 103.35 564.08 103.35 564.08 103.35 C 564.54 103.35 564.85 103.05 564.85 102.75 C 564.85 102.61 564.85 102.3 564.85 102.3 Z M 564.37 105.25 C 562.96 106.6 560.78 107.34 558.73 107.34 C 556.55 107.34 554.5 106.6 553.1 105.25 C 551.68 103.9 550.77 101.94 550.77 100.02 C 550.77 98.19 551.68 96.27 553.1 94.91 C 554.5 93.56 556.55 92.79 558.73 92.79 C 560.78 92.79 562.96 93.56 564.37 94.91 C 567.46 97.75 567.46 102.41 564.37 105.25 Z M 577.48 114.1 C 568.74 105.86 568.74 105.86 568.74 105.86 C 571.55 101.64 571.06 96.09 567.15 92.34 C 564.83 90.39 561.69 89.2 558.73 89.2 C 555.77 89.2 552.78 90.39 550.45 92.34 C 545.77 96.54 545.77 103.46 550.45 107.65 C 552.78 109.74 555.77 110.79 558.73 110.79 C 560.92 110.79 563.1 110.35 564.97 109.3 C 573.88 117.41 573.88 117.41 573.88 117.41 C 574.51 117.99 575.75 117.99 576.56 117.41 C 577.48 116.5 577.48 116.5 577.48 116.5 C 578.25 115.76 578.25 114.54 577.48 114.1 Z M 542.48 116.04 C 542.31 115.91 541.85 115.6 541.71 115.44 C 541.85 115.6 542.31 115.91 542.48 116.04 Z M 534.49 104.19 C 534.49 104.05 534.49 104.05 534.35 103.74 C 534.49 104.05 534.49 104.05 534.49 104.19 Z M 534.49 95.81 C 534.49 95.94 534.49 96.11 534.35 96.24 C 534.49 96.11 534.49 95.94 534.49 95.81 Z M 536.53 90.54 C 536.7 90.1 536.84 89.97 536.98 89.66 C 536.84 89.97 536.7 90.1 536.53 90.54 Z M 537.16 110.81 C 536.98 110.5 536.84 110.2 536.84 109.89 C 536.84 110.2 536.98 110.5 537.16 110.81 Z M 542.62 84.39 C 543.4 84.39 544.03 84.69 544.03 85.44 C 544.03 86.04 543.58 86.49 542.8 86.49 C 541.71 86.49 541.71 86.49 541.71 86.49 C 541.71 84.69 541.71 84.69 541.71 84.69 C 541.54 84.69 541.4 84.86 541.22 84.99 C 541.71 84.56 542.31 84.09 542.8 83.82 C 542.48 83.95 542.31 84.09 541.99 84.39 L 542.62 84.39 Z M 542.8 90.1 C 541.71 90.1 541.71 90.1 541.71 90.1 C 541.71 87.7 541.71 87.7 541.71 87.7 C 542.94 87.7 542.94 87.7 542.94 87.7 C 543.89 87.7 544.21 88.14 544.21 88.92 C 544.21 89.79 543.89 90.1 542.8 90.1 Z M 554.68 119.79 C 550.13 119.79 546.08 118.44 542.8 116.22 C 542.94 116.52 543.4 116.65 543.72 116.79 C 543.72 110.07 543.72 110.07 543.72 110.07 C 546.22 110.07 546.22 110.07 546.22 110.07 C 546.22 108.54 546.22 108.54 546.22 108.54 C 539.21 108.54 539.21 108.54 539.21 108.54 C 539.21 110.07 539.21 110.07 539.21 110.07 C 541.71 110.07 541.71 110.07 541.71 110.07 C 541.71 115.44 541.71 115.44 541.71 115.44 C 539.98 114.09 538.57 112.47 537.16 110.81 C 537.3 110.81 537.3 110.81 537.3 110.81 C 537.16 110.07 536.84 109.46 536.39 109.02 C 536.39 109.46 536.53 109.59 536.84 109.89 C 535.76 108.24 534.8 106.14 534.49 104.19 C 535.9 104.19 535.9 104.19 535.9 104.19 C 535.9 100.6 535.9 100.6 535.9 100.6 C 539.21 104.19 539.21 104.19 539.21 104.19 C 541.71 104.19 541.71 104.19 541.71 104.19 C 537.63 99.69 537.63 99.69 537.63 99.69 C 541.4 95.81 541.4 95.81 541.4 95.81 C 538.89 95.81 538.89 95.81 538.89 95.81 C 535.9 99.39 535.9 99.39 535.9 99.39 C 535.9 95.81 535.9 95.81 535.9 95.81 C 534.49 95.81 534.49 95.81 534.49 95.81 C 534.8 93.99 535.62 92.19 536.53 90.54 C 536.39 90.84 536.21 91.14 536.07 91.45 C 537.8 91.45 537.8 91.45 537.8 91.45 C 536.98 89.66 536.98 89.66 536.98 89.66 C 538.26 87.84 539.49 86.49 541.07 85.17 C 540.58 85.44 540.12 85.91 539.67 86.49 C 539.67 91.45 539.67 91.45 539.67 91.45 C 542.8 91.45 542.8 91.45 542.8 91.45 C 545.31 91.45 546.39 90.54 546.39 89.05 C 546.39 87.84 545.59 87.26 544.49 86.96 C 545.31 86.79 545.9 86.22 545.9 85.3 C 545.9 84.09 545.31 83.51 543.72 83.21 C 543.4 83.34 543.08 83.64 542.8 83.82 C 546.22 81.55 550.13 80.34 554.68 80.34 C 565.92 80.34 575.14 89.05 575.14 99.99 C 575.14 102.84 574.68 105.54 573.42 107.94 C 575.78 110.2 575.78 110.2 575.78 110.2 C 577.47 107.19 578.28 103.74 578.28 99.99 C 578.28 87.57 567.64 77.19 554.68 77.19 C 548.26 77.19 542.31 79.59 537.8 83.95 C 533.39 88.31 530.75 93.99 530.75 99.99 C 530.75 112.6 541.54 122.8 554.68 122.8 C 560.77 122.8 566.37 120.54 570.6 116.79 C 568.27 114.69 568.27 114.69 568.27 114.69 C 564.68 117.84 559.81 119.79 554.68 119.79 Z M 549.98 109.15 C 549.98 109.15 549.98 109.15 549.98 109.15 C 549.66 108.84 549.35 108.71 549.21 108.4 C 548.25 108.4 548.25 108.4 548.25 108.4 C 548.25 113.2 548.25 113.2 548.25 113.2 C 548.25 114.69 549.66 115.6 552.17 115.6 C 554.49 115.6 555.76 114.69 555.76 113.2 C 555.76 111.85 555.76 111.85 555.76 111.85 C 555.13 111.72 554.49 111.54 553.89 111.24 C 553.89 113.2 553.89 113.2 553.89 113.2 C 553.89 114.12 553.26 114.55 552.17 114.55 C 551.08 114.55 550.44 114.12 550.44 113.2 C 550.44 109.45 550.44 109.45 550.44 109.45 C 550.3 109.45 550.12 109.32 549.98 109.15 Z M 563.26 110.8 C 562.63 111.11 561.99 111.24 561.39 111.41 C 560.76 114.12 560.76 114.12 560.76 114.12 C 560.13 111.72 560.13 111.72 560.13 111.72 C 559.49 111.85 559.21 111.85 558.72 111.85 C 558.26 111.85 558.26 111.85 558.26 111.85 C 559.49 115.6 559.49 115.6 559.49 115.6 C 561.85 115.6 561.85 115.6 561.85 115.6 L 563.26 110.8 Z M 565.59 89.2 C 565.76 88.76 565.76 87.99 565.76 87.24 C 565.76 83.79 564.35 82 561.54 82 C 558.26 82 558.26 82 558.26 82 C 558.26 87.24 558.26 87.24 558.26 87.24 C 559.03 87.24 559.03 87.24 559.03 87.24 C 559.35 87.24 559.81 87.24 560.3 87.24 C 560.3 83.79 560.3 83.79 560.3 83.79 C 561.54 83.79 561.54 83.79 561.54 83.79 C 562.94 83.79 563.72 84.84 563.72 87.24 C 563.72 87.54 563.72 87.85 563.72 88.29 C 564.35 88.59 564.98 88.89 565.59 89.2 Z M 552.17 83.66 C 553.12 83.66 553.71 84.24 553.89 85.59 C 555.76 85.59 555.76 85.59 555.76 85.59 C 555.44 83.19 554.03 82 552.17 82 C 549.8 82 548.25 83.97 548.25 87.24 C 548.25 89.34 548.89 90.86 549.8 91.6 C 550.3 91.16 550.62 90.69 551.08 90.39 C 550.62 89.81 550.3 88.76 550.3 87.24 C 550.3 84.71 551.08 83.66 552.17 83.66 Z M 543.26 96.4 C 543.26 103.6 543.26 103.6 543.26 103.6 C 544.81 103.6 544.81 103.6 544.81 103.6 C 544.81 98.64 544.81 98.64 544.81 98.64 C 545.44 99.85 545.44 99.85 545.44 99.85 C 545.62 99.11 545.76 98.37 545.76 97.59 C 545.3 96.4 545.3 96.4 545.3 96.4 L 543.26 96.4 Z M 523.25 100 C 523.25 83.52 537.3 70 554.5 70 C 571.87 70 585.75 83.52 585.75 100 C 585.75 116.66 571.87 130 554.5 130 C 537.3 130 523.25 116.66 523.25 100 Z"
                  fill="#99004d" stroke="none" pointer-events="all"/>
        </g>
        <g>
            <rect x="1" y="120" width="70" height="85" fill="rgb(255, 255, 255)" stroke="#000000" stroke-width="3"
                  pointer-events="all"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject pointer-events="none" width="100%" height="100%"
                                   requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"
                                   style="overflow: visible; text-align: left;">
                        <div xmlns="http://www.w3.org/1999/xhtml"
                             style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 68px; height: 1px; padding-top: 200px; margin-left: 2px;">
                            <div data-drawio-colors="color: #000000; "
                                 style="box-sizing: border-box; font-size: 0px; text-align: center;">
                                <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                    Arguments
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="36" y="200" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                        Arguments
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 13.75 171.76 L 21.91 171.8 L 21.91 180 L 13.75 179.95 Z M 27.73 171.8 L 58.25 171.85 L 58.25 179.95 L 27.73 179.91 Z M 13.75 157.84 L 21.91 157.88 L 21.91 166.08 L 13.75 166.03 Z M 27.73 157.88 L 58.25 157.93 L 58.25 166.03 L 27.73 165.99 Z M 13.75 143.92 L 21.91 143.96 L 21.91 152.16 L 13.75 152.11 Z M 27.73 143.96 L 58.25 144.01 L 58.25 152.11 L 27.73 152.07 Z M 13.75 130 L 21.91 130.04 L 21.91 138.24 L 13.75 138.19 Z M 27.73 130.04 L 58.25 130.09 L 58.25 138.19 L 27.73 138.15 Z"
                  fill="#000000" stroke="none" pointer-events="all"/>
        </g>
        <g>
            <rect x="1" y="215" width="70" height="85" fill="rgb(255, 255, 255)" stroke="#000000" stroke-width="3"
                  pointer-events="all"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject pointer-events="none" width="100%" height="100%"
                                   requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"
                                   style="overflow: visible; text-align: left;">
                        <div xmlns="http://www.w3.org/1999/xhtml"
                             style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 68px; height: 1px; padding-top: 295px; margin-left: 2px;">
                            <div data-drawio-colors="color: #000000; "
                                 style="box-sizing: border-box; font-size: 0px; text-align: center;">
                                <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                    Arguments
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="36" y="295" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                        Arguments
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 13.75 266.76 L 21.91 266.8 L 21.91 275 L 13.75 274.95 Z M 27.73 266.8 L 58.25 266.85 L 58.25 274.95 L 27.73 274.91 Z M 13.75 252.84 L 21.91 252.88 L 21.91 261.08 L 13.75 261.03 Z M 27.73 252.88 L 58.25 252.93 L 58.25 261.03 L 27.73 260.99 Z M 13.75 238.92 L 21.91 238.96 L 21.91 247.16 L 13.75 247.11 Z M 27.73 238.96 L 58.25 239.01 L 58.25 247.11 L 27.73 247.07 Z M 13.75 225 L 21.91 225.04 L 21.91 233.24 L 13.75 233.19 Z M 27.73 225.04 L 58.25 225.09 L 58.25 233.19 L 27.73 233.15 Z"
                  fill="#000000" stroke="none" pointer-events="all"/>
        </g>
        <g>
            <path d="M 71.7 137.09 L 135.9 137.01" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3"
                  stroke-miterlimit="10" pointer-events="stroke"/>
            <path d="M 142.65 137 L 133.65 141.51 L 135.9 137.01 L 133.64 132.51 Z" fill="rgb(0, 0, 0)"
                  stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/>
        </g>
        <g>
            <path d="M 70.86 250.02 L 175.9 250" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3"
                  stroke-miterlimit="10" pointer-events="stroke"/>
            <path d="M 182.65 250 L 173.65 254.5 L 175.9 250 L 173.65 245.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)"
                  stroke-width="3" stroke-miterlimit="10" pointer-events="all"/>
        </g>
        <g>
            <rect x="96" y="2" width="160" height="30" fill="none" stroke="none" pointer-events="all"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject pointer-events="none" width="100%" height="100%"
                                   requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"
                                   style="overflow: visible; text-align: left;">
                        <div xmlns="http://www.w3.org/1999/xhtml"
                             style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 158px; height: 1px; padding-top: 17px; margin-left: 97px;">
                            <div data-drawio-colors="color: #99004D; "
                                 style="box-sizing: border-box; font-size: 0px; text-align: center;">
                                <div style="display: inline-block; font-size: 22px; font-family: &quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, Avenir, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, Helvetica, Arial, &quot;sans-serif&quot;; color: rgb(153, 0, 77); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">
                                    LogAnalyzer
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="176" y="24" fill="#99004D"
                          font-family="&quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, &quot;Avenir&quot;, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, &quot;Helvetica&quot;, &quot;Arial&quot;, &quot;sans-serif&quot;"
                          font-size="22px" text-anchor="middle" font-weight="bold">LogAnalyzer
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="394.5" y="150" width="200" height="50" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)"
                  stroke-width="3" pointer-events="all"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject pointer-events="none" width="100%" height="100%"
                                   requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"
                                   style="overflow: visible; text-align: left;">
                        <div xmlns="http://www.w3.org/1999/xhtml"
                             style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 198px; height: 1px; padding-top: 175px; margin-left: 397px;">
                            <div data-drawio-colors="color: #99004D; "
                                 style="box-sizing: border-box; font-size: 0px; text-align: left;">
                                <div style="display: inline-block; font-size: 14px; font-family: &quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, Avenir, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, Helvetica, Arial, &quot;sans-serif&quot;; color: rgb(153, 0, 77); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                       Prompt Generator
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="397" y="179" fill="#99004D"
                          font-family="&quot;Avenir,Avenir Next,Segoe UI,Helvetica,Arial,sans-serif&quot;, &quot;Avenir&quot;, &quot;Avenir Next&quot;, &quot;Segoe UI&quot;, &quot;Helvetica&quot;, &quot;Arial&quot;, &quot;sans-serif&quot;"
                          font-size="14px">   Prompt Generator
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 525.75 158 L 585.75 158 L 585.75 178 L 556.15 178 L 546.15 193 L 546.15 178 L 525.75 178 Z"
                  fill="#99004d" stroke="none" pointer-events="all"/>
        </g>
        <g>
            <path d="M 346 120 L 366 120 L 366 175 L 384.4 175" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3"
                  stroke-miterlimit="10" pointer-events="stroke"/>
            <path d="M 391.15 175 L 382.15 179.5 L 384.4 175 L 382.15 170.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)"
                  stroke-width="3" stroke-miterlimit="10" pointer-events="all"/>
        </g>
        <g>
            <path d="M 596 175 L 671.86 174.82" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3"
                  stroke-miterlimit="10" pointer-events="stroke"/>
            <path d="M 678.61 174.81 L 669.62 179.33 L 671.86 174.82 L 669.6 170.33 Z" fill="rgb(0, 0, 0)"
                  stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/>
        </g>
    </g>
</svg>
