## knut-gpt/mock_server/app.py
from flask import Flask, jsonify, request, send_from_directory
from flask_cors import CORS
import os, json

VOCAB_MAP = {
    'keywords': {
        'error': 'keywords_errors.json',
        'false_positive': 'keywords_false_positives.json',
        'hints': 'keywords_hints.json',
        'success': 'keywords_success.json'
    },
    'regex': {
        'error': 'regex_errors.json',
        'false_positive': 'regex_false_positives.json',
        'hints': 'regex_hints.json',
        'success': 'regex_success.json'
    }
}

app = Flask(__name__)
CORS(app)

# Utility to load a JSON file from ./data
def load_json(name):
    path = os.path.join(os.path.dirname(__file__), 'data', name)
    with open(path, 'r', encoding='utf-8') as f:
        return json.load(f)

# Load all mock data at startup
USERS    = load_json('users.json')               # single object
PROJECTS = load_json('projects.json')            # list
COLLS    = load_json('knowledge_collections.json')  # list
SESS_SUM = load_json('jobs_summary.json')    # list
SESS_DET = load_json('jobs_details.json')    # list

# --- LOGIN endpoint ---
@app.route('/api/login', methods=['POST'])
def login():
    data = request.get_json() or {}
    username = data.get('username')
    password = data.get('password')
    for user in USERS:
        if user.get('username') == username and user.get('password') == password:
            # Return a copy of the user object without password
            result = user.copy()
            result.pop('password', None)
            return jsonify(result), 200
    return jsonify({'error': 'Invalid credentials'}), 401

# --- USER endpoint ---
@app.route('/api/user', methods=['GET'])
def get_current_user():
    return jsonify(USERS)

# --- PROJECT endpoints ---
@app.route('/api/projects', methods=['GET'])
def list_projects():
    return jsonify(PROJECTS)

@app.route('/api/projects/<int:pid>', methods=['GET'])
def get_project(pid):
    proj = next((p for p in PROJECTS if p['id'] == pid), None)
    if not proj:
        return jsonify({'error': 'Project not found'}), 404
    return jsonify(proj)


# --- COLLECTION endpoints ---
@app.route('/api/collections', methods=['GET'])
def list_collections():
    """Get all knowledge collections"""
    return jsonify(COLLS)

# get one collection by id
@app.route('/api/collections/<int:cid>', methods=['GET'])
def get_collection(cid):
    coll = next((c for c in COLLS if c['id'] == cid), None)
    if not coll:
        return jsonify({'error': 'Collection not found'}), 404
    return jsonify(coll)

# --- SESSION endpoints ---
@app.route('/api/jobs', methods=['GET'])
def list_jobs():
    user_id = request.args.get('user_id', type=int)
    if user_id:
        user_jobs = [s for s in SESS_SUM if s.get('userId') == user_id]
        return jsonify(user_jobs)
    return jsonify(SESS_SUM)

# full details for one session
@app.route('/api/jobs/<int:sid>', methods=['GET'])
def get_session(sid):
    sess = next((s for s in SESS_DET if s['id'] == sid), None)
    if not sess:
        return jsonify({'error': 'Session not found'}), 404
    return jsonify(sess)

# allow posting a new user message & echo a bot reply
@app.route('/api/jobs/<int:sid>/message', methods=['POST'])
def post_message(sid):
    payload = request.get_json() or {}
    text = payload.get('text', '').strip()
    if not text:
        return jsonify({'error': 'Message text required'}), 400

    sess = next((s for s in SESS_DET if s['id'] == sid), None)
    if not sess:
        return jsonify({'error': 'Session not found'}), 404

    # append user message
    sess.setdefault('messages', []).append({'role': 'user', 'text': text})
    # dummy bot reply
    reply = f"Echo: {text}"
    sess['messages'].append({'role': 'bot', 'text': reply})

    return jsonify({'reply': reply}), 201

# --- SESSION CREATION endpoint ---
@app.route('/api/jobs/start', methods=['POST'])
def start_session():
    """Create a new session (analyze or train) and log all session data"""
    data = request.get_json() or {}
    
    print("\n" + "="*80)
    print("🚀 START SESSION ENDPOINT HIT!")
    print("="*80)
    print(f"Session Type: {data.get('sessionType', 'unknown')}")
    print(f"User ID: {data.get('userId', 'unknown')}")
    print(f"Project: {data.get('project', 'unknown')}")
    print(f"Method: {data.get('method', 'unknown')}")
    print(f"Session ID: {data.get('sessionId', 'unknown')}")
    print("-" * 40)
    
    # Log session-specific data
    session_type = data.get('sessionType', '').lower()
    
    if session_type == 'analyze':
        print("📊 ANALYSIS SESSION DATA:")
        print(f"  Knowledge Collection: {data.get('knowledgeCollection', 'N/A')}")
        print(f"  Log Files: {data.get('logFile', 'N/A')}")
        print(f"  Number of Files: {len(data.get('logFiles', []))}")
        
        if data.get('method') == 'keywords':
            print("  🔍 KEYWORD ANALYSIS:")
            print(f"    Error Keywords: {data.get('errorKeywords', [])}")
            print(f"    False Positive Keywords: {data.get('falsePositiveKeywords', [])}")
            print(f"    Hints Keywords: {data.get('hintsKeywords', [])}")
            print(f"    Success Keywords: {data.get('successKeywords', [])}")
            
        elif data.get('method') == 'regex':
            print("  🔧 REGEX ANALYSIS:")
            print(f"    Error Regexes: {data.get('errorRegexes', [])}")
            print(f"    False Positive Regexes: {data.get('falseRegexes', [])}")
            print(f"    Hints Regexes: {data.get('hintsRegexes', [])}")
            print(f"    Success Regexes: {data.get('successRegexes', [])}")
            
        elif data.get('method') == 'neural':
            print("  🧠 NEURAL ANALYSIS:")
            print(f"    Knowledge Collection: {data.get('knowledgeCollection', 'N/A')}")
            
    elif session_type == 'train':
        print("🎯 TRAINING SESSION DATA:")
        print(f"  Knowledge Collection: {data.get('knowledgeCollection', 'N/A')}")
        print(f"  Log Files: {data.get('logFile', 'N/A')}")
        print(f"  Number of Files: {len(data.get('logFiles', []))}")
        
    # Log file contents preview (first 100 chars of each file)
    if data.get('logFiles'):
        print("\n📄 LOG FILE CONTENTS PREVIEW:")
        for i, file_data in enumerate(data.get('logFiles', [])[:3]):  # Show first 3 files
            file_name = file_data.get('name', f'file_{i}')
            file_content = file_data.get('content', '')[:100]
            print(f"  File {i+1}: {file_name}")
            print(f"    Content preview: {repr(file_content)}...")
            
    print("="*80)
    print("✅ Session creation request processed successfully!")
    print("="*80 + "\n")
    
    # Return success response with session ID
    session_id = data.get('sessionId', str(int(100 + 900 * __import__('random').random())))
    
    # Store session data for status endpoint
    session_data[session_id] = {
        'sessionType': data.get('sessionType', 'analyze'),
        'method': data.get('method', 'neural'),
        'userId': data.get('userId'),
        'project': data.get('project'),
        'knowledgeCollection': data.get('knowledgeCollection'),
        'logFiles': data.get('logFiles', []),
        'created_at': __import__('time').time()
    }
    
    return jsonify({
        'sessionId': session_id,
        'status': 'created',
        'message': f'Session {session_id} created successfully'
    }), 201

# --- SESSION CREATION endpoint (legacy) ---
@app.route('/api/jobs', methods=['POST'])
def create_session():
    """Create a new session (analyze or train) and log all session data"""
    data = request.get_json() or {}
    
    print("\n" + "="*80)
    print("🚀 START SESSION ENDPOINT HIT!")
    print("="*80)
    print(f"Session Type: {data.get('sessionType', 'unknown')}")
    print(f"User ID: {data.get('userId', 'unknown')}")
    print(f"Project: {data.get('project', 'unknown')}")
    print(f"Method: {data.get('method', 'unknown')}")
    print(f"Session ID: {data.get('sessionId', 'unknown')}")
    print("-" * 40)
    
    # Log session-specific data
    session_type = data.get('sessionType', '').lower()
    
    if session_type == 'analyze':
        print("📊 ANALYSIS SESSION DATA:")
        print(f"  Knowledge Collection: {data.get('knowledgeCollection', 'N/A')}")
        print(f"  Log Files: {data.get('logFile', 'N/A')}")
        print(f"  Number of Files: {len(data.get('logFiles', []))}")
        
        if data.get('method') == 'keywords':
            print("  🔍 KEYWORD ANALYSIS:")
            print(f"    Error Keywords: {data.get('errorKeywords', [])}")
            print(f"    False Positive Keywords: {data.get('falsePositiveKeywords', [])}")
            print(f"    Hints Keywords: {data.get('hintsKeywords', [])}")
            print(f"    Success Keywords: {data.get('successKeywords', [])}")
            
        elif data.get('method') == 'regex':
            print("  🔧 REGEX ANALYSIS:")
            print(f"    Error Regexes: {data.get('errorRegexes', [])}")
            print(f"    False Positive Regexes: {data.get('falseRegexes', [])}")
            print(f"    Hints Regexes: {data.get('hintsRegexes', [])}")
            print(f"    Success Regexes: {data.get('successRegexes', [])}")
            
        elif data.get('method') == 'neural':
            print("  🧠 NEURAL ANALYSIS:")
            print(f"    Knowledge Collection: {data.get('knowledgeCollection', 'N/A')}")
            
    elif session_type == 'train':
        print("🎯 TRAINING SESSION DATA:")
        print(f"  Knowledge Collection: {data.get('knowledgeCollection', 'N/A')}")
        print(f"  Log Files: {data.get('logFile', 'N/A')}")
        print(f"  Number of Files: {len(data.get('logFiles', []))}")
        
    # Log file contents preview (first 100 chars of each file)
    if data.get('logFiles'):
        print("\n📄 LOG FILE CONTENTS PREVIEW:")
        for i, file_data in enumerate(data.get('logFiles', [])[:3]):  # Show first 3 files
            file_name = file_data.get('name', f'file_{i}')
            file_content = file_data.get('content', '')[:100]
            print(f"  File {i+1}: {file_name}")
            print(f"    Content preview: {repr(file_content)}...")
            
    print("="*80)
    print("✅ Session creation request processed successfully!")
    print("="*80 + "\n")
    
    # Return success response with session ID
    session_id = data.get('sessionId', str(int(100 + 900 * __import__('random').random())))
    return jsonify({
        'sessionId': session_id,
        'status': 'created',
        'message': f'Session {session_id} created successfully'
    }), 201

# Store session start times and data (in real app, this would be in a database)
session_start_times = {}
session_data = {}

@app.route('/api/jobs/<string:session_id>/status', methods=['GET'])
def get_session_status(session_id):
    """Get the current status of a session with simulated progression"""
    import time
    
    # Get session data from stored session information
    session_info = session_data.get(session_id, {})
    session_type = session_info.get('sessionType', 'analyze')
    method = session_info.get('method', 'neural')
    
    # Initialize session start time if not exists
    if session_id not in session_start_times:
        session_start_times[session_id] = time.time()
        print(f"🆕 New session started - ID: {session_id}")
    
    # Calculate elapsed time since session start
    elapsed_time = time.time() - session_start_times[session_id]
    
    print(f"📊 Session Status Check - ID: {session_id}, Type: {session_type}, Method: {method}, Elapsed: {elapsed_time:.1f}s")
    
    # Phase 1: Uploading (0-5 seconds) - Always starts at 0%
    if elapsed_time < 5:
        percentage = int((elapsed_time / 5) * 100)
        return jsonify({
            'sessionId': session_id,
            'status': 'uploading',
            'percentage': percentage,
            'message': f'Uploading files... {percentage}%'
        })
    
    # Phase 2: Waiting for execution (5-7 seconds)
    elif elapsed_time < 7:
        return jsonify({
            'sessionId': session_id,
            'status': 'waiting-for-exec',
            'percentage': 0,
            'message': 'Waiting for Knut Executor...'
        })
    
    # Phase 3: Processing (7-15 seconds) - 8 seconds of processing
    elif elapsed_time < 15:
        percentage = int(((elapsed_time - 7) / 8) * 100)
        
        if session_type == 'analyze':
            if method == 'keywords':
                status = 'analyzing-keywords'
            elif method == 'regex':
                status = 'analyzing-regex'
            else:  # neural
                status = 'analyzing-neural'
        else:  # train
            status = 'training'
        
        return jsonify({
            'sessionId': session_id,
            'status': status,
            'percentage': percentage,
            'message': f'Processing... {percentage}%'
        })
    
    # Phase 4: Completed (15+ seconds)
    else:
        # Clean up the session start time after completion
        if session_id in session_start_times:
            del session_start_times[session_id]
        
        # Clean up session data as well
        if session_id in session_data:
            del session_data[session_id]
        
        return jsonify({
            'sessionId': session_id,
            'status': 'completed',
            'percentage': 100,
            'message': 'Session completed successfully',
            'resultHTML': f'{session_id}-visual-log.html',
            'resultSummary': None  # Will be fetched separately via getSummaryMessage
        })

# File: mock_server/app.py
@app.route('/api/summary-message', methods=['GET'])
def get_summary_message():
    summary_type = request.args.get('type', '').lower()
    if summary_type == 'analysis':
        return jsonify({
            "result": (
                "### Root Cause Analysis\n\n"
                "The root cause is around **line #1392**\n\n"
                "**Error Name:** Build Failure\n\n"
                "**Description:** The build failure happened during the compilation of a package named `sbt-tools` in the ApertisPro system. "
                "The error occurred due to the missing dependency `dh` in the system. "
                "It could not find the file `dh` while executing the make command, which caused the **Error 127** when executing the Debian package build rule.\n\n"
                "**Solution:** To fix the issue, we can install the dependency `dh` on the system where the package is being compiled. "
                "The developer can install it using the package manager command from the terminal:\n\n"
                "`sudo apt-get install dh`\n\n"
                "After installing the dependency, the developer should recompile the package and execute the Debian package build rule again."
            )
        })
    elif summary_type == 'train':
        return jsonify({
            "result": (
                "### Training Summary\n\n"
                "**Status:** Training complete\n\n"
                "**Results:**\n"
                "- The collection was updated with **17** new error patterns\n"
                "- **2032** log lines processed successfully\n"
                "- No severe data issues were detected\n\n"
                "Your changes are now available for enhanced log analysis."
            )
        })
    return jsonify({"result": ""}), 400

@app.route('/api/user-projects', methods=['GET'])
def get_user_projects():
    # For demo, get user_id from query or default to first user
    user_id = request.args.get('user_id', type=int)
    if user_id is None:
        # fallback: use first user
        user = USERS[0] if isinstance(USERS, list) and USERS else USERS
    else:
        user = next((u for u in USERS if u['id'] == user_id), None)
    if not user:
        return jsonify({"error": "User not found"}), 404
    # Collect project metadata from projects.json
    available_ids = set(user.get('projects', []))
    projects = [proj for proj in PROJECTS if proj['id'] in available_ids]
    return jsonify(projects)

@app.route('/api/projects/<int:pid>/collections', methods=['GET'])
def get_project_collections(pid):
    if isinstance(COLLS, list):
        colls = [c for c in COLLS if c['project_id'] == pid]
    else:
        colls = []
    return jsonify(colls)

@app.route('/api/vocab/<string:vocab_type>/<string:variant>', methods=['GET'])
def get_vocabulary(vocab_type, variant):
    # vocab_type: 'keywords' or 'regex'
    # variant: 'error', 'false_positive', 'hints', or 'success'
    vocab_type = vocab_type.lower()
    variant = variant.lower()
    file_map = VOCAB_MAP.get(vocab_type)
    if not file_map:
        return jsonify({"error": "Invalid vocab type"}), 400
    filename = file_map.get(variant)
    if not filename:
        return jsonify({"error": "Invalid variant"}), 400
    try:
        vocab_data = load_json(filename)
        return jsonify(vocab_data), 200
    except Exception as e:
        return jsonify({"error": f"Could not load vocabulary: {str(e)}"}), 500

if __name__ == '__main__':
    # http://localhost:5001
    app.run(debug=True, port=5123)