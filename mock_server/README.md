# KnutGPT Mock Server

A Flask-based mock backend server that simulates the KnutGPT API for development and testing purposes. This server provides realistic API responses and data structures to support frontend development without requiring the full production backend.

## Overview

The mock server provides a complete simulation of the KnutGPT backend API, including:

- **User Authentication**: Login/logout with mock user accounts
- **Project Management**: CRUD operations for projects and collections
- **Session Handling**: Analysis and training session lifecycle
- **Real-time Status**: Simulated progress tracking with realistic delays
- **File Upload**: Mock file processing and analysis
- **Vocabulary Management**: Keywords and regex pattern storage

## Architecture

### Tech Stack
- **Flask** - Lightweight Python web framework
- **Flask-CORS** - Cross-Origin Resource Sharing support
- **JSON** - File-based data storage for mock data
- **Python 3.8+** - Core runtime environment

### Project Structure
```
mock_server/
├── app.py              # Main Flask application
├── data/               # Mock data files (JSON)
│   ├── users.json      # User accounts and credentials
│   ├── projects.json   # Project information
│   ├── knowledge_collections.json
│   ├── sessions_summary.json
│   ├── sessions_details.json
│   ├── keywords_*.json # Vocabulary files
│   └── regex_*.json    # Regex pattern files
└── README.md           # This file
```

## Key Features

### Authentication System
- Mock user accounts with different roles (user, maintainer)
- Project-based access control simulation
- Session-based authentication (simplified for development)

### API Endpoints

#### User & Authentication
- `POST /api/login` - User authentication
- `GET /api/user` - Current user information

#### Projects & Collections
- `GET /api/projects` - List all projects
- `GET /api/projects/:id` - Get specific project
- `GET /api/projects/:id/collections` - Project collections
- `GET /api/collections` - List all knowledge collections
- `GET /api/collections/:id` - Get specific collection

#### Sessions (Analysis & Training)
- `POST /api/sessions/start` - Start new session
- `POST /api/sessions` - Legacy session creation
- `GET /api/sessions` - List sessions (with user filtering)
- `GET /api/sessions/:id` - Get session details
- `GET /api/sessions/:id/status` - Real-time status updates
- `POST /api/sessions/:id/message` - Send message to session

#### Vocabulary & Patterns
- `GET /api/vocab/:type/:variant` - Get vocabulary (keywords/regex)

### Session Simulation

The mock server provides realistic session progression simulation:

1. **Uploading Phase** (0-5s): File upload simulation with progress
2. **Processing Phase** (5-7s): Quick transition phase  
3. **Analysis Phase** (7-15s): Main processing with progress updates
4. **Completion** (15s+): Final results with HTML links

### Data Storage

Mock data is stored in JSON files in the `data/` directory:

- **users.json**: User accounts with roles and project access
- **projects.json**: Project metadata and descriptions
- **knowledge_collections.json**: Analysis pattern collections
- **sessions_*.json**: Session history and details
- **vocabulary files**: Keywords and regex patterns by type

## 🚀 Getting Started

### Prerequisites
- Python 3.8+
- pip package manager
- Virtual environment (recommended)

### Installation
```bash

# Install dependencies
pip install flask flask-cors

# Run the server
python app.py
```

The server will start on `http://localhost:5001`

### Development Setup
```bash
# Enable debug mode for auto-reload
export FLASK_DEBUG=1  # On Windows: set FLASK_DEBUG=1
python app.py
```

## Mock Data Structure

### Users
```json
{
  "id": 1,
  "username": "<EMAIL>",
  "password": "password123",
  "role": "maintainer",
  "projects": [1, 2, 3, 4]
}
```

### Projects
```json
{
  "id": 1,
  "name": "Titan",
  "description": "Titan Embedded Project"
}
```

### Sessions
```json
{
  "id": 1,
  "sessionId": "S101",
  "userId": 1,
  "sessionType": "analyze",
  "method": "neural",
  "SessionStatus": "Complete",
  "resultMessage": "Analysis complete...",
  "resultHTML": "S101-visual-log.html"
}
```

## Configuration

### Environment Variables
- `FLASK_DEBUG`: Enable debug mode (default: False)
- `PORT`: Server port (default: 5001)

### CORS Configuration
The server is configured to allow all origins for development:
```python
CORS(app)  # Allows all origins
```

## Development Features

### Detailed Logging
The mock server provides extensive console logging for debugging:
- Session creation details
- File upload information
- Status progression tracking
- API endpoint access logs

### Error Simulation
The server can simulate various error conditions:
- Invalid credentials
- Missing resources (404 errors)
- Processing failures
- Timeout scenarios

### Realistic Delays
Session processing includes realistic delays to simulate actual backend processing time, helping with frontend loading state testing.

## API Integration

### Request/Response Format
All API responses follow a consistent format:
```json
{
  "data": {...},
  "status": "success"
}
```

Error responses:
```json
{
  "error": "Error message",
  "status": "error"
}
```

### File Upload Handling
The server accepts file uploads and logs file contents for debugging:
- File name and size logging
- Content preview (first 100 characters)
- MIME type detection

## Security Notes

**Development Only**: This mock server is designed for development and testing only. It includes:
- Hardcoded credentials
- No encryption
- Simplified authentication
- Open CORS policy

**Do not use in production!**

## Testing Support

The mock server is ideal for:
- Frontend development without backend dependencies
- API integration testing
- UI/UX testing with realistic data
- Load testing with simulated delays
- Error handling testing

## Adding New Mock Data

To add new mock data:

1. **Add JSON files** to the `data/` directory
2. **Load data** in `app.py` using `load_json()`
3. **Create endpoints** to serve the data
4. **Update documentation** with new endpoints

Example:
```python
# Load new data
NEW_DATA = load_json('new_data.json')

# Create endpoint
@app.route('/api/new-endpoint', methods=['GET'])
def get_new_data():
    return jsonify(NEW_DATA)
```

## Production Deployment

For production use, replace this mock server with:
- Proper database storage
- Real authentication system
- Secure API endpoints
- Production-grade error handling
- Logging and monitoring

---

For frontend setup and integration, see the `knut-gpt-app/README.md` file.
