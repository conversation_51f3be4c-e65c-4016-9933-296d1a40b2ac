[".*abort.*", ".*bad.*", ".*break.*", ".*cannot open.*", ".*corrupt.*", ".*crash.*", ".*critical.*", ".*damage.*", ".*defect.*", ".*degrade.*", ".*deny.*", ".*disconnect.*", ".*disrupt.*", ".*diverge.*", ".*does not exist.*", ".*errno.*", ".*error.*", ".*exceed.*", ".*exception.*", ".*expire.*", ".*fail.*", ".*failure.*", ".*fatal.*", ".*fault.*", ".*faulty.*", ".*forbidden.*", ".*halt.*", ".*impair.*", ".*inaccessible.*", ".*incompatible.*", ".*inoperative.*", ".*interrupt.*", ".*invalid.*", ".*malformed.*", ".*malfunction.*", ".*misalign.*", ".*misbehave.*", ".*miscalculate.*", ".*mismatch.*", ".*traceback.*", ".*missing.*", ".*cannot run.*", ".*not found.*", ".*null.*", ".*obstruct.*", ".*out of range.*", ".*overflow.*", ".*problem.*", ".*refuse.*", ".*raise.*", ".*reject.*", ".*stop.*", ".*terminate.*", ".*timeout.*", ".*unable.*", ".*unauthorized.*", ".*unavailable.*", ".*underflow.*", ".*unexpected.*", ".*unreachable.*", ".*unsound.*", ".*unstable.*", ".*violate.*"]