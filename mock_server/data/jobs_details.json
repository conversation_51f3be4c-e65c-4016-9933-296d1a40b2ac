[{"id": 1, "jobId": "101", "userId": 1, "jobType": "analyze", "projectId": 1, "project": "Titan", "method": "<PERSON><PERSON><PERSON>", "jobStatus": "Complete", "knowledgeCollectionId": 1, "knowledgeCollection": "General Titan Collection", "logFile": ["system.log"], "resultMessage": "Found error on line 123...", "resultHTML": "URL-HTML-Link...", "expire": "Date and time when job expires"}, {"id": 2, "jobId": "102", "userId": 1, "jobType": "train", "projectId": 2, "project": "Kodiak", "method": "n/a", "logFile": ["app.log"], "jobStatus": "Complete", "resultMessage": "Succesful training message...", "resultHTML": "no HTML link for training jobs", "expire": "Date and time when job expires"}]