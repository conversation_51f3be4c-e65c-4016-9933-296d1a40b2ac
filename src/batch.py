import argparse
import sys
from pathlib import Path
from traceback import format_exc
from typing import List

from analyser.analyser import <PERSON>gAnalys<PERSON>, get_main_logger, run_analysis
from analyser.streamer.streamer_factory import StreamerFactory
from analyser.version import VERSION
from utils.default_config import DEFAULT_CONFIG_FILE
from utils.time_monitor import TimeMonitor
from visualiser.visualiser import gen_visualisation

NAME = "BatchAnalysisTool"

def parse_arguments() -> argparse.Namespace:
    parser = argparse.ArgumentParser(
        description="Batch process multiple files matching a pattern, e.g. c:\\files log-*"
    )
    parser.add_argument("folder", help="Folder containing files to process")
    parser.add_argument(
        "filepattern", nargs="?", default="log-*", help="File pattern to match (default: log-*)"
    )
    parser.add_argument(
        "--analysis_type", default="neural", help="Analysis type (default: neural)"
    )
    parser.add_argument(
        "--match_at", default=0.95, help="Match threshold (default: 0.95)"
    )
    parser.add_argument(
        "--output_dir", default="../output", help="Directory for output files (default: ./)"
    )
    parser.add_argument(
        "--collection", default="good_c", help="The collection to use (default: good_collection)"
    )
    parser.add_argument("--verbose", action="store_true", help="Enable verbose output")
    parser.add_argument("--training", action="store_true", help="If set, will run in training mode")
    parser.add_argument("--ini_config", default=DEFAULT_CONFIG_FILE, help="Path to the configuration file")
    return parser.parse_args()


def get_matching_files(folder: str, pattern: str) -> List[str]:
    """Get all files matching the pattern in the specified folder."""
    folder_path = Path(folder)
    return [str(p) for p in folder_path.glob(pattern)]


def get_streamer(file_path, logger):
    return StreamerFactory().create(
        streamer_type='file',
        file_path=file_path,
        logger=logger,
        timeout=None,
        trigger=True
    )


def process_file(
        file_path: str,
        prompt_output: str,
        json_results: str,
        analysis_type: str,
        match_at: float,
        verbose: bool,
        collection: str,
        training: bool = False,
        ini_config: Path = DEFAULT_CONFIG_FILE,
        logger=None):
    """Process a single file with the LogAnalyser."""
    try:
        streamer = None
        if analysis_type == 'neural':
            streamer = get_streamer(file_path, logger)

        analyser = LogAnalyser(
            input=file_path,
            analysis_type=analysis_type,
            match_at=match_at,
            verbose=verbose,
            output=json_results,
            prompt_output=prompt_output,
            training_mode=training,
            logger=logger,
            ini_config=ini_config,
            collection=collection,
            streamer=streamer,
        )
        run_analysis(analyser)
        return True

    except Exception as e:
        logger.error(f"{NAME}: Error processing {file_path}: {e}, {format_exc()}")
        if verbose:
            import traceback
            traceback.print_exc()
        return False


def get_logger(args):
    logger = get_main_logger()
    logger.logger.setLevel(logger.INFO)
    if args.verbose:
        logger.logger.setLevel(logger.DEBUG)
    return logger


def main():
    args = parse_arguments()
    logger = get_logger(args)

    output_dir_path = Path(args.output_dir)
    output_dir_path.mkdir(parents=True, exist_ok=True)
    files = get_matching_files(args.folder, args.filepattern)
    files_count = len(files)

    logger.info(f":::::::::: {NAME} {VERSION} ::::::::::")

    if not files:
        logger.error(f"{NAME}: No files matching '{args.filepattern}' found in '{args.folder}'")
        return 1

    logger.info(f"{NAME}: Found {len(files)} files to process")

    success_count = 0
    with TimeMonitor(f"{NAME}: --- Processing of all files took: ", logger):
        for file_pos, file_path in enumerate(files, start=1):
            with TimeMonitor(f"{NAME}: --- Processing of '{file_path}' file took: ", logger):
                try:
                    logger.info(f"{NAME} --- Processing file {file_pos} ot of {files_count}: '{file_path}'")
                    file_path_obj = Path(file_path)
                    file_name = file_path_obj.name
                    base_name = file_path_obj.stem
                    visualisation_file = str(Path(output_dir_path / f"{base_name}_visualisation.html"))
                    prompt_file = str(Path(output_dir_path / f"{base_name}_prompt.txt"))
                    json_results = str(Path(output_dir_path / f"{base_name}_result_{args.analysis_type}.json"))
                    process_successful = process_file(
                        file_path=file_path,
                        prompt_output=prompt_file,
                        json_results=json_results,
                        analysis_type=args.analysis_type,
                        match_at=args.match_at,
                        verbose=args.verbose,
                        collection=args.collection,
                        ini_config=args.ini_config,
                        training=args.training,
                        logger=logger
                    )
                    if process_successful:
                        logger.debug(f"{NAME}: Successfully processed: {file_name}, "
                                     f"JSON result: {json_results}, "
                                     f"Prompt result: {prompt_file}")
                        # Generate HTML visualisation if we have a result file
                        html = gen_visualisation(
                            input_file=file_path,
                            results_file=json_results,
                            output=visualisation_file,
                            compact=True,
                            compact_from=2000,
                            compact_max_normal_lines=10,
                            compact_context_lines=5,
                            llm_response_file=None,
                            gzip=True,
                            verbose=args.verbose,
                            logger=logger)
                        if html:
                            logger.debug(f"{NAME}: HTML visualisation generated: {visualisation_file}")
                            logger.info(f"{NAME} --- File: '{file_path}' was processed successfully.")
                            success_count += 1
                        else:
                            logger.warning(f"{NAME}: Failed to generate HTML visualisation for {file_name}")
                    else:
                        logger.warning(f"{NAME}: Failed to process file: {file_name}")
                except Exception as e:
                    logger.error(f"{NAME}: Error processing file '{file_path}': {e}, {format_exc()}")
                    raise e
        logger.info(f"{NAME}: Out of {files_count} files, {success_count} files were processed successfully, and {files_count - success_count} files failed being processing.")
    return 0


if __name__ == "__main__":
    sys.exit(main())
