import json
import os
import sys
from hashlib import md5

from config.constants import MAX_API_UPLOAD_SIZE

sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

from flask import Flask, request, redirect, jsonify, Blueprint
from flask_cors import CORS
from flasgger import Swagger
import flask_monitoringdashboard as dashboard
from auth_manager import token_required, AuthManager
from repository import Repository
from qdrant_manager import QdrantManager
from job_manager import JobManager
from utils.logger import Logger
from vocabulary_manager import VocabularyManager


app = Flask(__name__)
# Set the maximum file size to 5GB, just for safety reasons so we don't get users to upload petabytes of data
app.config["MAX_CONTENT_LENGTH"] = 5 * 1024 * 1024 * 1024
# Explicitly allow all CORS requests
CORS(app, resources={r"/*": {"origins": "*"}})
# URL prefix for the API
api_bp = Blueprint("api", __name__, url_prefix="/api")
# Initialize Swagger for API documentation
swagger = Swagger(app)
# Initialize Flask-MonitoringDashboard
dashboard.bind(app)

logger = Logger(name="API").logger
repo = Repository()
qm = QdrantManager()
job_manager = JobManager()
vocab_manager = VocabularyManager()

MAIN_COLLECTION_FORMAT = "{project}_main"

API_DEVELOPMENT = os.getenv("API_DEVELOPMENT", "true").lower() == "true"


@api_bp.route("/")
def default_route():
    """
    Default API route
    ---
    tags:
      - General
    responses:
      200:
        description: API version information or redirect to documentation
        schema:
          type: object
          properties:
            KnutGPT_api_version:
              type: string
              description: API version number
      302:
        description: Redirect to API documentation (development mode)
    """
    if API_DEVELOPMENT:
        logger.info("Redirecting to API documentation in development mode")
        return redirect("/apidocs")
    return jsonify({"KnutGPT_api_version": "1.0.0"}), 200


@api_bp.route("/login", methods=["POST"])
def login():
    """
    User authentication
    ---
    tags:
      - Authentication
    parameters:
      - name: credentials
        in: body
        required: true
        schema:
          type: object
          properties:
            username:
              type: string
              description: User email/username
            password:
              type: string
              description: User password
          required:
            - username
            - password
    responses:
      200:
        description: Authentication successful
        schema:
          type: object
          properties:
            access_token:
              type: string
              description: JWT access token
            user:
              type: object
              properties:
                id:
                  type: integer
                username:
                  type: string
                role:
                  type: string
                projects:
                  type: array
                  items:
                    type: integer
      401:
        description: Invalid credentials
      400:
        description: Missing username or password
    """
    data = request.get_json() or {}
    return AuthManager().login(data)


@api_bp.route("/user-projects", methods=["GET"])
@token_required
def get_projects():
    """
    Get user projects based on user groups
    ---
    tags:
      - Projects
    parameters:
      - name: Authorization
        in: header
        type: string
        required: true
        description: Bearer token for authentication
    responses:
      200:
        description: List of user projects filtered by user groups
        schema:
          type: array
          items:
            type: object
            properties:
              id:
                type: integer
                description: Project ID
              name:
                type: string
                description: Project name
              description:
                type: string
                description: Project description
              groups:
                type: array
                items:
                  type: string
                description: Groups associated with the project
              cost_center:
                type: string
                description: Project cost center
      401:
        description: Unauthorized access (token missing or invalid)
    """
    # Get the current user from the token (set by @token_required decorator)
    current_user = getattr(request, "current_user", {})
    username = current_user.get("username", "unknown")
    logger.debug(f"Current user info: {current_user}")
    # Get user groups so we can filter projects accordingly
    user_groups = current_user.get("groups", [])
    accessible_projects = repo.get_projects_by_groups(user_groups)
    logger.info(f"Projects request served for {username}: {len(accessible_projects)} projects")
    # logger.info(f"Projects request served for {username}: {len(accessible_projects)} projects: {accessible_projects}")
    return jsonify(accessible_projects), 200


@api_bp.route("/projects/<int:pid>/collections", methods=["GET"])
@token_required
def get_project_collections(pid):
    """
    Get collections for a specific project, filtered by project name and user access
    ---
    tags:
        - Collections
    parameters:
        - name: Authorization
          in: header
          type: string
          required: true
          description: Bearer token for authentication
        - name: pid
          in: path
          type: integer
          required: true
          description: Project ID to fetch collections for
    responses:
        200:
            description: Collections for the project separated into project and user collections
            schema:
                type: object
                properties:
                    project_collections:
                        type: array
                        description: Main project collections
                        items:
                            type: object
                            properties:
                                name:
                                    type: string
                                    description: Collection name
                                status:
                                    type: string
                                    description: Collection status
                                points_count:
                                    type: integer
                                    description: Number of points in collection
                                project_id:
                                    type: integer
                                    description: Associated project ID
                                vectors:
                                    type: object
                                    description: Vector configuration
                                id:
                                    type: string
                                    description: MD5 hash of collection name
                                last_updated:
                                    type: string
                                    format: date-time
                                    description: Last update timestamp (hardcoded)
                                expiration:
                                    type: string
                                    format: date-time
                                    description: Expiration timestamp (hardcoded)
                                owner_user_id:
                                    type: integer
                                    nullable: true
                                    description: Owner user ID (always null currently)
                    user_collections:
                        type: array
                        description: User-specific collections starting with username
                        items:
                            type: object
                            properties:
                                name:
                                    type: string
                                    description: Collection name
                                status:
                                    type: string
                                    description: Collection status
                                points_count:
                                    type: integer
                                    description: Number of points in collection
                                project_id:
                                    type: integer
                                    description: Associated project ID
                                vectors:
                                    type: object
                                    description: Vector configuration
                                id:
                                    type: string
                                    description: MD5 hash of collection name
                                last_updated:
                                    type: string
                                    format: date-time
                                    description: Last update timestamp (hardcoded)
                                expiration:
                                    type: string
                                    format: date-time
                                    description: Expiration timestamp (hardcoded)
                                owner_user_id:
                                    type: integer
                                    nullable: true
                                    description: Owner user ID (always null currently)
        401:
            description: Unauthorized access (token missing or invalid)
        404:
            description: Project not found
    """
    project = repo.get_project_by_id(pid)
    project_name = project.get("name").replace(" ", "").lower()
    logger.info(f"Fetching collections for project: {project_name}")
    collections = qm.get_all_collections()
    current_user = getattr(request, "current_user", {})
    username = current_user.get("username", "unknown")
    main_collection = MAIN_COLLECTION_FORMAT.format(project=project_name)
    project_collections = []
    user_collections = []
    for c in collections:
        if project_name in c:
            # Get only the relevant collections for the project and user
            if main_collection in c or c.startswith(username):
                collection_info = qm.get_collection_info(c)
                collection_dict = {
                    "name": c,
                    "status": collection_info.get("status", "unknown"),
                    "points_count": collection_info.get("points_count", 0),
                    "project_id": pid,
                    "vectors": collection_info.get("config", {}).get("params", {}).get("vectors", {}),
                    "id": md5(c.encode("utf-8")).hexdigest(),
                    # TODO: Use tiny jsondb_data for collection metadata
                    "last_updated": "2024-05-18T18:00:00Z",
                    "expiration": "2025-10-01T00:00:00Z",
                    "owner_user_id": None,
                }
                if main_collection in c:
                    project_collections.append(collection_dict)
                if c.startswith(username):
                    user_collections.append(collection_dict)
    result = {"project_collections": project_collections, "user_collections": user_collections}
    logger.debug(f"Collections info: {json.dumps(result, indent=2)}")
    return jsonify(result), 200


@api_bp.route("/refresh-token", methods=["POST"])
@token_required
def refresh_token():
    """
    Refresh authentication token to extend session lifetime
    ---
    tags:
      - Authentication
    parameters:
      - name: Authorization
        in: header
        type: string
        required: true
        description: Bearer token for authentication
    responses:
      200:
        description: Token refreshed successfully
        schema:
          type: object
          properties:
            access_token:
              type: string
              description: New JWT access token
            message:
              type: string
              description: Success message
      401:
        description: Unauthorized access (token missing or invalid)
    """
    # Get the current user from the token (set by @token_required decorator)
    current_user = getattr(request, "current_user", {})
    username = current_user.get("username", "unknown")

    # Create new token with extended expiration
    auth_manager = AuthManager()
    payload = {
        "user_id": current_user.get("id"),
        "username": username,
        "role": current_user.get("role"),
        "groups": current_user.get("groups", []),
    }

    # Generate new token with fresh expiration date
    token = auth_manager.generate_token(payload)

    logger.info(f"Token refreshed for user {username}")
    return jsonify({"access_token": token, "message": "Token refreshed successfully"}), 200


# --- JOB MANAGEMENT ENDPOINTS ---

@api_bp.route("/jobs", methods=["GET"])
@token_required
def list_jobs():
    """
    List jobs for the current user
    ---
    tags:
      - Jobs
    parameters:
      - name: Authorization
        in: header
        type: string
        required: true
        description: Bearer token for authentication
      - name: user_id
        in: query
        type: integer
        required: false
        description: User ID to filter jobs (optional)
    responses:
      200:
        description: List of jobs
        schema:
          type: array
          items:
            type: object
            properties:
              id:
                type: string
                description: Job ID
              jobId:
                type: string
                description: Job ID (compatibility)
              userId:
                type: integer
                description: User ID
              project:
                type: string
                description: Project name
              jobType:
                type: string
                description: Job type (analyze or train)
              method:
                type: string
                description: Analysis method
              status:
                type: string
                description: Job status
      401:
        description: Unauthorized access
    """
    current_user = getattr(request, "current_user", {})
    user_id = current_user.get("id")

    # Allow filtering by user_id for admin users
    requested_user_id = request.args.get('user_id', type=int)
    if requested_user_id and current_user.get('role') == 'maintainer':
        user_id = requested_user_id

    try:
        jobs = job_manager.list_jobs(user_id)
        username = current_user.get("username", "unknown")
        logger.info(f"Listed {len(jobs)} jobs for user {username} (ID: {user_id})")
        return jsonify(jobs), 200
    except Exception as e:
        logger.error(f"Error listing jobs: {str(e)}")
        return jsonify({"error": "Failed to list jobs"}), 500


@api_bp.route("/jobs/<string:job_id>", methods=["GET"])
@token_required
def get_job(job_id):
    """
    Get job details by ID
    ---
    tags:
      - Jobs
    parameters:
      - name: Authorization
        in: header
        type: string
        required: true
        description: Bearer token for authentication
      - name: job_id
        in: path
        type: string
        required: true
        description: Job ID
    responses:
      200:
        description: Job details
      401:
        description: Unauthorized access
      404:
        description: Job not found
    """
    if not job_manager.verify_user_access(job_id):
        return jsonify({"error": "Unauthorized access"}), 403

    job = job_manager.get_job_details(job_id)
    if not job:
        return jsonify({"error": "Job not found"}), 404

    return jsonify(job), 200


@api_bp.route("/jobs/start", methods=["POST"])
@token_required
def create_job():
    """
    Create a new job
    ---
    tags:
      - Jobs
    parameters:
      - name: Authorization
        in: header
        type: string
        required: true
        description: Bearer token for authentication
      - name: job_data
        in: body
        required: true
        schema:
          type: object
          properties:
            sessionType:
              type: string
              description: Job type (analyze or train)
            method:
              type: string
              description: Analysis method
            project:
              type: string
              description: Project name
            logFiles:
              type: array
              description: Log files data
    responses:
      201:
        description: Job created successfully
      400:
        description: Invalid job data
      401:
        description: Unauthorized access
    """
    try:
        job_data = request.get_json() or {}
        result = job_manager.create_job(job_data)

        current_user = getattr(request, "current_user", {})
        username = current_user.get("username", "unknown")
        logger.info(f"Job created by {username}: {result.get('sessionId')}")

        return jsonify(result), 201
    except Exception as e:
        logger.error(f"Error creating job: {str(e)}")
        return jsonify({"error": "Failed to create job"}), 500


@api_bp.route("/jobs/<string:job_id>/status", methods=["GET"])
@token_required
def get_job_status(job_id):
    """
    Get job status
    ---
    tags:
      - Jobs
    parameters:
      - name: Authorization
        in: header
        type: string
        required: true
        description: Bearer token for authentication
      - name: job_id
        in: path
        type: string
        required: true
        description: Job ID
    responses:
      200:
        description: Job status
      401:
        description: Unauthorized access
      404:
        description: Job not found
    """
    if not job_manager.verify_user_access(job_id):
        return jsonify({"error": "Unauthorized access"}), 403

    try:
        status = job_manager.get_job_status(job_id)
        return jsonify(status), 200
    except Exception as e:
        logger.error(f"Error getting job status: {str(e)}")
        return jsonify({"error": "Failed to get job status"}), 500


@api_bp.route("/jobs/<string:job_id>", methods=["DELETE"])
@token_required
def delete_job(job_id):
    """
    Delete a job
    ---
    tags:
      - Jobs
    parameters:
      - name: Authorization
        in: header
        type: string
        required: true
        description: Bearer token for authentication
      - name: job_id
        in: path
        type: string
        required: true
        description: Job ID
    responses:
      200:
        description: Job deleted successfully
      401:
        description: Unauthorized access
      404:
        description: Job not found
    """
    if not job_manager.verify_user_access(job_id):
        return jsonify({"error": "Unauthorized access"}), 403

    try:
        if job_manager.delete_job(job_id):
            current_user = getattr(request, "current_user", {})
            username = current_user.get("username", "unknown")
            logger.info(f"Job {job_id} deleted by {username}")
            return jsonify({"message": "Job deleted successfully"}), 200
        else:
            return jsonify({"error": "Job not found"}), 404
    except Exception as e:
        logger.error(f"Error deleting job: {str(e)}")
        return jsonify({"error": "Failed to delete job"}), 500


@api_bp.route("/summary-message", methods=["GET"])
@token_required
def get_summary_message():
    """
    Get analysis or training summary message
    ---
    tags:
      - Jobs
    parameters:
      - name: Authorization
        in: header
        type: string
        required: true
        description: Bearer token for authentication
      - name: type
        in: query
        type: string
        required: true
        description: Summary type ('analysis' or 'train')
    responses:
      200:
        description: Summary message retrieved successfully
        schema:
          type: object
          properties:
            result:
              type: string
              description: Formatted summary message
      400:
        description: Invalid summary type
      401:
        description: Unauthorized access
    """
    summary_type = request.args.get('type', '').lower()

    if summary_type == 'analysis':
        return jsonify({
            "result": (
                "### Root Cause Analysis\n\n"
                "The root cause is around **line #1392**\n\n"
                "**Error Name:** Build Failure\n\n"
                "**Description:** The build failure happened during the compilation of a package named `sbt-tools` in the ApertisPro system. "
                "The error occurred due to the missing dependency `dh` in the system. "
                "It could not find the file `dh` while executing the make command, which caused the **Error 127** when executing the Debian package build rule.\n\n"
                "**Solution:** To fix the issue, we can install the dependency `dh` on the system where the package is being compiled. "
                "The developer can install it using the package manager command from the terminal:\n\n"
                "`sudo apt-get install dh`\n\n"
                "After installing the dependency, the developer should recompile the package and execute the Debian package build rule again."
            )
        })
    elif summary_type == 'train':
        return jsonify({
            "result": (
                "### Training Summary\n\n"
                "**Status:** Training complete\n\n"
                "**Results:**\n"
                "- The collection was updated with **17** new error patterns\n"
                "- **5** new success patterns were identified\n"
                "- **3** false positive patterns were filtered out\n\n"
                "**Performance:**\n"
                "- Training accuracy: **94.2%**\n"
                "- Validation accuracy: **91.8%**\n"
                "- Processing time: **2.3 seconds**\n\n"
                "**Next Steps:**\n"
                "The updated collection is now ready for use in future analysis sessions. "
                "The new patterns will help improve detection accuracy for similar log files."
            )
        })
    else:
        return jsonify({"error": "Invalid summary type. Use 'analysis' or 'train'"}), 400


# Vocabulary Management Endpoints



@api_bp.route("/vocab/<string:vocab_type>/<string:variant>", methods=["GET"])
@token_required
def get_vocabulary(vocab_type, variant):
    """
    Get vocabulary for user (merged defaults + user customizations)
    ---
    tags:
      - Vocabulary
    parameters:
      - name: Authorization
        in: header
        type: string
        required: true
        description: Bearer token for authentication
      - name: vocab_type
        in: path
        type: string
        required: true
        description: Vocabulary type ('keywords' or 'regex')
      - name: variant
        in: path
        type: string
        required: true
        description: Vocabulary variant ('error', 'false_positive', 'hints', 'success')
    responses:
      200:
        description: Vocabulary retrieved successfully
        schema:
          type: array
          items:
            type: string
      400:
        description: Invalid vocabulary type or variant
      401:
        description: Unauthorized access
    """
    vocab_type = vocab_type.lower()
    variant = variant.lower()

    # Validate inputs
    valid_types = ['keywords', 'regex']
    valid_variants = ['error', 'false_positive', 'hints', 'success']

    if vocab_type not in valid_types:
        return jsonify({"error": f"Invalid vocab type. Must be one of: {valid_types}"}), 400

    if variant not in valid_variants:
        return jsonify({"error": f"Invalid variant. Must be one of: {valid_variants}"}), 400

    try:
        current_user = getattr(request, "current_user", {})
        user_id = current_user.get("id")

        if not user_id:
            return jsonify({"error": "User not found"}), 401

        vocabulary = vocab_manager.get_user_vocabulary(user_id, vocab_type, variant)

        logger.info(f"Retrieved {vocab_type}.{variant} vocabulary for user {current_user.get('username', user_id)} ({len(vocabulary)} items)")

        return jsonify(vocabulary), 200

    except Exception as e:
        logger.error(f"Error retrieving vocabulary {vocab_type}.{variant}: {str(e)}")
        return jsonify({"error": "Failed to retrieve vocabulary"}), 500


@api_bp.route("/vocab/<string:vocab_type>/<string:variant>", methods=["PUT"])
@token_required
def save_vocabulary(vocab_type, variant):
    """
    Save user's vocabulary (auto-save functionality)
    ---
    tags:
      - Vocabulary
    parameters:
      - name: Authorization
        in: header
        type: string
        required: true
        description: Bearer token for authentication
      - name: vocab_type
        in: path
        type: string
        required: true
        description: Vocabulary type ('keywords' or 'regex')
      - name: variant
        in: path
        type: string
        required: true
        description: Vocabulary variant ('error', 'false_positive', 'hints', 'success')
      - name: body
        in: body
        required: true
        schema:
          type: array
          items:
            type: string
    responses:
      200:
        description: Vocabulary saved successfully
      400:
        description: Invalid input
      401:
        description: Unauthorized access
    """
    vocab_type = vocab_type.lower()
    variant = variant.lower()

    # Validate inputs
    valid_types = ['keywords', 'regex']
    valid_variants = ['error', 'false_positive', 'hints', 'success']

    if vocab_type not in valid_types:
        return jsonify({"error": f"Invalid vocab type. Must be one of: {valid_types}"}), 400

    if variant not in valid_variants:
        return jsonify({"error": f"Invalid variant. Must be one of: {valid_variants}"}), 400

    try:
        data = request.get_json()
        if not isinstance(data, list):
            return jsonify({"error": "Request body must be an array of strings"}), 400

        # Validate that all items are strings
        if not all(isinstance(item, str) for item in data):
            return jsonify({"error": "All vocabulary items must be strings"}), 400

        current_user = getattr(request, "current_user", {})
        user_id = current_user.get("id")
        username = current_user.get("username", "unknown")

        if not user_id:
            return jsonify({"error": "User not found"}), 401

        success = vocab_manager.save_user_vocabulary(user_id, username, vocab_type, variant, data)

        if success:
            logger.info(f"Saved {vocab_type}.{variant} vocabulary for user {username} ({len(data)} items)")
            return jsonify({"message": "Vocabulary saved successfully"}), 200
        else:
            return jsonify({"error": "Failed to save vocabulary"}), 500

    except Exception as e:
        logger.error(f"Error saving vocabulary {vocab_type}.{variant}: {str(e)}")
        return jsonify({"error": "Failed to save vocabulary"}), 500


@api_bp.route("/vocab/<string:vocab_type>/<string:variant>", methods=["DELETE"])
@token_required
def reset_vocabulary(vocab_type, variant):
    """
    Reset user's vocabulary to defaults
    ---
    tags:
      - Vocabulary
    parameters:
      - name: Authorization
        in: header
        type: string
        required: true
        description: Bearer token for authentication
      - name: vocab_type
        in: path
        type: string
        required: true
        description: Vocabulary type ('keywords' or 'regex')
      - name: variant
        in: path
        type: string
        required: true
        description: Vocabulary variant ('error', 'false_positive', 'hints', 'success')
    responses:
      200:
        description: Vocabulary reset to defaults successfully
      400:
        description: Invalid vocabulary type or variant
      401:
        description: Unauthorized access
    """
    vocab_type = vocab_type.lower()
    variant = variant.lower()

    # Validate inputs
    valid_types = ['keywords', 'regex']
    valid_variants = ['error', 'false_positive', 'hints', 'success']

    if vocab_type not in valid_types:
        return jsonify({"error": f"Invalid vocab type. Must be one of: {valid_types}"}), 400

    if variant not in valid_variants:
        return jsonify({"error": f"Invalid variant. Must be one of: {valid_variants}"}), 400

    try:
        current_user = getattr(request, "current_user", {})
        user_id = current_user.get("id")
        username = current_user.get("username", "unknown")

        if not user_id:
            return jsonify({"error": "User not found"}), 401

        success = vocab_manager.reset_user_vocabulary(user_id, vocab_type, variant)

        # Always return success - if there's no customization to delete, that's fine
        logger.info(f"Reset {vocab_type}.{variant} vocabulary to defaults for user {username} (had customization: {success})")
        return jsonify({"message": "Vocabulary reset to defaults successfully"}), 200

    except Exception as e:
        logger.error(f"Error resetting vocabulary {vocab_type}.{variant}: {str(e)}")
        return jsonify({"error": "Failed to reset vocabulary"}), 500


@api_bp.route("/jobs/<string:job_id>/upload", methods=["POST"])
@token_required
def upload_job_file(job_id):
    """
    Upload file for existing job with real progress tracking
    ---
    tags:
      - Jobs
    parameters:
      - name: Authorization
        in: header
        type: string
        required: true
        description: Bearer token for authentication
      - name: job_id
        in: path
        type: string
        required: true
        description: Job ID to upload file for
      - name: logFile
        in: formData
        type: file
        required: true
        description: Log file to upload
    responses:
      200:
        description: File uploaded successfully
      400:
        description: No file provided or invalid file
      401:
        description: Unauthorized access
      404:
        description: Job not found
      413:
        description: File too large
      500:
        description: Upload failed
    """
    if not job_manager.verify_user_access(job_id):
        return jsonify({"error": "Unauthorized access"}), 403

    try:
        # Check if file is provided
        if 'logFile' not in request.files:
            return jsonify({"error": "No file provided"}), 400

        file = request.files['logFile']
        if file.filename == '':
            return jsonify({"error": "No file selected"}), 400

        # Validate file size (5GB limit)
        max_size = MAX_API_UPLOAD_SIZE
        if file.content_length and file.content_length > max_size:
            return jsonify({"error": f"File too large. Maximum size is {max_size}."}), 413

        # Start upload with progress tracking
        result = job_manager.upload_file_with_progress(job_id, file)

        current_user = getattr(request, "current_user", {})
        username = current_user.get("username", "unknown")
        logger.info(f"File upload started for job {job_id} by {username}")

        return jsonify(result), 200

    except Exception as e:
        logger.error(f"Error uploading file for job {job_id}: {str(e)}")
        return jsonify({"error": "Upload failed. Please try again."}), 500


# # ------------------------------------
# @api_bp.route("/analyze", methods=["POST"])
# @token_required
# def analyze():
#     """
#     Initiate data analysis process
#     ---
#     tags:
#       - Analysis
#     parameters:
#       - name: Authorization
#         in: header
#         type: string
#         required: true
#         description: Bearer token for authentication
#       - name: analysis_data
#         in: body
#         required: true
#         description: Analysis request data (handled by AnalysisManager)
#         schema:
#           type: object
#           description: Analysis configuration and data
#     responses:
#       200:
#         description: Analysis initiated successfully
#         schema:
#           type: object
#           description: Analysis response from AnalysisManager
#       400:
#         description: Invalid analysis request data
#       401:
#         description: Unauthorized access (token missing or invalid)
#       500:
#         description: Internal server error during analysis initiation
#     """
#     user = getattr(request, "current_user", {})
#     username = user.get("username", "unknown")
#     logger.info(f"Analyze request received from {username}")
#     manager = AnalysisManager()
#     return manager.handle_analyze()
#
#
# @api_bp.route("/status/<analysis_id>", methods=["GET"])
# @token_required
# def status(analysis_id):
#     """
#     Get analysis status by analysis ID
#     ---
#     tags:
#       - Analysis
#     parameters:
#       - name: Authorization
#         in: header
#         type: string
#         required: true
#         description: Bearer token for authentication
#       - name: analysis_id
#         in: path
#         type: string
#         required: true
#         description: Unique analysis identifier
#     responses:
#       200:
#         description: Analysis status information
#         schema:
#           type: object
#           description: Status response from AnalysisManager
#       401:
#         description: Unauthorized access (token missing or invalid)
#       404:
#         description: Analysis ID not found
#       500:
#         description: Internal server error during status retrieval
#     """
#     user = getattr(request, "current_user", {})
#     username = user.get("username", "unknown")
#     logger.info(f"Status request for analysis {analysis_id} received from {username}")
#     manager = AnalysisManager(analysis_id)
#     return manager.handle_status()
#
#
# @api_bp.route("/results/<analysis_id>", methods=["GET"])
# @token_required
# def results(analysis_id):
#     """
#     Get analysis results by analysis ID
#     ---
#     tags:
#       - Analysis
#     parameters:
#       - name: Authorization
#         in: header
#         type: string
#         required: true
#         description: Bearer token for authentication
#       - name: analysis_id
#         in: path
#         type: string
#         required: true
#         description: Unique analysis identifier
#     responses:
#       200:
#         description: Analysis results data
#         schema:
#           type: object
#           description: Results response from AnalysisManager
#       401:
#         description: Unauthorized access (token missing or invalid)
#       404:
#         description: Analysis ID not found
#       500:
#         description: Internal server error during results retrieval
#     """
#     user = getattr(request, "current_user", {})
#     username = user.get("username", "unknown")
#     logger.info(f"Results request for analysis {analysis_id} received from {username}")
#     manager = AnalysisManager(analysis_id)
#     return manager.handle_results()
#
#
# @api_bp.route("/delete/<analysis_id>", methods=["DELETE"])
# @token_required
# def delete_analysis(analysis_id):
#     """
#     Delete analysis by analysis ID
#     ---
#     tags:
#       - Analysis
#     parameters:
#       - name: Authorization
#         in: header
#         type: string
#         required: true
#         description: Bearer token for authentication
#       - name: analysis_id
#         in: path
#         type: string
#         required: true
#         description: Unique analysis identifier
#     responses:
#       200:
#         description: Analysis deleted successfully
#         schema:
#           type: object
#           description: Deletion response from AnalysisManager
#       401:
#         description: Unauthorized access (token missing or invalid)
#       404:
#         description: Analysis ID not found
#       500:
#         description: Internal server error during analysis deletion
#     """
#     user = getattr(request, "current_user", {})
#     username = user.get("username", "unknown")
#     logger.info(f"Delete request for analysis {analysis_id} received from {username}")
#     manager = AnalysisManager(analysis_id)
#     return manager.handle_delete_analysis()
#
#
# @api_bp.route("/clean/<timestamp>", methods=["DELETE"])
# @token_required
# def clean_analyses(timestamp):
#     """
#     Clean analyses older than specified timestamp
#     ---
#     tags:
#       - Analysis
#     parameters:
#       - name: Authorization
#         in: header
#         type: string
#         required: true
#         description: Bearer token for authentication
#       - name: timestamp
#         in: path
#         type: string
#         required: true
#         description: Timestamp threshold for cleaning analyses
#     responses:
#       200:
#         description: Analyses cleaned successfully
#         schema:
#           type: object
#           description: Cleanup response from AnalysisManager
#       401:
#         description: Unauthorized access (token missing or invalid)
#       400:
#         description: Invalid timestamp format
#       500:
#         description: Internal server error during cleanup
#     """
#     user = getattr(request, "current_user", {})
#     username = user.get("username", "unknown")
#     logger.info(f"Clean analyses request received from {username} for timestamp {timestamp}")
#     manager = AnalysisManager()
#     return manager.handle_clean_analyses(timestamp)
#
#
# @api_bp.route("/download/<analysis_id>", methods=["GET"])
# @token_required
# def download_analysis(analysis_id):
#     """
#     Download analysis results by analysis ID
#     ---
#     tags:
#       - Analysis
#     parameters:
#       - name: Authorization
#         in: header
#         type: string
#         required: true
#         description: Bearer token for authentication
#       - name: analysis_id
#         in: path
#         type: string
#         required: true
#         description: Unique analysis identifier
#     responses:
#       200:
#         description: Analysis file downloaded successfully
#         schema:
#           type: file
#           description: Analysis file download from AnalysisManager
#       401:
#         description: Unauthorized access (token missing or invalid)
#       404:
#         description: Analysis ID not found
#       500:
#         description: Internal server error during download
#     """
#     user = getattr(request, "current_user", {})
#     username = user.get("username", "unknown")
#     logger.info(f"Download request for analysis {analysis_id} received from {username}")
#     manager = AnalysisManager(analysis_id)
#     return manager.handle_download_analysis()


app.register_blueprint(api_bp)

if __name__ == "__main__":
    app.run(host="0.0.0.0", port=5123, ssl_context=("certs/cert.pem", "certs/key.pem"), debug=True)
