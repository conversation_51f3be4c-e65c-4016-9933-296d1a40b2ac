"""
Simple executor manager for running LogAnalyser as subprocess.
Follows the principle: Keep it simple - no cancellation, no complex monitoring.
"""

import subprocess
import sys
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional

from analyser.progress_tracker import ProgressTracker
from config.constants import MAX_EXECUTION_TIME_HOURS
from utils.logger import Logger


class ExecutorManager:
    """Simple executor - no cancellation, no monitoring complexity"""
    
    def __init__(self):
        self.logger = Logger(name="ExecutorManager", logfile="ExecutorManager.log", level=Logger.INFO).logger
        
    def submit_job(self, username: str, job_id: str, config: Dict) -> bool:
        """
        Start LogAnalyser subprocess - that's it!
        
        Args:
            username: User's NTID
            job_id: Job UUID
            config: Job configuration containing analysis parameters
            
        Returns:
            bool: True if subprocess started successfully
        """
        try:
            # Get job directory structure
            jobs_dir = Path(__file__).parent.parent.parent / "jobs"
            job_dir = jobs_dir / username / job_id
            input_dir = job_dir / "input" / "logs"
            output_dir = job_dir / "output"
            
            # Ensure output directory exists
            output_dir.mkdir(parents=True, exist_ok=True)
            
            # Find the uploaded log file
            log_files = list(input_dir.glob("*"))
            if not log_files:
                self.logger.error(f"No log files found in {input_dir}")
                return False
                
            input_file = log_files[0]  # Take first file (single file upload)
            output_file = output_dir / "result.json"
            
            # Build LogAnalyser command
            cmd = self._build_analyser_command(
                input_file=str(input_file),
                output_file=str(output_file),
                config=config
            )
            
            self.logger.info(f"Starting analysis for job {job_id}: {' '.join(cmd)}")
            
            # Start subprocess (non-blocking)
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                cwd=str(Path(__file__).parent.parent.parent)  # Repository root
            )
            
            self.logger.info(f"Analysis subprocess started for job {job_id}, PID: {process.pid}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to start analysis for job {job_id}: {str(e)}")
            return False
    
    def _build_analyser_command(self, input_file: str, output_file: str, config: Dict) -> List[str]:
        """
        Build LogAnalyser command line arguments.
        
        Args:
            input_file: Path to input log file
            output_file: Path to output result file
            config: Job configuration
            
        Returns:
            List[str]: Command line arguments
        """
        # Base command - run LogAnalyser as module
        cmd = [
            sys.executable, "-m", "analyser.analyser",
            "--input", input_file,
            "--output", output_file
        ]
        
        # Add analysis type
        analysis_type = config.get('method', 'neural')
        cmd.extend(["--analysis-type", analysis_type])
        
        # Add training mode if specified
        if config.get('sessionType') == 'train':
            cmd.append("--training-mode")
            
        # Add knowledge collection if specified
        if config.get('knowledgeCollection'):
            cmd.extend(["--collection", config['knowledgeCollection']])
            
        # Add verbose logging
        cmd.append("--verbose")
        
        return cmd
    
    def get_job_status(self, username: str, job_id: str) -> Dict:
        """
        Read progress file from job directory using ProgressTracker.
        
        Args:
            username: User's NTID
            job_id: Job UUID
            
        Returns:
            Dict: Progress information
        """
        try:
            # Get job directory
            jobs_dir = Path(__file__).parent.parent.parent / "jobs"
            job_dir = jobs_dir / username / job_id
            output_dir = job_dir / "output"
            
            # Use ProgressTracker to read progress
            # ProgressTracker expects output file path, but we want the directory
            # So we'll create a dummy output path and let ProgressTracker find progress.json
            dummy_output = output_dir / "result.json"
            progress_tracker = ProgressTracker(str(dummy_output))
            
            progress_data = progress_tracker.read()
            
            if progress_data:
                return {
                    "status": progress_data.get("status", "unknown"),
                    "progress": progress_data.get("percentage", 0),
                    "lines_processed": progress_data.get("lines_processed", 0),
                    "total_lines": progress_data.get("total_lines", 0),
                    "message": f"Processing... {progress_data.get('percentage', 0)}%",
                    "updated": progress_data.get("updated")
                }
            else:
                # No progress file yet - check if job completed by looking for result files
                return self._check_completion_status(output_dir)
                
        except Exception as e:
            self.logger.error(f"Failed to get status for job {job_id}: {str(e)}")
            return {
                "status": "unknown",
                "progress": 0,
                "message": f"Error reading status: {str(e)}"
            }
    
    def _check_completion_status(self, output_dir: Path) -> Dict:
        """
        Check if job completed by looking for result files.
        
        Args:
            output_dir: Job output directory
            
        Returns:
            Dict: Status information
        """
        result_file = output_dir / "result.json"
        html_file = output_dir / "visual.html"
        
        if result_file.exists():
            # Job completed successfully
            return {
                "status": "completed",
                "progress": 100,
                "message": "Analysis completed successfully"
            }
        elif any(output_dir.glob("*.log")):
            # Log files exist but no result - might be failed or still running
            # Check if any error indicators exist
            return {
                "status": "analyzing",
                "progress": 0,
                "message": "Analysis in progress..."
            }
        else:
            # No files yet - just started or waiting
            return {
                "status": "starting",
                "progress": 0,
                "message": "Starting analysis..."
            }
    
    def clean_job(self, username: str, job_id: str) -> bool:
        """
        Clean job directory (for future use).
        
        Args:
            username: User's NTID
            job_id: Job UUID
            
        Returns:
            bool: True if successful
        """
        try:
            jobs_dir = Path(__file__).parent.parent.parent / "jobs"
            job_dir = jobs_dir / username / job_id
            
            if job_dir.exists():
                import shutil
                shutil.rmtree(job_dir)
                self.logger.info(f"Cleaned job directory for {job_id}")
                return True
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to clean job {job_id}: {str(e)}")
            return False
