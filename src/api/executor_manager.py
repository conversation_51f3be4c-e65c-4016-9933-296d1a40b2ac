"""
Simple executor manager for running LogAnalyser as subprocess.
Follows the principle: Keep it simple - no cancellation, no complex monitoring.
"""

import subprocess
import sys
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional

from analyser.progress_tracker import ProgressTracker
from utils.logger import Logger

# Import constants with fallback
try:
    from api.config.constants import MAX_EXECUTION_TIME_HOURS
except ImportError:
    MAX_EXECUTION_TIME_HOURS = 20  # Default fallback


class ExecutorManager:
    """Simple executor - no cancellation, no monitoring complexity"""
    
    def __init__(self):
        self.logger = Logger(name="ExecutorManager", logfile="ExecutorManager.log", level=Logger.DEBUG).logger
        
    def submit_job(self, username: str, job_id: str, config: Dict) -> bool:
        """
        Start LogAnalyser subprocess - that's it!
        
        Args:
            username: User's NTID
            job_id: Job UUID
            config: Job configuration containing analysis parameters
            
        Returns:
            bool: True if subprocess started successfully
        """
        try:
            # Get job directory structure
            jobs_dir = Path(__file__).parent.parent.parent / "jobs"
            job_dir = jobs_dir / username / job_id
            input_dir = job_dir / "input" / "logs"
            output_dir = job_dir / "output"
            
            # Ensure output directory exists
            output_dir.mkdir(parents=True, exist_ok=True)
            
            # Find the uploaded log file
            log_files = list(input_dir.glob("*"))
            if not log_files:
                self.logger.error(f"No log files found in {input_dir}")
                return False

            input_file = log_files[0]  # Take first file (single file upload)
            output_file = output_dir / "result.json"

            # Use absolute paths to ensure files are created in the right location
            input_file = input_file.resolve()
            output_file = output_file.resolve()
            
            # Get src directory for running the command
            src_dir = Path(__file__).parent.parent  # src directory

            # Build LogAnalyser command
            cmd = self._build_analyser_command(
                input_file=str(input_file),
                output_file=str(output_file),
                config=config,
                job_id=job_id
            )

            self.logger.info(f"Starting analysis for job {job_id}")
            self.logger.info(f"Command: {' '.join(cmd)}")
            self.logger.info(f"Working directory: {src_dir}")

            # Start subprocess (non-blocking) - run from src directory
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                cwd=str(src_dir)  # Run from src directory
            )

            self.logger.info(f"Analysis subprocess started for job {job_id}, PID: {process.pid}")

            # Start a background thread to monitor the process output
            import threading
            def monitor_process():
                try:
                    stdout, stderr = process.communicate()
                    self.logger.info(f"Job {job_id} subprocess finished with return code: {process.returncode}")
                    if stdout:
                        self.logger.info(f"Job {job_id} STDOUT:\n{stdout}")
                    if stderr:
                        self.logger.error(f"Job {job_id} STDERR:\n{stderr}")
                except Exception as e:
                    self.logger.error(f"Error monitoring process for job {job_id}: {str(e)}")

            monitor_thread = threading.Thread(target=monitor_process, daemon=True)
            monitor_thread.start()

            return True
            
        except Exception as e:
            self.logger.error(f"Failed to start analysis for job {job_id}: {str(e)}")
            return False
    
    def _build_analyser_command(self, input_file: str, output_file: str, config: Dict, job_id: str = "unknown") -> List[str]:
        """
        Build LogAnalyser command line arguments.

        Args:
            input_file: Path to input log file
            output_file: Path to output result file
            config: Job configuration

        Returns:
            List[str]: Command line arguments
        """
        # Get the virtual environment python executable
        repo_root = Path(__file__).parent.parent.parent
        venv_python = repo_root / ".venv" / "Scripts" / "python.exe"

        # Base command - run LogAnalyser as module from src directory
        cmd = [
            str(venv_python), "-m", "analyser",
            input_file,  # positional argument
            "--output", output_file
        ]

        # Add analysis type (map frontend method names to analyser expected values)
        analysis_type = config.get('method', 'neural')
        self.logger.info(f"Job {job_id}: Frontend method = '{analysis_type}'")

        if analysis_type == 'keywords':
            cmd.extend(["--analysis_type", "keywords"])
        elif analysis_type == 'regexps':  # Frontend sends 'regexps'
            cmd.extend(["--analysis_type", "regexps"])
        elif analysis_type == 'regex':   # Alternative frontend name
            cmd.extend(["--analysis_type", "regexps"])
        else:  # Default to neural
            cmd.extend(["--analysis_type", "neural"])

        # Add training mode if specified
        if config.get('sessionType') == 'train':
            cmd.append("--training_mode")

        # Add knowledge collection for neural analysis
        if analysis_type == 'neural' and config.get('knowledgeCollection'):
            cmd.extend(["--collection", config['knowledgeCollection']])
            self.logger.info(f"Job {job_id}: Using neural collection = '{config['knowledgeCollection']}'")

        # Add custom vocabularies for keywords/regex analysis
        if analysis_type in ['keywords', 'regexps', 'regex']:
            # Check if custom vocabulary is specified
            if config.get('customVocabulary'):
                cmd.extend(["--vocabulary", config['customVocabulary']])
                self.logger.info(f"Job {job_id}: Using custom vocabulary = '{config['customVocabulary']}'")

        # Add verbose logging
        cmd.append("--verbose")

        return cmd
    
    def get_job_status(self, username: str, job_id: str) -> Dict:
        """
        Read progress file from job directory using ProgressTracker.
        
        Args:
            username: User's NTID
            job_id: Job UUID
            
        Returns:
            Dict: Progress information
        """
        try:
            # Get job directory
            jobs_dir = Path(__file__).parent.parent.parent / "jobs"
            job_dir = jobs_dir / username / job_id
            output_dir = job_dir / "output"

            self.logger.debug(f"Checking progress for job {job_id}")
            self.logger.debug(f"Job directory: {job_dir}")
            self.logger.debug(f"Output directory: {output_dir}")
            self.logger.debug(f"Output directory exists: {output_dir.exists()}")

            # Use ProgressTracker to read progress
            # ProgressTracker expects output file path, but we want the directory
            # So we'll create a dummy output path and let ProgressTracker find progress.json
            dummy_output = output_dir / "result.json"
            progress_tracker = ProgressTracker(str(dummy_output))

            # Check if progress file exists
            progress_file = output_dir / "progress.json"
            self.logger.debug(f"Progress file path: {progress_file}")
            self.logger.debug(f"Progress file exists: {progress_file.exists()}")

            if progress_file.exists():
                self.logger.debug(f"Progress file size: {progress_file.stat().st_size} bytes")
                try:
                    content = progress_file.read_text()
                    self.logger.debug(f"Progress file content: {content}")
                except Exception as e:
                    self.logger.error(f"Error reading progress file: {str(e)}")

            progress_data = progress_tracker.read()
            self.logger.debug(f"ProgressTracker returned: {progress_data}")

            if progress_data:
                result = {
                    "status": progress_data.get("status", "unknown"),
                    "progress": progress_data.get("percentage", 0),
                    "lines_processed": progress_data.get("lines_processed", 0),
                    "total_lines": progress_data.get("total_lines", 0),
                    "message": f"Processing... {progress_data.get('percentage', 0)}%",
                    "updated": progress_data.get("updated")
                }
                self.logger.debug(f"Returning progress data: {result}")
                return result
            else:
                # No progress file yet - check if job completed by looking for result files
                self.logger.debug("No progress data found, checking completion status")
                return self._check_completion_status(output_dir)
                
        except Exception as e:
            self.logger.error(f"Failed to get status for job {job_id}: {str(e)}")
            return {
                "status": "unknown",
                "progress": 0,
                "message": f"Error reading status: {str(e)}"
            }
    
    def _check_completion_status(self, output_dir: Path) -> Dict:
        """
        Check if job completed by looking for result files.
        
        Args:
            output_dir: Job output directory
            
        Returns:
            Dict: Status information
        """
        result_file = output_dir / "result.json"
        html_file = output_dir / "visual.html"

        self.logger.debug(f"Checking completion status for {output_dir}")
        self.logger.debug(f"Result file exists: {result_file.exists()}")
        self.logger.debug(f"HTML file exists: {html_file.exists()}")

        # List all files in output directory for debugging
        if output_dir.exists():
            files = list(output_dir.glob("*"))
            self.logger.debug(f"Files in output directory: {[f.name for f in files]}")
        else:
            self.logger.debug("Output directory does not exist")

        if result_file.exists():
            # Job completed successfully
            self.logger.debug("Job completed - result file found")
            return {
                "status": "completed",
                "progress": 100,
                "message": "Analysis completed successfully"
            }
        elif any(output_dir.glob("*.log")):
            # Log files exist but no result - might be failed or still running
            # Check if any error indicators exist
            log_files = list(output_dir.glob("*.log"))
            self.logger.debug(f"Log files found: {[f.name for f in log_files]}")
            return {
                "status": "analyzing",
                "progress": 0,
                "message": "Analysis in progress..."
            }
        else:
            # No files yet - just started or waiting
            self.logger.debug("No files found - job starting")
            return {
                "status": "starting",
                "progress": 0,
                "message": "Starting analysis..."
            }
    
    def clean_job(self, username: str, job_id: str) -> bool:
        """
        Clean job directory (for future use).
        
        Args:
            username: User's NTID
            job_id: Job UUID
            
        Returns:
            bool: True if successful
        """
        try:
            jobs_dir = Path(__file__).parent.parent.parent / "jobs"
            job_dir = jobs_dir / username / job_id
            
            if job_dir.exists():
                import shutil
                shutil.rmtree(job_dir)
                self.logger.info(f"Cleaned job directory for {job_id}")
                return True
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to clean job {job_id}: {str(e)}")
            return False
