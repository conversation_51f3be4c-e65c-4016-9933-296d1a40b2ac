import json
import shutil
import threading
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional

from flask import request
from repository import Repository
from utils.logger import Logger
from config.constants import JOB_EXPIRATION_DAYS, UPLOAD_CHUNK_SIZE, DEFAULT_PROJECT_ID
from executor_manager import ExecutorManager

# Directory to store job data
JOBS_DIR  = Path(__file__).parent.parent.parent / Path("jobs")
JOBS_DIR.mkdir(parents=True, exist_ok=True)

# Configuration constants imported from constants.py


class JobManager:
    """Manages job lifecycle, storage, and processing"""
    
    def __init__(self):
        self.logger = Logger(name="JobManager", logfile="JobManager.log", level=Logger.INFO).logger
        self.repo = Repository()

    def lookup_project_id_by_name(self, project_name: str) -> int:
        """Lookup project ID by name, with fallback to default"""
        if not project_name:
            return DEFAULT_PROJECT_ID

        try:
            # Get all projects for current user
            current_user = getattr(request, 'current_user', {})
            user_id = current_user.get('id')

            if user_id:
                projects = self.repo.get_projects_by_user_id(user_id)
                for project in projects:
                    if project.get('name', '').lower() == project_name.lower():
                        return project.get('id', DEFAULT_PROJECT_ID)

            self.logger.warning(f"Project '{project_name}' not found, using default project ID")
            return DEFAULT_PROJECT_ID

        except Exception as e:
            self.logger.error(f"Error looking up project '{project_name}': {e}")
            return DEFAULT_PROJECT_ID

    def get_job_dir(self, username: str, job_id: str) -> Path:
        """Get job directory path: jobs/{username}/{job_id}/"""
        return JOBS_DIR / username / job_id

    def create_job_structure(self, username: str, job_id: str) -> Dict[str, Path]:
        """Create job directory structure and return paths"""
        job_dir = self.get_job_dir(username, job_id)
        
        # Create directory structure
        input_dir = job_dir / "input"
        output_dir = job_dir / "output"
        logs_dir = input_dir / "logs"
        
        for directory in [job_dir, input_dir, output_dir, logs_dir]:
            directory.mkdir(parents=True, exist_ok=True)
        
        return {
            "job_dir": job_dir,
            "input_dir": input_dir,
            "output_dir": output_dir,
            "logs_dir": logs_dir
        }

    def save_job_config(self, username: str, job_id: str, config: Dict) -> None:
        """Save job configuration to input/config.json"""
        paths = self.create_job_structure(username, job_id)
        config_file = paths["input_dir"] / "config.json"
        
        with open(config_file, 'w') as f:
            json.dump(config, f, indent=2, default=str)

    def save_log_files(self, username: str, job_id: str, log_files: List[Dict]) -> List[str]:
        """Save uploaded log files and return filenames"""
        paths = self.create_job_structure(username, job_id)
        saved_files = []
        
        for file_data in log_files:
            filename = file_data.get('name', f'log_{len(saved_files)}.txt')
            content = file_data.get('content', '')
            
            file_path = paths["logs_dir"] / filename
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            saved_files.append(filename)
            self.logger.info(f"Saved log file: {filename} ({len(content)} chars)")
        
        return saved_files

    def save_log_files_with_progress(self, username: str, job_id: str, files: List[Dict]) -> List[str]:
        """Save files with real-time progress tracking"""
        total_size = sum(len(f.get('content', '').encode('utf-8')) for f in files)
        processed_size = 0

        self.repo.update_job(job_id, {
            'status': 'uploading',
            'upload_started_at': datetime.utcnow(),
            'message': 'Starting upload...'
        })

        saved_files = []
        for file_data in files:
            content = file_data.get('content', '')
            filename = file_data.get('name', f'log_{len(saved_files)}.txt')
            file_path = self.get_job_dir(username, job_id) / "input" / "logs" / filename

            # Ensure directory exists
            file_path.parent.mkdir(parents=True, exist_ok=True)

            # Write file in chunks with progress updates
            with open(file_path, 'w', encoding='utf-8') as f:
                content_bytes = content.encode('utf-8')

                for i in range(0, len(content_bytes), UPLOAD_CHUNK_SIZE):
                    chunk = content_bytes[i:i + UPLOAD_CHUNK_SIZE]
                    f.write(chunk.decode('utf-8'))

                    processed_size += len(chunk)
                    progress = int((processed_size / total_size) * 100) if total_size > 0 else 100

                    # Update progress in database
                    self.repo.update_job(job_id, {
                        'upload_progress': progress,
                        'message': f'Uploading {filename}... {progress}%'
                    })

            saved_files.append(filename)
            self.logger.info(f"Saved log file: {filename} ({len(content)} chars)")

        # Upload complete
        self.repo.update_job(job_id, {
            'status': 'uploaded',
            'upload_progress': 100,
            'upload_completed_at': datetime.utcnow(),
            'message': 'Upload completed'
        })

        return saved_files

    def start_analysis_job(self, job_id: str, file_paths: List[Path], analysis_type: str, training_mode: bool = False):
        """Start analysis with job_id for progress tracking"""

        # Update job status with specific status for training vs analysis
        if training_mode:
            status = 'training'
            status_message = 'Starting training...'
        else:
            status = 'analyzing'
            status_message = 'Starting analysis...'

        self.repo.update_job(job_id, {
            'status': status,
            'processing_started_at': datetime.utcnow(),
            'message': status_message
        })

        try:
            # Get job details for executor
            job = self.repo.get_job_by_id(job_id)
            if not job:
                raise ValueError("Job not found")

            username = job.get('username')
            config = job.get('config', {})

            # Submit to executor - simple!
            executor = ExecutorManager()
            success = executor.submit_job(username, job_id, config)

            if not success:
                self.repo.update_job(job_id, {
                    'status': 'failed',
                    'completed_at': datetime.utcnow(),
                    'message': 'Failed to start analysis',
                    'error_message': 'ExecutorManager failed to start subprocess'
                })
            else:
                self.logger.info(f"Analysis subprocess started successfully for job {job_id}")
                # Note: Job completion will be detected by progress monitoring
                # No immediate status update to 'completed' - that happens when progress monitoring
                # detects the result files

        except Exception as e:
            operation_type = 'Training' if training_mode else 'Analysis'
            self.logger.error(f"{operation_type} failed for job {job_id}: {e}")
            self.repo.update_job(job_id, {
                'status': 'failed',
                'completed_at': datetime.utcnow(),
                'message': f'{operation_type} failed: {str(e)}',
                'error_message': str(e)
            })

    def update_job_status(self, job_id: str, status: str, progress: int = None, message: str = None) -> bool:
        """Update job status in database"""
        update_data = {"status": status}
        
        if progress is not None:
            update_data["progress"] = progress
        if message is not None:
            update_data["message"] = message
        if status == "processing":
            update_data["started_at"] = datetime.utcnow()
        elif status in ["completed", "failed"]:
            update_data["completed_at"] = datetime.utcnow()
            
        return self.repo.update_job(job_id, update_data)

    def create_job(self, job_data: Dict) -> Dict:
        """Create a new job with proper structure"""
        current_user = getattr(request, 'current_user', {})
        user_id = current_user.get('id')
        username = current_user.get('username', 'unknown')

        if not user_id:
            raise ValueError("User not authenticated")

        # Use frontend's sessionId (should always be provided as UUID)
        job_id = job_data.get('sessionId')
        if not job_id:
            raise ValueError("sessionId is required")
        
        # Get project info - lookup by name from frontend
        project_name_from_frontend = job_data.get('project', '')
        project_id = self.lookup_project_id_by_name(project_name_from_frontend)

        # Get project details for verification
        project = self.repo.get_project_by_id(project_id)
        project_name = project.get('name', project_name_from_frontend) if project else project_name_from_frontend

        # Calculate expiration date
        expire_at = datetime.utcnow() + timedelta(days=JOB_EXPIRATION_DAYS)

        # Prepare job database record
        job_record = {
            "id": job_id,
            "user_id": user_id,
            "username": username,  # NTID for file operations and logging
            "project_id": project_id,
            "job_type": job_data.get('sessionType', 'analyze'),  # sessionType from frontend
            "method": job_data.get('method', 'neural'),
            "status": "created",
            "name": f"{job_data.get('sessionType', 'analyze').title()} - {project_name}",
            "project_name": project_name,
            "knowledge_collection": job_data.get('knowledgeCollection', ''),
            "log_files": [f.get('name', '') for f in job_data.get('logFiles', [])],
            "config": {
                "sessionType": job_data.get('sessionType'),
                "method": job_data.get('method'),
                "knowledgeCollection": job_data.get('knowledgeCollection'),
                "errorKeywords": job_data.get('errorKeywords', []),
                "falsePositiveKeywords": job_data.get('falsePositiveKeywords', []),
                "hintsKeywords": job_data.get('hintsKeywords', []),
                "successKeywords": job_data.get('successKeywords', []),
                "errorRegexes": job_data.get('errorRegexes', []),
                "falseRegexes": job_data.get('falseRegexes', []),
                "hintsRegexes": job_data.get('hintsRegexes', []),
                "successRegexes": job_data.get('successRegexes', [])
            },
            "expire_at": expire_at
        }

        # Create job in database
        created_job = self.repo.create_job(job_record)
        
        # Create job directory structure using NTID
        self.create_job_structure(username, job_id)

        # Save configuration
        self.save_job_config(username, job_id, job_record["config"])

        # Job created without files - files will be uploaded separately
        self.logger.info(f"Job {job_id} created by {username} for project {project_name} (awaiting file upload)")

        return {
            "jobId": job_id,  # Frontend expects jobId
            "sessionId": job_id,  # Keep for backward compatibility
            "status": "created",
            "message": f"Job {job_id} created successfully. Ready for file upload."
        }

    def get_job_status(self, job_id: str) -> Dict:
        """Get current job status from database"""
        job = self.repo.get_job_by_id(job_id)
        if not job:
            return {"error": "Job not found"}, 404

        # Calculate overall progress
        if job['status'] == 'uploading':
            progress = job.get('upload_progress', 0)
        elif job['status'] in ['analyzing', 'training', 'processing', 'completed']:
            progress = job.get('processing_progress', 0)
        else:
            progress = 0

        return {
            "jobId": job_id,  # Frontend expects jobId
            "sessionId": job_id,  # Keep for backward compatibility
            "status": job['status'],
            "percentage": progress,
            "message": job.get('message', ''),
            "resultHTML": job.get('result_html', ''),
            "resultSummary": job.get('result_summary', '')
        }

    def list_jobs(self, user_id: int = None) -> List[Dict]:
        """List jobs, optionally filtered by user"""
        if user_id:
            return self.repo.get_jobs_by_user(user_id)
        else:
            # Admin view - all jobs
            return self.repo.get_all_jobs()

    def get_job_details(self, job_id: str) -> Optional[Dict]:
        """Get detailed job information"""
        return self.repo.get_job_by_id(job_id)

    def delete_job(self, job_id: str) -> bool:
        """Delete job and its files"""
        job = self.repo.get_job_by_id(job_id)
        if not job:
            return False

        # Delete from database
        if self.repo.delete_job(job_id):
            # Delete job directory using NTID
            username = job.get("username") or f"user_{job.get('userId', 'unknown')}"
            job_dir = self.get_job_dir(username, job_id)
            if job_dir.exists():
                shutil.rmtree(job_dir)
                self.logger.info(f"Deleted job directory for {username}: {job_dir}")
            return True

        return False

    def verify_user_access(self, job_id: str) -> bool:
        """Verify if current user has access to this job"""
        current_user = getattr(request, 'current_user', {})
        if not current_user:
            return False

        job = self.repo.get_job_by_id(job_id)
        if not job:
            return False

        # Admin/maintainer role has access to all jobs
        if current_user.get('role') == 'maintainer':
            return True

        # User can access their own jobs
        return job.get('userId') == current_user.get('id')

    def upload_file_with_progress(self, job_id: str, file) -> Dict:
        """Upload file with streaming and progress tracking"""
        try:
            job = self.repo.get_job_by_id(job_id)
            if not job:
                raise ValueError("Job not found")

            username = job.get('username')  # NTID for file operations
            if not username:
                self.logger.error(f"Job data missing username. Available keys: {list(job.keys())}")
                raise ValueError("Invalid job data - missing username")

            # Update job status to uploading
            self.repo.update_job(job_id, {
                'status': 'uploading',
                'upload_started_at': datetime.utcnow(),  # datetime.now(datetime.UTC)
                'upload_progress': 0,
                'message': 'Starting file upload...'
            })

            # Create job directory structure using NTID
            paths = self.create_job_structure(username, job_id)
            file_path = paths["logs_dir"] / file.filename

            # Stream file to disk with progress tracking
            chunk_size = UPLOAD_CHUNK_SIZE
            total_size = 0
            uploaded_size = 0

            # First pass: get total size if not provided
            if hasattr(file, 'content_length') and file.content_length:
                total_size = file.content_length
            else:
                # Estimate size by reading in chunks
                file.seek(0, 2)  # Seek to end
                total_size = file.tell()
                file.seek(0)  # Reset to beginning

            self.logger.info(f"Starting upload of {file.filename} ({total_size} bytes) for job {job_id} by {username}")

            # Stream file to disk
            with open(file_path, 'wb') as f:
                while True:
                    chunk = file.read(chunk_size)
                    if not chunk:
                        break

                    f.write(chunk)
                    uploaded_size += len(chunk)

                    # Update progress every chunk
                    if total_size > 0:
                        progress = min(int((uploaded_size / total_size) * 100), 100)
                        self.repo.update_job(job_id, {
                            'upload_progress': progress,
                            'message': f'Uploading {file.filename}... {progress}%'
                        })

            # Upload completed successfully
            self.repo.update_job(job_id, {
                'status': 'uploaded',
                'upload_progress': 100,
                'upload_completed_at': datetime.utcnow(),
                'message': 'Upload completed successfully',
                'log_files': [file.filename]  # Update with actual filename
            })

            self.logger.info(f"Upload completed for job {job_id} by {username}: {file.filename}")

            # Start analysis in background
            self._start_background_analysis(job_id, [file_path])

            return {
                "message": "File uploaded successfully",
                "filename": file.filename,
                "size": uploaded_size,
                "status": "uploaded"
            }

        except Exception as e:
            # Upload failed - update job status
            self.logger.error(f"Upload failed for job {job_id} by {username}: {str(e)}")
            self.repo.update_job(job_id, {
                'status': 'failed',
                'upload_progress': 0,
                'completed_at': datetime.utcnow(),
                'message': f'Upload failed: {str(e)}',
                'error_message': str(e)
            })
            raise e

    def _start_background_analysis(self, job_id: str, file_paths: List[Path]):
        """Start analysis in background thread with error handling"""
        def run_analysis():
            try:
                job = self.repo.get_job_by_id(job_id)
                if not job:
                    raise ValueError("Job not found")

                analysis_type = job.get('method', 'neural')
                training_mode = job.get('sessionType') == 'train'

                self.start_analysis_job(job_id, file_paths, analysis_type, training_mode)

            except Exception as e:
                self.logger.error(f"Background analysis failed for job {job_id}: {str(e)}")
                self.repo.update_job(job_id, {
                    'status': 'failed',
                    'completed_at': datetime.utcnow(),
                    'message': f'Analysis failed: {str(e)}',
                    'error_message': str(e)
                })

        # Start analysis in daemon thread
        threading.Thread(target=run_analysis, daemon=True).start()

    def cleanup_expired_jobs(self) -> int:
        """Clean up expired jobs - remove files and database records using NTID"""
        try:
            expired_jobs = self.repo.get_expired_jobs()
            cleaned_count = 0

            for job in expired_jobs:
                try:
                    # Get username for directory cleanup
                    username = job.get('username') or f"user_{job.get('userId', 'unknown')}"
                    job_id = job.get('id')

                    # Remove job files using NTID-based path
                    job_dir = self.get_job_dir(username, job_id)
                    if job_dir.exists():
                        shutil.rmtree(job_dir)
                        self.logger.info(f"Removed expired job directory for {username}: {job_dir}")

                    # Remove from database
                    self.repo.delete_job(job_id)
                    cleaned_count += 1
                    self.logger.info(f"Cleaned up expired job {job_id} for user {username}")

                except Exception as e:
                    job_id = job.get('id', 'unknown')
                    username = job.get('username', 'unknown')
                    self.logger.error(f"Failed to cleanup job {job_id} for {username}: {e}")

            return cleaned_count

        except Exception as e:
            self.logger.error(f"Cleanup operation failed: {e}")
            return 0

    def cleanup_on_login(self) -> int:
        """Run cleanup if needed on user login"""
        try:
            if self.should_run_cleanup():
                expired_count = self.cleanup_expired_jobs()
                self.repo.update_cleanup_time(datetime.utcnow())
                self.logger.info(f"Login cleanup completed: {expired_count} jobs removed")
                return expired_count
            return 0
        except Exception as e:
            self.logger.error(f"Login cleanup failed: {e}")
            return 0

    def should_run_cleanup(self) -> bool:
        """Check if cleanup should run (every 6 hours)"""
        try:
            from api.config.constants import CLEANUP_INTERVAL_HOURS
            last_cleanup = self.repo.get_last_cleanup_time()
            if not last_cleanup:
                return True

            time_since_cleanup = datetime.utcnow() - last_cleanup
            return time_since_cleanup.total_seconds() > (CLEANUP_INTERVAL_HOURS * 3600)
        except Exception as e:
            self.logger.warning(f"Error checking cleanup time: {e}")
            return True  # Run cleanup if we can't determine last time
