import configparser
import os
import traceback
import uuid
from typing import List, Optional, Dict, Any, Union
import logging

from utils.logger import Logger
from utils.qdrant_util import QdrantUtil


class QdrantManager:
    """
    Manager class for Qdrant operations, providing high-level interface for collection management.
    """

    LOG_LEVEL = logging.INFO
    CONFIG_FILE = "../../config.ini"

    def __init__(self, config_section: str = "NEURAL_SEARCH", logger: Optional[Logger] = None):
        """
        Initialize QdrantManager with configuration.

        Args:
            config_section (str): Configuration section to use from config.ini
            logger (Logger, optional): Logger instance. If None, creates a new one.
        """
        self.config_section = config_section
        self.logger = (
            logger
            if logger
            else Logger(
                name=f"QdrantManager-{uuid.uuid4().hex}",
                logfile="QdrantManager.log",
                level=self.LOG_LEVEL,
            )
        )

        # Load configuration
        self.config = self._load_config()

        # Initialize QdrantUtil
        self.qdrant_util = self._initialize_qdrant_util()

        self.logger.info(f"QdrantManager initialized with config section: {config_section}")

    def _load_config(self) -> Dict[str, Union[str, int, None]]:
        """
        Load configuration from config.ini file and environment variables.
        Environment variables take precedence over config file values.

        Returns:
            Dict[str, Union[str, int, None]]: Configuration dictionary
        """
        config = configparser.ConfigParser()
        config_path = self.CONFIG_FILE

        config_dict: Dict[str, Union[str, int, None]] = {}

        if not os.path.exists(config_path):
            self.logger.warning(f"Config file {config_path} not found, using environment variables only")
        else:
            config.read(config_path)
            if self.config_section in config:
                config_dict.update(dict(config[self.config_section]))

        # Override with environment variables (format: SECTION_OPTION)
        env_prefix = f"{self.config_section}_"
        for key in ['HOST', 'PORT', 'API_KEY', 'THREADS']:
            env_key = f"{env_prefix}{key}"
            if env_key in os.environ:
                config_dict[key.lower()] = os.environ[env_key]
                self.logger.debug(f"Using environment variable {env_key}")

        # Convert port to int if present
        if 'port' in config_dict and config_dict['port'] is not None:
            try:
                config_dict['port'] = int(str(config_dict['port']))
            except ValueError:
                self.logger.warning(f"Invalid port value: {config_dict['port']}, using None")
                config_dict['port'] = None

        self.logger.debug(f"Loaded config: {self._mask_sensitive_config(config_dict)}")
        return config_dict

    def _mask_sensitive_config(self, config_dict: Dict[str, Union[str, int, None]]) -> Dict[str, Union[str, int, None]]:
        """
        Mask sensitive information in config for logging.

        Args:
            config_dict: Original config dictionary

        Returns:
            Config dictionary with masked sensitive values
        """
        masked = config_dict.copy()
        if 'api_key' in masked and isinstance(masked['api_key'], str):
            masked['api_key'] = QdrantUtil.hide_key(masked['api_key'], reveal_ends=True)
        return masked

    def _initialize_qdrant_util(self) -> QdrantUtil:
        """
        Initialize QdrantUtil with loaded configuration.

        Returns:
            QdrantUtil: Initialized QdrantUtil instance

        Raises:
            Exception: If required configuration is missing
        """
        host = self.config.get('host')
        port = self.config.get('port')
        api_key = self.config.get('api_key')

        if not host:
            raise Exception(f"Qdrant host not specified in config section '{self.config_section}' or environment variables")

        # Ensure proper types for QdrantUtil
        host_str = str(host) if host is not None else ""
        port_int = int(port) if port is not None and str(port).isdigit() else None
        api_key_str = str(api_key) if api_key is not None else None

        try:
            return QdrantUtil(
                logger=self.logger,
                host=host_str,
                port=port_int,
                api_key=api_key_str
            )
        except Exception as e:
            self.logger.error(f"Failed to initialize QdrantUtil: {e}")
            raise

    def get_collections_by_prefix(self, prefix: str) -> List[str]:
        """
        Get all collection names that begin with the specified prefix.

        Args:
            prefix (str): The prefix to filter collections by

        Returns:
            List[str]: List of collection names that start with the given prefix

        Raises:
            Exception: If there's an error retrieving collections
        """
        try:
            self.logger.debug(f"Getting collections with prefix: '{prefix}'")

            # Get all collections from Qdrant
            collections_response = self.qdrant_util.qdrant.get_collections()

            # Extract collection names and filter by prefix
            filtered_collections = []

            # Handle the response structure - it's a tuple (bool, list of CollectionDescription)
            if isinstance(collections_response, tuple) and len(collections_response) >= 2:
                collections_list = collections_response[1]
            else:
                collections_list = collections_response

            for collection in collections_list:
                collection_name = collection.name if hasattr(collection, 'name') else str(collection)
                if collection_name.startswith(prefix):
                    filtered_collections.append(collection_name)

            self.logger.info(f"Found {len(filtered_collections)} collections with prefix '{prefix}': {filtered_collections}")
            return filtered_collections

        except Exception as e:
            self.logger.error(f"Error getting collections with prefix '{prefix}': {e}")
            raise

    def get_all_collections(self) -> List[str]:
        """
        Get all collection names and information from Qdrant.

        Returns:
            List[Dict[str, Any]]: List of dictionaries containing collection information
        """
        collections_response = self.qdrant_util.qdrant.get_collections()
        result = []
        for item in collections_response:
            if isinstance(item, tuple) and len(item) > 1:
                collections_list = item[1]  # Get the actual list of collections
                result.extend([collection.name for collection in collections_list if hasattr(collection, 'name')])
        self.logger.debug(f"Collections in Qdrant: {result}")
        return result

    def collection_exists(self, collection_name: str) -> bool:
        """
        Check if a collection exists in Qdrant.

        Args:
            collection_name (str): Name of the collection to check

        Returns:
            bool: True if collection exists, False otherwise
        """
        try:
            return self.qdrant_util.qdrant.collection_exists(collection_name=collection_name)
        except Exception as e:
            self.logger.error(f"Error checking if collection '{collection_name}' exists: {e}")
            return False

    def object_to_dict(self, obj):
        """
        Convert Qdrant configuration objects to dictionaries recursively.

        Args:
            obj: Any object or data structure

        Returns:
            dict or primitive: Dictionary representation or primitive value
        """
        # Handle None
        if obj is None:
            return None

        # Direct handling for enum objects
        if hasattr(obj, "value") and not isinstance(obj, (dict, list, tuple)):
            # Many enum classes have a direct value property
            return obj.value

        # Try parsing from string representation for enum-like objects
        obj_str = str(obj)
        if not isinstance(obj, (dict, list, tuple, str, int, float, bool)) and obj_str.startswith("<") and ":" in obj_str and obj_str.endswith(">"):
            try:
                # Extract the value part (after the colon)
                value_part = obj_str.split(":", 1)[1].strip(" >")

                # If it's a quoted string, remove the quotes
                if (value_part.startswith("'") and value_part.endswith("'")) or (value_part.startswith('"') and value_part.endswith('"')):
                    return value_part[1:-1]

                # Try numeric conversion if appropriate
                try:
                    if "." in value_part:
                        return float(value_part)
                    return int(value_part)
                except ValueError:
                    return value_part
            except Exception:
                pass  # Fall through to normal processing

        # Handle regular objects
        if hasattr(obj, "__dict__"):
            return {k: self.object_to_dict(v) for k, v in obj.__dict__.items() if not k.startswith("_")}
        elif isinstance(obj, dict):
            return {k: self.object_to_dict(v) for k, v in obj.items()}
        elif isinstance(obj, (list, tuple)):
            return [self.object_to_dict(item) for item in obj]
        elif hasattr(obj, "_asdict"):
            return {k: self.object_to_dict(v) for k, v in obj._asdict__.items()}
        else:
            return obj

    def get_collection_info(self, collection_name: str) -> Optional[Dict[str, Any]]:
        """
        Get information about a specific collection.
        Collection info sample: {
            "status": "green",
            "optimizer_status": "ok",
            "vectors_count": null,
            "indexed_vectors_count": 0,
            "points_count": 10739,
            "segments_count": 8,
            "config": {
            "params": {
              "vectors": {
                "fast-all-minilm-l6-v2": {
                  "size": 384,
                  "distance": "Cosine",
                  "hnsw_config": null,
                  "quantization_config": null,
                  "on_disk": null,
                  "datatype": null,
                  "multivector_config": null
                }
              },
              "shard_number": 1,
              "sharding_method": null,
              "replication_factor": 1,
              "write_consistency_factor": 1,
              "read_fan_out_factor": null,
              "on_disk_payload": true,
              "sparse_vectors": null
            },
            "hnsw_config": {
              "m": 16,
              "ef_construct": 100,
              "full_scan_threshold": 10000,
              "max_indexing_threads": 0,
              "on_disk": false,
              "payload_m": null
            },
            "optimizer_config": {
              "deleted_threshold": 0.2,
              "vacuum_min_vector_number": 1000,
              "default_segment_number": 0,
              "max_segment_size": null,
              "memmap_threshold": null,
              "indexing_threshold": 20000,
              "flush_interval_sec": 5,
              "max_optimization_threads": null
            },
            "wal_config": {
              "wal_capacity_mb": 32,
              "wal_segments_ahead": 0
            },
            "quantization_config": null,
            "strict_mode_config": null
            },
            "payload_schema": {}
            }

        Args:
            collection_name (str): Name of the collection

        Returns:
            Optional[Dict[str, Any]]: Collection information or None if not found
        """
        try:
            if not self.collection_exists(collection_name):
                return None

            collection_info = self.qdrant_util.qdrant.get_collection(collection_name=collection_name)
            return self.object_to_dict(collection_info)

        except Exception as e:
            self.logger.error(f"Error getting info for collection '{collection_name}': {e}, {traceback.format_exc()}")
            return None
