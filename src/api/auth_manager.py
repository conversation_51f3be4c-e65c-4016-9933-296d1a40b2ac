import os
import sys
import traceback

import jwt
import datetime
from functools import wraps
from typing import Dict

sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

from flask import request, jsonify
from utils.logger import Logger
from repository import Repository  # Updated import to use the new SQLAlchemy Repository by default


class AuthManager:
    # JWT Configuration
    JWT_SECRET_KEY = "nBBeuIapWnzmCqECOmFFeL6R2SAN78De09WqgMEqBoA"
    JWT_ALGORITHM = "HS256"
    TOKEN_EXPIRATION_HOURS = 3
    LOG_LEVEL = Logger.INFO

    ADMIN_GROUP = "IDM_KNUT_ADMINISTRATOR"
    MAINTAINER_GROUP = "IDM_KNUT_MAINTAINER"
    USER_GROUP = "IDM_KNUT_USER"

    def __init__(self):
        self.logger = Logger(name="Auth<PERSON>anager", logfile="AuthManager.log", level=self.LOG_LEVEL).logger
        self.repo = Repository()

    def authenticate_user(self, username: str, password: str) -> Dict | None:
        """Authenticate user with username and password

        Args:
            username (str): The username to authenticate
            password (str): The SHA-256 hashed password from the client
        """
        username=username.lower()
        users = self.repo.get_user_by_username(username)
        for user in users:
            # Compare the hashed passwords directly since client sends SHA-256 hashed password
            if user["password"] == password:  # password from client is already hashed
                self.logger.info(f"User {username} authentication successful")
                return user
        self.logger.warning(f"Authentication failed for user {username}")
        return None

    def generate_token(self, user_data: Dict) -> str:
        """Generate JWT token for authenticated user"""
        payload = {
            **user_data,
            "exp": datetime.datetime.now(datetime.UTC) + datetime.timedelta(hours=self.TOKEN_EXPIRATION_HOURS),
            "iat": datetime.datetime.now(datetime.UTC),
        }
        token = jwt.encode(payload, self.JWT_SECRET_KEY, algorithm=self.JWT_ALGORITHM)
        self.logger.debug(f"JWT token generated for user {user_data['username']}")
        return token

    def verify_token(self, token: str) -> Dict | None:
        """Verify JWT token and return user data"""
        try:
            payload = jwt.decode(token, self.JWT_SECRET_KEY, algorithms=[self.JWT_ALGORITHM])
            self.logger.debug(f"Token verification successful for user_id {payload.get('user_id')}")
            return payload
        except jwt.ExpiredSignatureError:
            self.logger.warning("Token verification failed: Token expired")
            return None
        except jwt.InvalidTokenError:
            self.logger.warning("Token verification failed: Invalid token")
            return None

    def get_user_role(self, groups: list[str] = None) -> str:
        if not groups:
            groups = []
        if self.ADMIN_GROUP in groups:
            return "administrator"
        if self.MAINTAINER_GROUP in groups:
            return "maintainer"
        if self.USER_GROUP in groups:
            return "user"
        return "guest"  # TODO: redirected to a guest page instructing how to request access

    def get_user_from_token(self, token: str = None) -> Dict | None:
        """Extract user information from token"""
        if not token:
            self.logger.warning("Token extraction failed: No token provided")
            return None
        payload = self.verify_token(token)
        if payload:
            user_data = {
                "id": payload["user_id"],
                "username": payload["username"],
                "role": self.get_user_role(payload["groups"]),
                "groups": payload["groups"],
            }
            self.logger.debug(f"User data extracted from token: {user_data['username']}")
            return user_data
        return None

    @staticmethod
    def get_nt_user(username: str) -> str:
        return username[: username.find("@")] if "@" in username else username

    def login(self, data: Dict):
        """Handle user login"""
        try:
            if not data or not data.get("username") or not data.get("password"):
                self.logger.warning("Login attempt with missing credentials")
                return jsonify({"message": "Username and password are required"}), 400
            username = self.get_nt_user(data.get("username"))
            password = data.get("password")
            self.logger.info(f"Login attempt for user {username}")
            # Authenticate user
            user = self.authenticate_user(username, password)
            if not user:
                self.logger.warning(f"Login failed for user {username}: Invalid credentials")
                return jsonify({"message": "Invalid credentials"}), 401
            payload = {
                "user_id": user.get("id"),
                "username": user.get("username"),
                "role": self.get_user_role(user.get("groups")),
                "groups": user.get("groups"),
                "exp": datetime.datetime.now(datetime.UTC) + datetime.timedelta(hours=self.TOKEN_EXPIRATION_HOURS),
                "iat": datetime.datetime.now(datetime.UTC),
            }
            # Generate JWT token
            token = self.generate_token(payload)
            # Get user projects from jsondb
            user_projects = user.get("project_ids", [])
            # Return token and user info (without password)
            user_info = {"id": user["id"], "username": user["username"], "role": user["role"], "projects": user_projects}
            self.logger.info(f"User {username} authenticated successfully")
            return jsonify({"access_token": token, "user": user_info}), 200
        except Exception as e:
            self.logger.error(f"Login error: {str(e)}, {traceback.format_exc()}")
            return jsonify({"message": "Internal server error"}), 500

    def token_required(self, f):
        """Decorator to require authentication for API endpoints"""

        @wraps(f)
        def decorated(*args, **kwargs):
            auth_header = request.headers.get("Authorization")
            token = None
            self.logger.debug(f"Authorization header received: {auth_header is not None}")
            if auth_header and auth_header.startswith("Bearer "):
                token = auth_header.split(" ")[1]
                self.logger.debug("Bearer token extracted from Authorization header")
            else:
                self.logger.error("Authorization Token was not found in the request header")
            if not token:
                self.logger.warning("Authentication failed: No token found in request")
                return jsonify({"message": "Security Token is missing! Are you sure you are authorized to do this?"}), 401
            user_data = self.get_user_from_token(token)
            if not user_data:
                self.logger.warning("Authentication failed: Invalid or expired token")
                return jsonify({"message": "Token is invalid or expired!"}), 401
            # Make user data available to the decorated function
            request.current_user = user_data
            self.logger.debug(f"Authentication successful for user {user_data['username']}")
            return f(*args, **kwargs)

        return decorated


# Export the decorator function for easy import and use
def token_required(f):
    """Global token_required decorator function"""
    return AuthManager().token_required(f)
