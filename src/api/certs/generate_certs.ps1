# PowerShell script to generate self-signed certificates for local development
# Run this script as Administrator

# Create certs directory if it doesn't exist
$certsDir = Split-Path -Parent $MyInvocation.MyCommand.Path
if (-not (Test-Path $certsDir)) {
    New-Item -ItemType Directory -Path $certsDir -Force
}

# Set file paths
$rootCAKey = Join-Path $certsDir "rootCA.key"
$rootCAPem = Join-Path $certsDir "rootCA.pem"
$localhostKey = Join-Path $certsDir "key.pem"  # Named to match app.py expectations
$localhostCsr = Join-Path $certsDir "localhost.csr"
$localhostCrt = Join-Path $certsDir "cert.pem"  # Named to match app.py expectations
$localhostExtFile = Join-Path $certsDir "localhost.ext"

# Create SAN extension file
@"
authorityKeyIdentifier=keyid,issuer
basicConstraints=CA:FALSE
keyUsage = digitalSignature, nonRepudiation, keyEncipherment, dataEncipherment
extendedKeyUsage = serverAuth
subjectAltName = @alt_names

[alt_names]
DNS.1 = localhost
IP.1 = 127.0.0.1
IP.2 = ::1
"@ | Out-File -FilePath $localhostExtFile -Encoding ascii

# Check if OpenSSL is installed
$openssl = Get-Command openssl -ErrorAction SilentlyContinue
if (-not $openssl) {
    Write-Host "OpenSSL is not found. Please install OpenSSL for Windows."
    Write-Host "You can download it from https://slproweb.com/products/Win32OpenSSL.html"
    exit 1
}

# Generate Root CA
Write-Host "Generating Root CA certificate..."
& openssl genrsa -out $rootCAKey 2048
& openssl req -x509 -new -nodes -key $rootCAKey -sha256 -days 1024 -out $rootCAPem -subj "/CN=LogAnalyzerLocalCA"

# Generate Server Key and CSR
Write-Host "Generating server certificate..."
& openssl genrsa -out $localhostKey 2048
& openssl req -new -key $localhostKey -out $localhostCsr -subj "/CN=localhost"

# Sign the Server Certificate with Root CA
& openssl x509 -req -in $localhostCsr -CA $rootCAPem -CAkey $rootCAKey -CAcreateserial -out $localhostCrt -days 500 -sha256 -extfile $localhostExtFile

# Verify certificates were created
if ((Test-Path -Path $localhostCrt) -and (Test-Path -Path $localhostKey)) {
    Write-Host "Certificate files successfully generated at:"
    Write-Host "Certificate: $localhostCrt"
    Write-Host "Private Key: $localhostKey"

    # Import Root CA into Windows Trusted Store
    Write-Host "`nImporting Root CA into Trusted Root Certification Authorities..."
    try {
        Import-Certificate -FilePath $rootCAPem -CertStoreLocation Cert:\LocalMachine\Root -ErrorAction Stop
        Write-Host "Root CA certificate imported successfully."
        Write-Host "Please restart your browser to recognize the new certificate authority."
    } catch {
        Write-Host "Failed to import certificate: $_"
        Write-Host "Make sure you're running this script as Administrator."
        Write-Host "You can manually import the certificate from: $rootCAPem"
    }

    # Clean up temporary files
    Remove-Item $localhostCsr -Force -ErrorAction SilentlyContinue
    Remove-Item $localhostExtFile -Force -ErrorAction SilentlyContinue

    Write-Host "`nYour Flask API should now use HTTPS with these certificates."
    Write-Host "The API will use them automatically as specified in app.py."
    Write-Host "`nIf you encounter 'ERR_SSL_PROTOCOL_ERROR', please check that:"
    Write-Host "1. You've restarted your browser"
    Write-Host "2. You're using https:// in the URL"
    Write-Host "3. Your browser supports TLS 1.2 or higher"
} else {
    Write-Host "Failed to generate certificate files."
}
