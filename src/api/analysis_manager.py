import json
import shutil
import uuid
from datetime import datetime
from pathlib import Path

from flask import request, jsonify, send_file

from utils.logger import Logger

# Directory to store analysis sessions

ANALYSIS_DIR = Path(__file__).parent.parent.parent / Path("jobs")
ANALYSIS_DIR.mkdir(parents=True, exist_ok=True)


class AnalysisManager:
    LOG_LEVEL = Logger.INFO

    def __init__(self, analysis_id=None):
        self.analysis_id = analysis_id
        self.analysis_dir = self.get_analysis_dir(analysis_id) if analysis_id else None
        self.logger = Logger(name=f"AnalysisManager-{uuid.uuid4().hex}", logfile="AnalysisManager.log", level=self.LOG_LEVEL).logger

    @staticmethod
    def get_analysis_dir(analysis_id):
        return ANALYSIS_DIR / analysis_id

    def get_analysis_file_path(self):
        return self.analysis_dir / "analysis.json"

    def save_analysis(self, data):
        self.analysis_dir.mkdir(parents=True, exist_ok=True)
        with open(self.get_analysis_file_path(), "w") as f:
            json.dump(data, f)

        # Get current user from request context (set by @token_required decorator)
        user = getattr(request, 'current_user', None)
        username = user['username'] if user else 'unknown'
        self.logger.info(f"Analysis {self.analysis_id} saved by {username}")

    def load_analysis(self):
        try:
            with open(self.get_analysis_file_path(), "r") as f:
                return json.load(f)
        except FileNotFoundError:
            return None

    def verify_user_access(self, analysis_data):
        """Verify if current user has access to this analysis"""
        current_user = getattr(request, 'current_user', None)
        if not current_user:
            return False

        # Admin/maintainer role has access to all analyses
        if current_user.get('role') == 'maintainer':
            return True

        # User can access their own analyses
        if analysis_data.get('user_id') == current_user.get('id'):
            return True

        return False

    def handle_analyze(self):
        current_user = getattr(request, 'current_user', None)
        username = current_user['username'] if current_user else 'unknown'

        data = {}
        if "file" in request.files:
            file = request.files["file"]
            if file.filename == "":
                return jsonify({"message": "No file selected for uploading"}), 400
            analysis_id, file_path = self.handle_file_upload(file)
        else:
            data = request.json if request.json else {}
            analysis_id, file_path = self.handle_streamer(data)

        self.analysis_id = analysis_id
        self.analysis_dir = self.get_analysis_dir(analysis_id)

        # Store initial status with user information
        analysis_data = {
            "status": "analyzing",
            "results": None,
            "user_id": current_user['id'] if current_user else None,
            "username": username,
            "file_path": str(file_path),
            "persistent": data.get("persistent", False),
            "created_at": datetime.now().isoformat()
        }
        self.save_analysis(analysis_data)

        # Here we will call the analysis functions with the provided parameters
        # For demonstration, we'll just update the status and results
        analysis_data["status"] = "done"
        analysis_data["results"] = {
            "file_path": str(file_path),
            "analysis_result": str(self.analysis_dir / "analysis.json"),
            "report": str(self.analysis_dir / "report.html"),
            "knut_response": str(self.analysis_dir / "knut_response.txt"),
        }
        self.save_analysis(analysis_data)

        self.logger.info(f"Analysis {self.analysis_id} handled by {username}")
        return jsonify({"analysis_id": analysis_id})

    def handle_status(self):
        current_user = getattr(request, 'current_user', None)
        username = current_user['username'] if current_user else 'unknown'

        analysis_data = self.load_analysis()
        if analysis_data:
            if not self.verify_user_access(analysis_data):
                self.logger.warning(f"Unauthorized access attempt by {username} for analysis {self.analysis_id}")
                return jsonify({"message": "Unauthorized access!"}), 403

            self.logger.info(f"Status of analysis {self.analysis_id} requested by {username}")
            return jsonify({"status": analysis_data["status"]})
        else:
            return jsonify({"message": "Analysis ID not found"}), 404

    def handle_results(self):
        """Handle results endpoint"""
        current_user = getattr(request, 'current_user', None)
        username = current_user['username'] if current_user else 'unknown'

        analysis_data = self.load_analysis()
        if analysis_data:
            if not self.verify_user_access(analysis_data):
                self.logger.warning(f"Unauthorized access attempt by {username} for analysis {self.analysis_id}")
                return jsonify({"message": "Unauthorized access!"}), 403

            if analysis_data["status"] != "done":
                return jsonify({"message": "Analysis not completed yet"}), 202

            self.logger.info(f"Results of analysis {self.analysis_id} requested by {username}")
            return jsonify({"results": analysis_data.get("results", {})})
        else:
            return jsonify({"message": "Analysis ID not found"}), 404

    def handle_delete_analysis(self):
        """Handle delete analysis endpoint"""
        current_user = getattr(request, 'current_user', None)
        username = current_user['username'] if current_user else 'unknown'

        analysis_data = self.load_analysis()
        if analysis_data:
            if not self.verify_user_access(analysis_data):
                self.logger.warning(f"Unauthorized delete attempt by {username} for analysis {self.analysis_id}")
                return jsonify({"message": "Unauthorized access!"}), 403

            # Delete the analysis directory
            if self.analysis_dir and self.analysis_dir.exists():
                shutil.rmtree(self.analysis_dir)

            self.logger.info(f"Analysis {self.analysis_id} deleted by {username}")
            return jsonify({"message": "Analysis deleted successfully"})
        else:
            return jsonify({"message": "Analysis ID not found"}), 404

    def handle_clean_analyses(self, timestamp):
        """Handle clean analyses endpoint - only for maintainers"""
        current_user = getattr(request, 'current_user', None)
        username = current_user['username'] if current_user else 'unknown'

        # Only maintainers can clean analyses
        if not current_user or current_user.get('role') != 'maintainer':
            self.logger.warning(f"Unauthorized clean attempt by {username}")
            return jsonify({"message": "Insufficient permissions"}), 403

        # TODO: Implement timestamp-based cleaning logic
        self.logger.info(f"Clean analyses request for timestamp {timestamp} by {username}")
        return jsonify({"message": "Clean operation completed"})

    def handle_download_analysis(self):
        """Handle download analysis endpoint"""
        current_user = getattr(request, 'current_user', None)
        username = current_user['username'] if current_user else 'unknown'

        analysis_data = self.load_analysis()
        if analysis_data:
            if not self.verify_user_access(analysis_data):
                self.logger.warning(f"Unauthorized download attempt by {username} for analysis {self.analysis_id}")
                return jsonify({"message": "Unauthorized access!"}), 403

            # TODO: Implement actual file download logic
            self.logger.info(f"Download of analysis {self.analysis_id} requested by {username}")
            return jsonify({"message": "Download feature not implemented yet"})
        else:
            return jsonify({"message": "Analysis ID not found"}), 404

    def handle_file_upload(self, file):
        """Handle file upload logic"""
        # Generate analysis ID
        analysis_id = str(uuid.uuid4())
        analysis_dir = self.get_analysis_dir(analysis_id)
        analysis_dir.mkdir(parents=True, exist_ok=True)

        # Save uploaded file
        file_path = analysis_dir / file.filename
        file.save(str(file_path))

        return analysis_id, file_path

    def handle_streamer(self, data):
        """Handle streamer data logic"""
        # Generate analysis ID
        analysis_id = str(uuid.uuid4())
        analysis_dir = self.get_analysis_dir(analysis_id)
        analysis_dir.mkdir(parents=True, exist_ok=True)

        # Save streamer data to a file
        file_path = analysis_dir / "streamer_data.json"
        with open(file_path, 'w') as f:
            json.dump(data, f)

        return analysis_id, file_path

