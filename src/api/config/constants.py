"""
Configuration constants for the log analyzer application
"""

# Job Management Constants
JOB_EXPIRATION_DAYS = 30  # Jobs expire after 30 days
PROGRESS_UPDATE_INTERVAL = 1000  # Lines processed before progress update
UPLOAD_CHUNK_SIZE = 1 * 1024 * 1024   #
CLEANUP_INTERVAL_HOURS = 6  # Cleanup check interval
MAX_API_UPLOAD_SIZE = 5 * 1024 * 1024 * 1024  # 5GB limit for API uploads

# Job Status Constants
JOB_STATUS_CREATED = "created"
JOB_STATUS_UPLOADING = "uploading"
JOB_STATUS_UPLOADED = "uploaded"
JOB_STATUS_WAITING = "waiting-for-exec"
JOB_STATUS_ANALYZING = "analyzing"  # For analysis jobs
JOB_STATUS_TRAINING = "training"    # For training jobs
JOB_STATUS_PROCESSING = "processing"  # Generic processing (legacy)
JOB_STATUS_COMPLETED = "completed"
JOB_STATUS_FAILED = "failed"

# Job Types
JOB_TYPE_ANALYZE = "analyze"
JOB_TYPE_TRAIN = "train"

# Analysis Methods
METHOD_NEURAL = "neural"
METHOD_KEYWORDS = "keywords"
METHOD_REGEX = "regex"

# File Upload Limits
MAX_FILE_SIZE_GB = 5  # 5GB limit for single file uploads
MAX_FILES_PER_JOB = 1  # Currently limited to single file

# API Response Messages
MESSAGES = {
    "job_created": "Job created successfully",
    "job_not_found": "Job not found",
    "unauthorized": "Unauthorized access",
    "upload_complete": "Upload completed",
    "analysis_complete": "Analysis completed successfully",
    "training_complete": "Training completed successfully",
    "job_failed": "Job failed",
}

# Default Values
DEFAULT_PROJECT_ID = 1  # Fallback project ID
DEFAULT_EXPIRATION_DATE = "2025-12-31T23:59:59Z"
