import os
from openai import OpenAI

"""
Model: 1.5 Flash 002 (multimodal: audio, images, video, and text)
https://ai.google.dev/gemini-api/docs/models#gemini-1.5-flash

OpenAI API endpoint: https://aoai-farm.bosch-temp.com/api/openai/deployments/google-gemini-1-5-flash

Input:
$0.075, prompts <= 128k tokens
$0.15, prompts > 128k tokens

Output: 
$0.30, prompts <= 128k tokens
$0.60, prompts > 128k tokens

Context caching price:
$0.01875, prompts <= 128k tokens
$0.0375, prompts > 128k tokens

Input token limit: 1,048,576
Output token limit: 8,192

Feature	                    Gemini 1.5 Flash	GPT-4o Mini
---------------------------	-------------------	-------------------
Speed	                    ✅ Very fast	    ✅ Fast
Context length	            ✅ Longer	        ⚠️ Shorter
Log parsing & summarizing	✅ Strong	        ⚠️ Moderate
Natural language reasoning	⚠️ Moderate	        ✅ Strong
Cost-efficiency	            ✅ High	            ✅ High

"""

client = OpenAI(
    api_key="dummy",  # Required by the library, will be overwritten by the API manager
    base_url="https://aoai-farm.bosch-temp.com/api/openai/deployments/google-gemini-1-5-flash",
    default_headers={"genaiplatform-farm-subscription-key": os.getenv("GENAIPLATFORM_FARM_SUBSCRIPTION_KEY")},
)

# Option 1: Non-streaming response (simpler)
response = client.chat.completions.create(
    model="gemini-1.5-flash",  # Required by the endpoint, Azure OpenAI ignored that one but it's mandatory for Gemini
    n=1,
    stream=False,  # Changed to False for simple response
    messages=[{"role": "system", "content": "You are a helpful assistant."}, {"role": "user", "content": "Explain to me how AI works"}],
)

print(response.choices[0].message.content)

# Option 2: Streaming response (commented out)
"""
response = client.chat.completions.create(
    model="gemini-1.5-flash",
    n=1,
    stream=True,
    messages=[{"role": "system", "content": "You are a helpful assistant."}, {"role": "user", "content": "Explain to me how AI works"}],
)

# Handle streaming response
for chunk in response:
    if chunk.choices[0].delta.content is not None:
        print(chunk.choices[0].delta.content, end="")
print()  # Add newline at the end
"""
