"""
Vocabulary Manager - Handles default vocabularies and user customizations
"""

import json
from pathlib import Path
from typing import Dict, List, Optional
from flask import request
from repository import Repository
from utils.logger import Logger


class VocabularyManager:
    """Manages vocabulary loading, merging defaults with user customizations"""
    
    def __init__(self):
        self.logger = Logger(name="VocabularyManager", logfile="VocabularyManager.log", level=Logger.INFO).logger
        self.repo = Repository()
        self.vocab_dir = Path(__file__).parent.parent / "vocabularies"
        
        # Vocabulary file mapping
        self.vocab_files = {
            'keywords': 'keywords_unified.json',
            'regex': 'regex_unified.json'
        }
    
    def load_default_vocabulary(self, vocab_type: str) -> Dict[str, List[str]]:
        """Load default vocabulary from JSON files"""
        try:
            vocab_file = self.vocab_files.get(vocab_type)
            if not vocab_file:
                raise ValueError(f"Unknown vocabulary type: {vocab_type}")
            
            file_path = self.vocab_dir / vocab_file
            if not file_path.exists():
                self.logger.error(f"Vocabulary file not found: {file_path}")
                return {"errors": [], "false_positives": [], "hints": [], "success": []}
            
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Ensure all required variants exist
            default_structure = {
                "errors": [],
                "false_positives": [],
                "hints": [],
                "success": []
            }
            
            # Merge with defaults to ensure all keys exist
            for key in default_structure:
                if key not in data:
                    data[key] = []
            
            return data
            
        except Exception as e:
            self.logger.error(f"Error loading default vocabulary {vocab_type}: {e}")
            return {"errors": [], "false_positives": [], "hints": [], "success": []}
    
    def get_user_vocabulary(self, user_id: int, vocab_type: str, variant: str) -> List[str]:
        """Get user's vocabulary for a specific type and variant (merged with defaults)"""
        try:
            # Load default vocabulary
            default_vocab = self.load_default_vocabulary(vocab_type)
            
            # Map variant names (API uses singular, files use plural)
            variant_mapping = {
                'error': 'errors',
                'false_positive': 'false_positives',
                'hints': 'hints',
                'success': 'success'
            }
            
            file_variant = variant_mapping.get(variant, variant)
            default_items = default_vocab.get(file_variant, [])
            
            # Get user modifications
            user_delta = self.repo.get_user_vocabulary_delta(user_id, vocab_type, variant)
            
            if not user_delta:
                # No user modifications, return defaults
                return default_items
            
            # Apply user modifications
            added_items = user_delta.get('added_items', [])
            removed_items = user_delta.get('removed_items', [])
            
            # Start with defaults, remove user-removed items, add user-added items
            result = [item for item in default_items if item not in removed_items]
            result.extend([item for item in added_items if item not in result])
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error getting user vocabulary: {e}")
            return []
    
    def save_user_vocabulary(self, user_id: int, username: str, vocab_type: str, variant: str, items: List[str]) -> bool:
        """Save user's complete vocabulary list if different from defaults"""
        try:
            # Load default vocabulary
            default_vocab = self.load_default_vocabulary(vocab_type)

            # Map variant names
            variant_mapping = {
                'error': 'errors',
                'false_positive': 'false_positives',
                'hints': 'hints',
                'success': 'success'
            }

            file_variant = variant_mapping.get(variant, variant)
            default_items = default_vocab.get(file_variant, [])

            # Compare user items with defaults (order-independent)
            user_items_set = set(items)
            default_items_set = set(default_items)

            # Only save if different from defaults
            if user_items_set != default_items_set:
                # Calculate delta for storage efficiency
                added_items = list(user_items_set - default_items_set)
                removed_items = list(default_items_set - user_items_set)

                success = self.repo.save_user_vocabulary_delta(
                    user_id, username, vocab_type, variant, added_items, removed_items
                )
                if success:
                    self.logger.info(f"Saved vocabulary for {username}: {vocab_type}.{variant} ({len(items)} items, +{len(added_items)}, -{len(removed_items)})")
                return success
            else:
                # Same as defaults, delete any existing customization
                self.repo.delete_user_vocabulary_delta(user_id, vocab_type, variant)
                self.logger.info(f"Vocabulary matches defaults for {username}: {vocab_type}.{variant} - removed customization")
                return True

        except Exception as e:
            self.logger.error(f"Error saving user vocabulary: {e}")
            return False
    
    def reset_user_vocabulary(self, user_id: int, vocab_type: str, variant: str) -> bool:
        """Reset user's vocabulary to defaults"""
        try:
            success = self.repo.delete_user_vocabulary_delta(user_id, vocab_type, variant)
            if success:
                current_user = getattr(request, 'current_user', {})
                username = current_user.get('username', 'unknown')
                self.logger.info(f"Reset vocabulary to defaults for {username}: {vocab_type}.{variant}")
            return success
        except Exception as e:
            self.logger.error(f"Error resetting user vocabulary: {e}")
            return False
    
    def get_vocabulary_stats(self, user_id: int) -> Dict:
        """Get statistics about user's vocabulary customizations"""
        try:
            stats = {
                'total_customizations': 0,
                'by_type': {},
                'by_variant': {}
            }
            
            # This would require a query to get all user vocabularies
            # For now, return basic structure
            return stats
            
        except Exception as e:
            self.logger.error(f"Error getting vocabulary stats: {e}")
            return {}
