import os
import traceback

from api import app
from utils.logger import Logger

logger = Logger(name="ServerLogger").logger

if __name__ == "__main__":
    cert_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "certs")
    cert_file = os.path.join(cert_dir, "cert.pem")
    key_file = os.path.join(cert_dir, "key.pem")
    port = int(os.environ.get("KNUT_API_PORT", 5123))
    host = str(os.environ.get("KNUT_API_HOST", "0.0.0.0"))
    # Debug mode if DEBUG
    debug_mode = os.environ.get("KNUT_API_DEBUG", "True").lower() == "true"

    try:
        if os.path.exists(cert_file) and os.path.exists(key_file):
            logger.info(f"Starting server with SSL on {host}:{port}")
            ssl_context = (cert_file, key_file)
            app.run(
                host=host,
                port=port,
                ssl_context=ssl_context,
                debug=debug_mode,
                threaded=True
            )
        else:
            logger.warning("SSL certificates not found! Please run the generate_certs.ps1 script in the certs directory.")
            logger.warning("Starting server without SSL - ONLY USE THIS FOR DEVELOPMENT!")
            if os.environ.get("KNUT_API_ENFORCE_SSL", "False").lower() == "true":
                logger.error("KNUT_API_ENFORCE_SSL is set to True but certificates are missing. Server not started.")
                exit(1)
            app.run(host=host, port=port, debug=debug_mode, threaded=True)
    except Exception as e:
        logger.error(f"Server failed to start: {e}, {traceback.format_exc()}")
    finally:
        logger.info("Server stopped")
