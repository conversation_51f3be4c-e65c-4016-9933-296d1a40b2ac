import traceback
from typing import Any, Dict, List, Optional, Union, Tuple

from analyser.analyser_factory.generic_analyser import GenericAnalyser
from utils.fast_regex import re


class RegularExpressions(GenericAnalyser):
    """
    The RegularExpressions class provides methods for pre-compiling regular expressions,
    calculating regex scores, and matching regular expressions.
    """

    NAME = "RegexpsAnalyser"
    ERRORS = "regex_errors.json"
    FALSE_POSITIVES = "regex_false_positives.json"
    SUCCESS = "regex_success.json"
    HINTS = "regex_hints.json"
    VOCABULARY_FILE = "regex_unified.json"
    ANALYSIS_METHOD = "regexps"

    def __init__(
        self,
        input: str,
        match_at: float,
        verbose: bool,
        output: Optional[str],
        prompt_output: Optional[str],
        training_mode: bool,
        ground_truth_folder: Optional[str],
        rag: Optional[str],
        logger: Optional[Any] = None,
        ini_config: Optional[Any] = None,
        streamer: Optional[Any] = None,
        cfg: Optional[Any] = None,
        vocabulary: Optional[str] = None,
        progress_tracker: Optional[Any] = None,
        total_lines: Optional[int] = None,
        **kwargs  # Accept any additional parameters
    ) -> None:
        """
        Constructs all the necessary attributes for the RegularExpressions object.
        """
        super().__init__(
            input,
            match_at,
            verbose,
            output,
            prompt_output,
            training_mode,
            ground_truth_folder,
            rag,
            logger,
            ini_config,
            streamer,
            cfg,
            progress_tracker=progress_tracker,
            total_lines=total_lines,
        )
        if vocabulary:
            self.VOCABULARY_FILE = vocabulary
        # Compiled regex pattern cache
        self._compiled_patterns: Dict[str, re.Pattern] = {}

        # Individual patterns
        self.errors: List[re.Pattern] = []
        self.fs_vocabulary: List[re.Pattern] = []
        self.success: List[re.Pattern] = []
        self.hints: List[re.Pattern] = []

        # Original pattern strings (needed for results)
        self.error_patterns: List[str] = []
        self.fp_patterns: List[str] = []
        self.success_patterns: List[str] = []
        self.hint_patterns: List[str] = []

        # Combined patterns for faster matching
        self.combined_error_pattern: Optional[re.Pattern] = None
        self.combined_fp_pattern: Optional[re.Pattern] = None
        self.combined_success_pattern: Optional[re.Pattern] = None
        self.combined_hint_pattern: Optional[re.Pattern] = None

        # Precalculate all regex patterns
        self.get_vocabularies()

    def compile_pattern(self, pattern: str, case_sensitive: bool = False) -> re.Pattern:
        """
        Compile a regex pattern and cache it for reuse.
        This method is optimized for performance with a simplified cache key.

        Args:
            pattern (str): The regex pattern string
            case_sensitive (bool): Whether the pattern should be case sensitive

        Returns:
            re.Pattern: Compiled regex pattern
        """
        # Create a unique key for the pattern/case combination
        cache_key = f"{pattern}_{int(case_sensitive)}"

        if cache_key not in self._compiled_patterns:
            flags = 0 if case_sensitive else re.IGNORECASE
            try:
                self._compiled_patterns[cache_key] = re.compile(pattern, flags)
            except re.error as e:
                self.logger.error(f"{self.NAME}: Error compiling regex pattern '{pattern}': {e}")
                # Return a pattern that will never match anything if compilation fails
                return re.compile(r"^\b$")

        return self._compiled_patterns[cache_key]

    @staticmethod
    def get_compiled(regex_patterns: List[str], case_sensitive: bool = False) -> List[re.Pattern]:
        """
        Pre-compile the regular expressions strings from the regexps strings dictionary for faster matching.
        This method is optimized for performance with error handling.

        Args:
            regex_patterns (List[str]): List of regex pattern strings
            case_sensitive (bool): Whether the patterns should be case sensitive

        Returns:
            List[re.Pattern]: List of compiled regex patterns
        """
        compiled = []
        flags = 0 if case_sensitive else re.IGNORECASE

        for pattern in regex_patterns:
            try:
                compiled.append(re.compile(pattern, flags))
            except re.error:
                # Skip invalid patterns instead of failing
                continue

        return compiled

    def get_vocabularies(self) -> None:
        """Load and compile all regex vocabularies from a unified file."""
        try:
            vocab = self.files.get_json(str(self.VOCABULARY_PATH / self.VOCABULARY_FILE))
            self.error_patterns = vocab.get("errors", [])
            self.fp_patterns = vocab.get("false_positives", [])
            self.success_patterns = vocab.get("successes", [])
            self.hint_patterns = vocab.get("hints", [])

            # Compile individual patterns
            self.errors = self.get_compiled(self.error_patterns)
            self.fs_vocabulary = self.get_compiled(self.fp_patterns)
            self.success = self.get_compiled(self.success_patterns)
            self.hints = self.get_compiled(self.hint_patterns)

            # Create combined patterns for faster matching
            self.combined_error_pattern = self.create_combined_pattern(self.error_patterns)
            self.combined_fp_pattern = self.create_combined_pattern(self.fp_patterns)
            self.combined_success_pattern = self.create_combined_pattern(self.success_patterns)
            self.combined_hint_pattern = self.create_combined_pattern(self.hint_patterns)

            if self.verbose:
                self.logger.info(
                    f"{self.NAME}: Loaded and compiled {len(self.errors)} error patterns, "
                    f"{len(self.fs_vocabulary)} false positive patterns, "
                    f"{len(self.success)} success patterns, "
                    f"{len(self.hints)} hint patterns"
                )
                self.logger.info(
                    f"{self.NAME}: Created combined patterns: "
                    f"errors={self.combined_error_pattern is not None}, "
                    f"false positives={self.combined_fp_pattern is not None}, "
                    f"success={self.combined_success_pattern is not None}, "
                    f"hints={self.combined_hint_pattern is not None}"
                )
        except Exception as e:
            self.logger.error(f"{self.NAME}: {e}, {traceback.format_exc()}")
            raise e

    @staticmethod
    def get_regex_score(line: str, reg_exp: re.Pattern) -> float:
        """
        Calculate a score for a regex match. Returns 0.999 for a match, 0.001 for no match.
        This method is optimized for performance by eliminating unnecessary variables.

        Args:
            line (str): The line to match.
            reg_exp (re.Pattern): The regular expression pattern.

        Returns:
            float: The score of the match (0.999 for match, 0.001 for no match).
        """
        return 0.999 if reg_exp.search(line) is not None else 0.001

    def get_match(
        self,
        line_number: int,
        line: str,
        pattern: Union[str, re.Pattern],
        dict_section: str = "errors",
        case_sensitive: bool = False,
    ) -> Optional[Dict[str, Any]]:
        """
        Use regular expression matching for fast and efficient pattern detection.
        This method is optimized for performance.

        Args:
            line_number (int): The line number of the line being matched.
            line (str): The content of the line being matched.
            pattern (Union[str, re.Pattern]): The regex pattern string or compiled pattern.
            dict_section (str): The dict_section inside a json dictionary file.
            case_sensitive (bool): Whether to perform case-sensitive matching.

        Returns:
            Optional[Dict[str, Any]]: The result of the match, or None if no match.
        """
        # Use cached/compiled pattern or compile a new one if given a string
        regex_pattern = pattern if isinstance(pattern, re.Pattern) else self.compile_pattern(pattern, case_sensitive)

        # Direct search instead of using get_regex_score for performance
        if regex_pattern.search(line) is not None:
            pattern_str = pattern if isinstance(pattern, str) else pattern.pattern
            return self.get_result(
                line_number,
                line,
                pattern_str,
                0.999,
                self.ANALYSIS_METHOD,
                dict_section,
            )
        return None

    def get_match_line(
        self,
        i: int,
        line: str,
        vocabulary: List[re.Pattern],
        result: List,
        combined_pattern: Optional[re.Pattern] = None,
    ) -> List:
        """
        Check if a line matches any of the regex patterns in the vocabulary.
        Uses combined pattern if available for better performance.

        Args:
            i (int): The line number being processed
            line (str): The line content to check
            vocabulary (List[re.Pattern]): List of compiled regex patterns to check against
            result (List): List to accumulate results
            combined_pattern (Optional[re.Pattern]): Combined pattern to use if available

        Returns:
            List: Updated list of results
        """
        if not vocabulary and not combined_pattern:  # Skip if no patterns to check
            return result

        # Filter line only once per line
        filtered_line = self.lgu.filter_time_stamp(line)
        filtered_line = self.lgu.filter_trash_line(filtered_line, stem=False)

        # Use combined pattern if available (much faster)
        if combined_pattern:
            if combined_pattern.search(filtered_line) is not None:
                result.append(i)
            return result

        # Fall back to individual patterns
        for pattern in vocabulary:
            if pattern.search(filtered_line) is not None:
                result.append(i)
                break  # No need to check other patterns once we've found a match

        return result

    def analyze_log_lines(self) -> Tuple[List[int], List[int], List[int]]:
        """
        Ultra-optimized log analysis that processes only what's needed.

        Returns:
            Tuple[List[int], List[int], List[int]]: Lists of error, hint, and success line numbers
        """
        if not self.log_lines:
            return [], [], []

        # Process only if we have patterns
        has_errors = bool(self.errors)
        has_hints = bool(self.hints)
        has_success = bool(self.success)
        has_fp = bool(self.fs_vocabulary)

        # Early exit if nothing to do
        if not (has_errors or has_hints or has_success):
            return [], [], []

        # Pre-allocate result lists
        errors: List[int] = []
        hints: List[int] = []
        success: List[int] = []

        # Convert to list if needed for direct indexing
        if not isinstance(self.log_lines, list):
            self.log_lines = list(self.log_lines)

        # Compile all patterns once
        error_patterns = [(re.compile(p.pattern, re.IGNORECASE), p.pattern) for p in self.errors] if has_errors else []
        hint_patterns = [(re.compile(p.pattern, re.IGNORECASE), p.pattern) for p in self.hints] if has_hints else []
        success_patterns = [(re.compile(p.pattern, re.IGNORECASE), p.pattern) for p in self.success] if has_success else []
        fp_patterns = [(re.compile(p.pattern, re.IGNORECASE), p.pattern) for p in self.fs_vocabulary] if has_fp else []

        for i, line in enumerate(self.log_lines, start=1):
            if not line.strip():
                continue

            # Filter line only once
            filtered = self.lgu.filter_time_stamp(line)
            filtered = self.lgu.filter_trash_line(filtered, stem=False).lower()

            # Check false positives first
            if has_fp:
                fp_match = False
                for pattern, _ in fp_patterns:
                    if pattern.search(filtered):
                        fp_match = True
                        break
                if fp_match:
                    continue

            # Check success patterns
            if has_success:
                for pattern, _ in success_patterns:
                    if pattern.search(filtered):
                        success.append(i)
                        break

            # Check hint patterns
            if has_hints:
                for pattern, _ in hint_patterns:
                    if pattern.search(filtered):
                        hints.append(i)
                        break

            # Check error patterns
            if has_errors:
                for pattern, _ in error_patterns:
                    if pattern.search(filtered):
                        errors.append(i)
                        break

        return errors, hints, success

    def create_combined_pattern(self, patterns: List[str], case_sensitive: bool = False) -> Optional[re.Pattern]:
        """
        Combine multiple patterns into one regex pattern for faster matching.
        This method creates a single regex pattern with non-capturing groups.
        Uses module-level caching for better performance.

        Args:
            patterns (List[str]): List of regex patterns to combine
            case_sensitive (bool): Whether the pattern should be case sensitive

        Returns:
            Optional[re.Pattern]: Combined pattern or None if no patterns or creation fails
        """
        if not patterns:
            return None

        try:
            prefix = self.NAME[:3].lower()  # Use first 3 chars of analyzer name as prefix
            combined_pattern = get_or_create_combined_pattern(patterns, prefix, case_sensitive)

            if combined_pattern is None and self.verbose:
                self.logger.warning(f"{self.NAME}: Could not create combined pattern for {len(patterns)} patterns. " f"Will use individual patterns.")

            return combined_pattern
        except Exception as e:
            self.logger.warning(f"{self.NAME}: Error creating combined pattern: {e}. " f"Will use individual patterns.")
            return None


# Cache for expensive regex pattern construction
_cached_combined_patterns = {}


def get_or_create_combined_pattern(patterns: List[str], key_prefix: str, case_sensitive: bool = False) -> Optional[re.Pattern]:
    """
    Get a cached combined pattern or create and cache a new one.
    This module-level function improves performance by caching expensive regex compilations.

    Args:
        patterns (List[str]): The patterns to combine
        key_prefix (str): A prefix for the cache key
        case_sensitive (bool): Whether the pattern should be case sensitive

    Returns:
        Optional[re.Pattern]: The combined pattern or None if creation fails
    """
    if not patterns:
        return None

    # Create a unique key for this set of patterns
    patterns_hash = hash(tuple(patterns))
    cache_key = f"{key_prefix}_{patterns_hash}_{int(case_sensitive)}"

    if cache_key not in _cached_combined_patterns:
        try:
            # Limit number of patterns to avoid complexity issues
            MAX_PATTERNS = 200
            if len(patterns) > MAX_PATTERNS:
                patterns = patterns[:MAX_PATTERNS]

            # Use non-capturing groups for better performance
            combined = "|".join(f"(?:{p})" for p in patterns)
            flags = 0 if case_sensitive else re.IGNORECASE
            _cached_combined_patterns[cache_key] = re.compile(combined, flags)
        except re.error:
            # If compilation fails, store None so we don't try again
            _cached_combined_patterns[cache_key] = None

    return _cached_combined_patterns[cache_key]
