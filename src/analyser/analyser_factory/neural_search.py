import time
import traceback
import uuid
from collections import defaultdict
from concurrent.futures import Thread<PERSON><PERSON><PERSON>xecutor
from typing import Any, Optional, Dict, List, Iterator

import ahocorasick
from qdrant_client.models import PointStruct

from analyser.analyser_factory.generic_analyser import GenericAnalyser
from src.api.config.constants import PROGRESS_UPDATE_INTERVAL
from utils.config_reader import Config<PERSON>eader
from utils.default_config import DEFAULT_CONFIG_FILE
from utils.line_cache import LineCache
from utils.qdrant_util import QdrantUtil
from utils.time_monitor import TimeMonitor


class NeuralSearch(GenericAnalyser):
    """
    The NeuralSearch class provides methods for initializing Qdrant, creating embeddings from log lines,
    adding new logs to the Qdrant collection, and searching for the most similar log line.
    """

    NAME = "NeuralAnalyser"
    ANALYSIS_METHOD = "neural"
    VOCABULARY_FILE = "stem_unified.json"
    ERROR_PATTERNS = "stem_errors.json"
    FALSE_ERRORS = "stem_errors_false_positives.json"
    SUCCESS_PATTERNS = "stem_success.json"
    HINTS_PATTERNS = "stem_hints.json"
    QDRANT_SCROLL_TIMEOUT = 60 * 20  # seconds
    MAX_COLLECTION_SIZE = 10000000  # Safety limit for max number of points processed
    UPSERT_BATCH_SIZE = 1024  # Number of points to upsert in a single batch
    COLLECTION_MERGE_BATCH_SIZE = 1024  # Number of points to merge in a single batch

    def __init__(
        self,
        input: str,
        match_at: float,
        verbose: bool,
        output: Optional[str],
        prompt_output: Optional[str],
        training_mode: bool,
        ground_truth_folder: Optional[str],
        rag: Optional[str],
        logger: Optional[Any] = None,
        ini_config: str = str(DEFAULT_CONFIG_FILE),
        cfg: Optional[Any] = None,
        streamer: Optional[Any] = None,
        vocabulary: Optional[str] = None,
        progress_tracker: Optional[Any] = None,
        total_lines: Optional[int] = None,
        **kwargs  # Accept any additional parameters
    ) -> None:
        """
        Constructs all the necessary attributes for the NeuralSearch object.
        """
        super().__init__(
            input,
            match_at,
            verbose,
            output,
            prompt_output,
            training_mode,
            ground_truth_folder,
            rag,
            logger,
            ini_config,
            streamer,
            cfg,
            progress_tracker=progress_tracker,
            total_lines=total_lines,
        )
        if vocabulary:
            self.VOCABULARY_FILE = vocabulary
        self.qu = None
        self.training = None
        self.transformer = None
        self.threads = None
        self.temp_collection = None
        self.streamer = streamer
        self.empty_collection = False
        self.training_mode = training_mode
        self.results = defaultdict(list)
        self.batch_size = self.UPSERT_BATCH_SIZE  # Number of points to upsert in a single batch
        self.futures = None
        self.all_futures_submitted = False
        self.match_at = match_at
        self.log_lines = None

        self.cfg = cfg if cfg else ConfigReader(ini_config, self.logger)
        self.good_collection = self.cfg.get("NEURAL_SEARCH", "good_collection", "good_logs")

        # Load all patterns from unified vocabulary file
        vocab = self.files.get_json(str(self.VOCABULARY_PATH / self.VOCABULARY_FILE))
        self.error_patterns = vocab.get("errors", [])
        self.fp_patterns = vocab.get("false_positives", [])
        self.success_patterns = vocab.get("successes", [])
        self.hint_patterns = vocab.get("hints", [])

        self.stem_cache = LineCache()
        self.training_cache = LineCache(unique=True)

        self.error_automaton = self.build_automaton(self.error_patterns)
        self.fp_automaton = self.build_automaton(self.fp_patterns)
        self.success_automaton = self.build_automaton(self.success_patterns)
        self.hint_automaton = self.build_automaton(self.hint_patterns)

        # For async upsert operations
        self.upsert_executor = None
        self.upsert_futures = {}

    def build_automaton(self, patterns: List[str]) -> ahocorasick.Automaton:
        """
        Build a case-insensitive Aho-Corasick automaton for the given patterns.
        """
        automaton = ahocorasick.Automaton(ahocorasick.STORE_ANY)
        for idx, keyword in enumerate(patterns):
            if keyword:
                automaton.add_word(keyword.casefold(), (idx, keyword))
        automaton.make_automaton()
        return automaton

    def initialize(self) -> None:
        """
        Initialises Qdrant with fastembed embeddings, no sentence transformer is used.
        Args: None
        Returns: None
        """
        self.logger.debug(f"{self.NAME}: Initializing ...")
        if self.training_mode:
            self.logger.info(f"{self.NAME}: MANUAL TRAINING: Activated, all new patterns will be added to vector space.")
            self.logger.warning(f"{self.NAME}: MANUAL TRAINING: Use ONLY with 100% clean, error-free logs, else you risk RISK of model corruption!")
        try:
            port_value = self.cfg.get("NEURAL_SEARCH", "port")
            if port_value is None:
                port_value = 6333  # default Qdrant port or your project default
            else:
                port_value = int(port_value)
            self.qu = QdrantUtil(
                self.logger,
                str(self.cfg.get("NEURAL_SEARCH", "host")),
                port_value,
                str(self.cfg.get("NEURAL_SEARCH", "api_key")),
            )
            self.training = QdrantUtil(self.logger, "memory", 0, None)
            # Check to make sure we have both main and training qdrant instances functioning
            if not self.qu or not self.training:
                raise Exception("Qdrant and Training instances are not initialized correctly ...")

            self.logger.debug(f"{self.NAME}: Caching existing lines in collection {self.good_collection} ...")

            # Creates the collection if it does not exist, otherwise it will not recreate the collection
            self.qu.create(str(self.good_collection), recreate=False)
            # Checks if the good_collection collection is empty
            empty_collection = self.qu.is_collection_empty(str(self.good_collection))
            if not empty_collection:
                # Cache all stemmed lines in the good_collection in stem_cache
                for payload in self.get_collection_lines(str(self.good_collection)):
                    if "document" in payload:
                        self.stem_cache.add_to_lines_set(payload["document"])
                self.logger.debug(f"{self.NAME}: Done caching lines in collection {self.good_collection}.")
            else:
                self.logger.debug(f"{self.NAME}: Collection {self.good_collection} is EMPTY, no lines to cache ...")

            self.temp_collection = self.training.gen_temp_collection_name()
            self.training.create(str(self.temp_collection), recreate=True)
            try:
                collection_info = self.training.qdrant.get_collection(collection_name=str(self.temp_collection))
            except Exception as e:
                # Retry creating the training collection if it was not created properly
                self.training.qdrant.create_collection(
                    collection_name=str(self.temp_collection),
                    vectors_config=self.training.qdrant.get_fastembed_vector_params(),
                    sparse_vectors_config=self.training.qdrant.get_fastembed_sparse_vector_params(),
                )

            if empty_collection:
                self.logger.debug(f"{self.NAME}: Collection '{self.good_collection}' is EMPTY >>> ANALYSIS is NOT possible, only TRAINING is possible ...")

            self.threads = self.cfg.get("NEURAL_SEARCH", "threads")

            if self.streamer:
                self.logger.debug(f"{self.NAME}: Using Streamer input from: {self.streamer.NAME}")
                self.log_lines = []

            # Initialize thread pool for async upsert operations
            self.upsert_executor = ThreadPoolExecutor(
                max_workers=2,  # 2 workers should be sufficient for upsert operations
                thread_name_prefix=f"UPSERT-{uuid.uuid4().hex}",
            )

        except Exception as e:
            self.logger.critical(f"{self.NAME}: Error connecting to Qdrant instance! {e}")
            raise e

    def matches_as(self, line: str, score: int, pattern_type: str) -> bool:
        """
        Checks if the given line matches any keyword in the relevant vocabulary for the given type.
        Uses Aho-Corasick automaton for fast, case-insensitive matching.
        """
        line_lower = line.casefold()
        if pattern_type == "error":
            automaton = self.error_automaton
        elif pattern_type == "fp":
            automaton = self.fp_automaton
        elif pattern_type == "success":
            automaton = self.success_automaton
        elif pattern_type == "hint":
            automaton = self.hint_automaton
        else:
            return False
        # Check with pyahocorasick if the line matches any keyword in the automaton
        for _, _ in automaton.iter(line_lower):
            return True
        return False

    def upsert_training(self, lines: List[str], metadata: List[Dict[str, str]]) -> None:
        """
        Checks the cache to avoid reprocessing lines that have already been added and creates embeddings from new good log lines list and uploads the list to Qdrant.
        Args: lines (list): List of log lines to upsert, metadata (list): List of metadata for the log lines.
        Returns: None
        """
        if lines and self.training is not None and self.temp_collection is not None:
            try:
                self.logger.debug(f"{self.NAME}: Adding {len(lines)} new line patterns to {self.temp_collection} ...")
                self.training.qdrant.add(str(self.temp_collection), lines, metadata)
            except Exception as e:
                self.logger.error(f"{self.NAME}: Error while adding training data: {e}, {traceback.format_exc()}")

    def upsert_training_async(self, lines: List[str], metadata: List[Dict[str, str]]) -> None:
        """
        Submits a batch of training lines to be upserted asynchronously.

        Args:
            lines (list): List of log lines to upsert
            metadata (list): List of metadata for the log lines
        Returns: None
        """
        if not lines or self.training is None or self.temp_collection is None or self.upsert_executor is None:
            return

        # Submit the upsert task to the executor
        future = self.upsert_executor.submit(self.upsert_training, lines, metadata)
        self.upsert_futures[future] = len(lines)

        # Add callback to remove completed futures
        future.add_done_callback(self.upsert_completed)

        self.logger.debug(f"{self.NAME}: Submitted async upsert of {len(lines)} lines")

    def upsert_completed(self, future):
        """Callback for when an upsert operation completes"""
        if future in self.upsert_futures:
            if future.exception():
                self.logger.error(f"{self.NAME}: Async upsert failed: {future.exception()}")
            else:
                batch_size = self.upsert_futures.get(future, 0)
                self.logger.debug(f"{self.NAME}: Async upsert of {batch_size} lines completed")
            # Remove the future
            self.upsert_futures.pop(future, None)

    def classify(self, i: int, line: str, stem: str, results: List[Any]) -> Optional[Dict[str, Any]]:
        """
        Uses the Qdrant DB to search for the most similar log line in the dictionary.
        Args: i (int): Line number, line (str): The log line, stem (str): The stemmed log line, results (list): List of results from Qdrant.
        Returns: dict: A dictionary containing the result of the classification or None if not classified.
        """
        # No results or no match above a threshold
        if not results:
            self.logger.debug(f"(*) {self.NAME}: match:TRAINING > (no score) {i} stem: '{stem}'")
            self.training_cache.add(i, stem)
            return None
        result = results[0]
        score = getattr(result, "score", 0)
        document = getattr(result, "document", "") or ""
        if score >= self.match_at:
            return None
        # Use new matching logic
        if self.matches_as(stem, score, "error") and not self.matches_as(stem, score, "fp"):
            self.logger.debug(f"(!) {self.NAME}: match:ERROR > score: '{score}', {i} '{line.rstrip()[:256]}'")
            return self.get_result(i, line, document, score, self.ANALYSIS_METHOD, "errors")
        if self.matches_as(stem, score, "hint"):
            self.logger.debug(f"(!) {self.NAME}: match:HINT > score: {score}, {i} '{line.rstrip()[:256]}'")
            return self.get_result(i, line, document, score, self.ANALYSIS_METHOD, "hints")
        self.logger.debug(f"(*) {self.NAME}: match:TRAINING > score: {score}, {i} stem: '{stem}'")
        self.training_cache.add(i, stem)
        return None

    def process_line(self, i_line: tuple) -> None:
        """
        Cleans and processes a log line by filtering and matching it.
        Args: i_line (tuple): Tuple containing the line number and the log line.
        Returns: None
        """
        i, line = i_line
        if self.log_lines is None:
            self.log_lines = []
        self.log_lines.append(line)

        # Update progress every PROGRESS_UPDATE_INTERVAL lines
        if (i + 1) % PROGRESS_UPDATE_INTERVAL == 0:
            self.progress_tracker.write("analyzing", i + 1, self.total_lines)

        # Skip empty lines early
        if not line.strip():
            return

        clean_line = self.lgu.filter_trash_line(line)
        stem = self.lgu.stem_log_line(clean_line)

        # Skip empty stems
        if not stem:
            return

        # Skip stems that are already in the cache (e.g. existing lines in the Qdrant collection)
        if self.stem_cache.is_stored(stem):
            return

        # Add the stem to the cache for processing
        self.stem_cache.add(i, stem)

        # Only process caches when batch size is reached to reduce overhead
        if self.stem_cache.get_size() >= self.batch_size:
            self.process_caches()

    @staticmethod
    def is_blank(stem: str) -> bool:
        """
        Check if the line/stem is empty or contains only whitespace.
        Args: stem (str): The stemmed log line.
        Returns: bool: True if the stem is empty, False otherwise.
        """
        return not stem or stem.isspace()

    def process_caches(self, future=None, flush_all=False) -> None:
        """
        Processes the stem_cache and training_cache for matching and training.
        Two main operations:
        1. Process stems from stem_cache: Query Qdrant and classify results
        2. Process training_cache: Upsert new stems to the temp collection

        Args: future (Future, optional): The future object. flush_all (bool): Flush all caches regardless of size.
        """
        # Remove completed future
        if future and self.futures is not None:
            self.futures.pop(future, None)

        # Only process when batch size reached or forced flush (avoid frequent small batches)
        if self.stem_cache.get_size() < self.batch_size and not flush_all:
            return

        # Process stem_cache when batch size reached or forced flush
        if self.stem_cache.get_size() >= self.batch_size or (flush_all and self.stem_cache.get_size()):
            try:
                cache_size = self.stem_cache.get_size()
                self.logger.debug(f"{self.NAME}: Processing {cache_size} stems from cache")

                # Get stems and clear cache
                lines_batch_dict = self.stem_cache.get_all_and_clear()
                stems_list = list(lines_batch_dict.values())

                # Validate Qdrant
                if self.qu is None:
                    self.logger.error(f"{self.NAME}: Qdrant instance unavailable for batch query")
                    return

                # Query Qdrant with stems
                batch_results = self.qu.fastembed_batch_query(str(self.good_collection), stems_list)

                # Process results
                if batch_results and self.log_lines is not None:
                    line_numbers = list(lines_batch_dict.keys())
                    for res_i, matched_result in enumerate(batch_results):
                        line_number = line_numbers[res_i]

                        # Validate index
                        if not (0 <= line_number < len(self.log_lines)):
                            continue

                        stem = lines_batch_dict[line_number]
                        line = self.log_lines[line_number]

                        # Classify (line_number+1 adjusts for 0-based indexing)
                        result = self.classify(line_number + 1, line, stem, matched_result)

                        # Store result
                        if result:
                            self.results.setdefault(result["type"], []).append(result["line"])

            except Exception as e:
                self.logger.error(f"{self.NAME}: Error processing stems: {e}")

        # Process training_cache when batch size reached or forced flush
        if self.training_cache.get_size() >= self.batch_size or (flush_all and self.training_cache.get_size()):
            try:
                cache_size = self.training_cache.get_size()
                self.logger.debug(f"{self.NAME}: Processing {cache_size} training lines")

                # Get training lines and clear cache
                training_lines_dict = self.training_cache.get_all_and_clear()

                # Prepare metadata
                if self.log_lines is not None:
                    meta_data = [{"original": (self.log_lines[i - 1].rstrip() if 0 <= i - 1 < len(self.log_lines) else "")} for i in training_lines_dict.keys()]

                    # Use async upsert instead of blocking call
                    self.upsert_training_async(list(training_lines_dict.values()), meta_data)

            except Exception as e:
                self.logger.error(f"{self.NAME}: Error upserting training lines: {e}")

        # Log completion
        if self.all_futures_submitted and not self.futures:
            if self.log_lines is not None:
                self.logger.debug(f"{self.NAME}: Lines processing COMPLETE >>> {len(self.log_lines)} <<< processed")

    def analyze_threaded(self, iter_lines: Iterator[str], process_function) -> None:
        """
        Analyzes the log lines using neural search in a multithreaded environment.
        Args: iter_lines (iterable): Iterable of log lines, process_function (function): Function to process each log line.
        Returns: None
        """
        threads_val = self.threads if isinstance(self.threads, int) else 1
        if threads_val > 1:
            self.logger.debug(f"{self.NAME}: Starting MULTI-THREADED analysis with {threads_val} threads ...")
            try:
                with ThreadPoolExecutor(
                    max_workers=threads_val,
                    thread_name_prefix=f"NEURAL-{uuid.uuid4().hex}",
                ) as executor:
                    self.futures = {executor.submit(process_function, item): item for item in enumerate(iter_lines, start=0)}
                    futures_copy = self.futures.copy()
                    for future in futures_copy:
                        future.add_done_callback(self.process_caches)
                    self.all_futures_submitted = True
            except Exception as e:
                self.logger.error(f"{self.NAME}: Error in ThreadPoolExecutor: {e}, {traceback.format_exc()}")
                raise e
            finally:
                self.process_caches(flush_all=True)
        else:
            self.logger.debug(f"{self.NAME}: Starting analysis without threading (could take longer than multi-threading) ...")
            for i, line in enumerate(iter_lines, start=0):
                process_function((i, line))
            self.all_futures_submitted = True

    def merge_collections(
        self,
        temp_collection: str,
        existing_collection: str,
        batch_size: int = COLLECTION_MERGE_BATCH_SIZE,
    ) -> None:
        """
        Merges the temp session collection into the existing collection and deletes the temp session collection.
        Args: temp_collection (str): Name of the temporary collection, existing_collection (str): Name of the existing collection, batch_size (int, optional): Batch size for merging. Defaults to 1000.
        Returns: None
        """
        if self.training is None:
            self.logger.error(f"{self.NAME}: Training Qdrant instance is not available.")
            return
        self.logger.debug(f"{self.NAME}: Merging collections '{temp_collection}' :: '{existing_collection}' using a batch size of {batch_size}")
        scroll_position = None
        try:
            while True:
                self.logger.debug(f"{self.NAME}: .: Scrolling through {temp_collection}...")
                response = self.training.qdrant.scroll(
                    collection_name=temp_collection,
                    scroll_filter=None,
                    limit=batch_size,
                    offset=scroll_position,
                    with_vectors=True,
                )
                points = response[0]  # Access the first element of the tuple
                scroll_position = response[1]  # Update the scroll position
                self.logger.debug(f"{self.NAME}: .: Retrieved {len(points)} points from {temp_collection}")

                if not points:
                    break

                def extract_vector(vec):
                    # If already a list of floats
                    if isinstance(vec, list) and all(isinstance(x, float) for x in vec):
                        return vec
                    # If it's a dict with a 'values' or 'vector' key
                    if isinstance(vec, dict):
                        if "values" in vec and isinstance(vec["values"], list) and all(isinstance(x, float) for x in vec["values"]):
                            return vec["values"]
                        if "vector" in vec and isinstance(vec["vector"], list) and all(isinstance(x, float) for x in vec["vector"]):
                            return vec["vector"]
                        # If it's a dict of vectors, take the first
                        for v in vec.values():
                            result = extract_vector(v)
                            if result is not None:
                                return result
                    # If it has a .values attribute (but not a dict)
                    if hasattr(vec, "values") and not isinstance(vec, dict) and isinstance(vec.values, list) and all(isinstance(x, float) for x in vec.values):
                        return vec.values
                    return None

                # Get vector config to extract the name
                collection_info = self.qu.qdrant.get_collection(collection_name=existing_collection)
                vector_name = next(iter(collection_info.config.params.vectors.keys()))

                valid_points = []
                for point in points:
                    vec = None
                    if isinstance(point.vector, dict) and "default" in point.vector:
                        vec = extract_vector(point.vector["default"])
                    elif isinstance(point.vector, dict):
                        vec = extract_vector(next(iter(point.vector.values()), None))
                    else:
                        vec = extract_vector(point.vector)
                    if vec is not None:
                        valid_points.append(
                            PointStruct(
                                id=point.id,
                                vector={vector_name: vec},
                                payload=point.payload,
                            )
                        )
                        # valid_points.append(
                        #     PointStruct(
                        #         id=point.id,
                        #         vector=vec,
                        #         payload=point.payload,
                        #     )
                        # )

                if valid_points:
                    self.logger.info(f"{self.NAME}: .: Upserting {len(valid_points)} points to {existing_collection} ...")
                    # self.logger.info(f"{self.NAME}: .: Points: {valid_points}")
                    if self.qu is None:
                        self.logger.error(f"{self.NAME}: .: (!) Qdrant instance is not available or does not have qdrant attribute.")
                        return
                    upsert_response = self.qu.qdrant.upsert(collection_name=existing_collection, points=valid_points)
                    self.logger.debug(f"{self.NAME}: .: Upsert response: {upsert_response.status}")
                else:
                    self.logger.warning(".: (!) All points have None vectors, consider checking the vector generation process ...")

                # Ensure scroll_position is updated correctly
                if scroll_position is None:
                    # self.logger.debug("Scroll position is None, breaking the loop")
                    break
        except Exception as e:
            self.logger.error(f"{self.NAME}: .: (!) An error occurred during merging collections: {e} {traceback.format_exc()}")
        self.logger.debug(f"{self.NAME}: .: (*) Merging '{temp_collection}' with '{existing_collection}' was successful.")

    def get_collection_lines(
        self,
        collection: str,
        limit: int = MAX_COLLECTION_SIZE,
        timeout_s: int = QDRANT_SCROLL_TIMEOUT,
    ) -> Iterator[Dict[str, str]]:
        """
        Gets all the payloads from a collection.
        Args: collection (str): Name of the remote collection, limit (int, optional): A safety limit, max number of points processed.
        Returns: generator: A generator that yields the payload the collection.
        """
        if self.qu is None:
            self.logger.error(f"{self.NAME}: Qdrant instance is not available.")
            return iter([])
        self.logger.debug(f"{self.NAME}: Getting payloads from: {collection}, limit: {limit}")
        scroll_position = None
        try:
            while True:
                self.logger.debug(f"{self.NAME}: .Scrolling through {collection}...")
                response = self.qu.qdrant.scroll(
                    collection_name=collection,
                    scroll_filter=None,
                    limit=limit,
                    offset=scroll_position,
                    with_vectors=False,
                    timeout=timeout_s,
                )
                points = response[0]  # Access the first element of the tuple
                scroll_position = response[1]  # Update the scroll position
                self.logger.debug(f"{self.NAME}: .Retrieved {len(points)} points from {collection}")

                if not points:
                    break

                for point in points:
                    if point.payload:
                        # self.logger.debug(f"{self.NAME}: .payload: {point.payload}")
                        # payload = {'document': <stem_string>, 'original': <original_log_line_string>}
                        yield point.payload

                if scroll_position is None:
                    break
            self.logger.debug(f"{self.NAME}: Total points {len(points)}")
        except Exception as e:
            self.logger.error(f"{self.NAME}: An error occurred during operation on collections: {e} {traceback.format_exc()}")
        self.logger.debug(f"{self.NAME}: Done operation on {collection}, all good.")
        return None

    def run_post_analysis(self) -> None:
        """
        Runs post-analysis operations, including merging collections and cleaning up temporary collections.
        Args: None
        Returns: None
        """
        # Wait for any pending upsert operations to complete
        if self.upsert_executor is not None:
            self.logger.debug(f"{self.NAME}: Waiting for {len(self.upsert_futures)} pending upsert operations to complete...")
            # Shutdown the executor and wait for all tasks to complete
            self.upsert_executor.shutdown(wait=True)
            self.upsert_executor = None

        if self.training_mode is True:
            self.logger.debug(f"{self.NAME}: TRAINING MODE is <ON> :: new patterns will be added in '{self.good_collection}' collection vector space.")
        else:
            if self.streamer:
                self.logger.debug(f"{self.NAME}: Streamer will auto decide <TRAINING_MODE> ...")
                self.training_mode = self.streamer.trigger_training_mode()

        if self.training is None or self.temp_collection is None:
            self.logger.error(f"{self.NAME}: Training or temp_collection is not available.")
            return
        training_empty = self.training.is_collection_empty(str(self.temp_collection))
        new_patterns_count = 0
        if not training_empty:
            new_patterns_count = self.training.get_collection_size(str(self.temp_collection))

        if not training_empty and self.training_mode:
            self.logger.debug(f"{self.NAME}:  Adding {new_patterns_count} new patterns to the high-dimensional vector space ...")
            self.merge_collections(str(self.temp_collection), str(self.good_collection))
        else:
            if training_empty:
                self.logger.debug(f"{self.NAME}: NO TRAINING DATA :: No new patterns found ...")
            else:
                self.logger.debug(f"{self.NAME}: TRAINING MODE is <OFF> :: Skipping a total of {new_patterns_count} new patterns ...")

        # Clean the temp session collection
        self.training.qdrant.delete_collection(collection_name=str(self.temp_collection))
        self.logger.debug(f"{self.NAME}: Deleted temp_collection: {self.temp_collection}")
        # Clean temp leftover collection
        if self.qu is not None:
            self.qu.clean_temp_collections(days_old=1, minutes_old=0)
        if self.training is not None:
            self.training.clean_temp_collections(days_old=0, minutes_old=1)
        # Free up the memory
        del self.temp_collection
        del self.training

    def analyze(self) -> Dict[str, List[str]]:
        """
        Analyzes the log lines using neural search.
        Args: None
        Returns: dict: The resulting analysis is returned.
        """
        try:
            current_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
            self.logger.debug(f"{self.NAME}: Starting analysis (Current date/time: {current_time}) ...")
            self.initialize()
            self.logger.debug(f"{self.NAME}: Filtering timestamps and trash logs ...")

            with TimeMonitor(f"{self.NAME}: -- Analysis completed in: ", self.logger):
                if self.streamer is None:
                    self.logger.error(f"{self.NAME}: Streamer is not available or does not have stream method.")
                    return {}
                self.analyze_threaded(self.streamer.stream(), self.process_line)

            self.logger.debug(f"{self.NAME}: Running post analysis operations ...")
            self.run_post_analysis()

            # Mark completion
            self.progress_tracker.write("completed", self.total_lines, self.total_lines)

        except Exception as e:
            # Mark as failed on error
            self.progress_tracker.write("failed", self.total_lines, self.total_lines)
            self.logger.error(f"{self.NAME}: {e}, {traceback.format_exc()}")
            raise e
        finally:
            # Ensure all writes complete before analyzer exits
            self.progress_tracker.shutdown()

        return self.lgu.format_result(
            self.results["errors"],
            self.results["hints"],
            self.results["success"],
            str(self.log_file_path),
        )
