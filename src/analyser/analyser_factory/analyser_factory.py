from typing import Any

from analyser.analyser_factory.keywords import Keywords
from analyser.analyser_factory.neural_search import NeuralSearch
from analyser.analyser_factory.regular_expressions import RegularExpressions

# >>> Archived methods, DO NOT REMOVE them! We may reactivate them in the future based on customer request or use cases.
# from archived.patterns import Patterns
# from archived.vectors import Vectors

ANALYZERS = dict(
    keywords=Keywords,
    regexps=RegularExpressions,
    neural=NeuralSearch,
    # >>> Archived methods, DO NOT REMOVE them!
    # patterns=Patterns,
    # vectors=Vectors,
)


class AnalyserFactory:
    """
    The analyser factory class that creates an analyser based on the analysis type.
    """

    NAME = "AnalyserFactory"

    def __init__(self, input: str, analysis_type: str, *args: Any, **kwargs: Any) -> None:
        """
        Initializes the AnalyserFactory with the provided arguments and logger.
        Args: input (str): Path to the input file, analysis_type (str): The type of analysis to be performed, *args: Variable length argument list, **kwargs: Arbitrary keyword arguments.
        Returns: None
        """
        self.input = input
        self.analysis_type = analysis_type
        self.args = args
        self.kwargs = kwargs

    def create(self, analysis_type: str) -> Any:
        """
        Creates an analyser based on the analysis type.
        Args: analysis_type (str): The type of analysis to be performed, *args: Variable length argument list, **kwargs: Arbitrary keyword arguments.
        Returns: Analyser: An instance of the Analyser class.
        Raises: NotImplementedError: If the analysis type is not implemented.
        """
        analysis_type = analysis_type.lower()
        if analysis_type in ANALYZERS.keys():
            return ANALYZERS[analysis_type](self.input, *self.args, **self.kwargs)
        else:
            raise NotImplementedError(f'{self.NAME}: Analysis of type "{analysis_type}" is not yet implemented, possible types are {ANALYZERS.keys()} ...')
