from typing import Optional, Any

import hyperscan

from analyser.analyser_factory.generic_analyser import GenericAnalyser


class Keywords(GenericAnalyser):
    """
    The Keywords class provides methods for keyword matching in log lines.
    """

    NAME = "KeywordsAnalyser"
    ERRORS = "keywords_errors.json"
    FALSE_POSITIVES = "keywords_false_positives.json"
    SUCCESS = "keywords_success.json"
    HINTS = "keywords_hints.json"
    ANALYSIS_METHOD = "keywords"
    VOCABULARY_FILE = "keywords_unified.json"

    def __init__(
        self,
        input: str,
        match_at: float,
        verbose: bool,
        output: Optional[str],
        prompt_output: Optional[str],
        training_mode: bool,
        ground_truth_folder: Optional[str],
        rag: Optional[str],
        logger: Optional[Any] = None,
        ini_config: Optional[Any] = None,
        streamer: Optional[Any] = None,
        cfg: Optional[Any] = None,
        vocabulary: Optional[str] = None,
        use_hyperscan: bool = True,  # Only for Keywords
        progress_tracker: Optional[Any] = None,
        total_lines: Optional[int] = None,
        **kwargs  # Accept any additional parameters
    ) -> None:
        """
        Constructs all the necessary attributes for the Keywords object.
        """
        super().__init__(
            input,
            match_at,
            verbose,
            output,
            prompt_output,
            training_mode,
            ground_truth_folder,
            rag,
            logger,
            ini_config,
            streamer,
            cfg,
            progress_tracker=progress_tracker,
            total_lines=total_lines,
        )
        self.use_hyperscan = use_hyperscan
        if vocabulary:
            self.VOCABULARY_FILE = vocabulary
        self._hs_db = None
        self._hs_patterns = None
        self._hs_ids = None
        if self.use_hyperscan:
            self._init_hyperscan()

    def _init_hyperscan(self):
        """Compile all patterns into a Hyperscan database."""
        # Load vocabularies
        self.get_vocabularies()
        patterns = set()
        if self.errors:
            patterns.update(self.errors)
        if self.fs_vocabulary:
            patterns.update(self.fs_vocabulary)
        if self.success:
            patterns.update(self.success)
        if self.hints:
            patterns.update(self.hints)
        # Remove empty patterns
        patterns = [p for p in patterns if p]
        # Assign unique ids to each pattern
        self._hs_patterns = patterns
        self._hs_ids = list(range(1, len(patterns) + 1))
        expressions = [p.encode("utf8") for p in patterns]
        flags = [hyperscan.HS_FLAG_CASELESS] * len(patterns)
        ids = self._hs_ids
        db = hyperscan.Database()
        db.compile(expressions=expressions, flags=flags, ids=ids)
        self._hs_db = db
        self._hs_id_to_pattern = dict(zip(self._hs_ids, patterns))

    def _hyperscan_match(self, line: str) -> set:
        """Return set of patterns matched in the line using Hyperscan."""
        matches = set()
        if not self._hs_db:
            return matches

        def on_match(id, from_, to, flags, context):
            matches.add(self._hs_id_to_pattern[id])
            return None  # Return None for compatibility

        self._hs_db.scan(line.encode("utf8"), match_event_handler=on_match)
        return matches

    def get_match_line(self, i: int, line: str, vocabulary: Any, result: list) -> list:
        filtered_line = self.lgu.filter_time_stamp(line)
        filtered_line = self.lgu.filter_trash_line(filtered_line, stem=False).lower()
        # Priority: error > success > hint
        match_found = False
        for category, patterns in [
            ("errors", self.errors),
            ("success", self.success),
            ("hints", self.hints),
        ]:
            if not patterns:
                continue
            matches = set()
            if self.use_hyperscan:
                # Use Hyperscan to match patterns in this category
                for pattern in patterns:
                    if pattern and pattern in filtered_line:
                        matches.add(pattern)
            else:
                for pattern in patterns:
                    if pattern and pattern in filtered_line:
                        matches.add(pattern)
            for match in matches:
                if not self.is_false_positive(filtered_line):
                    data = self.get_match(i, filtered_line, match)
                    score = data.get("score") if data else None
                    if data and score is not None and isinstance(score, (int, float)) and score >= self.match_at:
                        result.append(data.get("line"))
                        match_found = True
                        break
            if match_found:
                break
        # Deduplicate result for this line
        if result:
            result[-1:] = list(dict.fromkeys(result[-1:]))
        return result

    def get_vocabularies(self) -> None:
        """Load all keyword vocabularies from a unified file."""
        vocab = self.files.get_json(str(self.VOCABULARY_PATH / self.VOCABULARY_FILE))
        self.errors = vocab.get("errors", [])
        self.fs_vocabulary = vocab.get("false_positives", [])
        self.success = vocab.get("successes", [])
        self.hints = vocab.get("hints", [])
