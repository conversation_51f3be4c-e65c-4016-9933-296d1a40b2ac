import argparse
from argparse import Namespace

from analyser.analyser_factory.analyser_factory import <PERSON><PERSON>YZERS
from analyser.version import VERSION
from utils.default_config import DEFAULT_CONFIG_FILE

ANALYSIS_TYPES = ANALYZERS.keys()
DEFAULT_MATCH = 0.8

# Define the arguments for the command line
ARGUMENTS = [
    Namespace(
        name="input",
        type=str,
        # nargs="?",
        help="Mandatory, the log file to be analysed",
    ),
    Namespace(
        name="-analysis_type",
        long_name="--analysis_type",
        type=str,
        default=None,
        choices=ANALYSIS_TYPES,
        help=f"Mandatory analysis method to use, possible values are: {', '.join(ANALYSIS_TYPES)}",
    ),
    Namespace(name="-output", long_name="--output", type=str, default=None, help="Mandatory, JSON file containing the log analysis, output to console if empty"),
    Namespace(name="-match_at", long_name="--match_at", default=DEFAULT_MATCH, type=float, help=f"Optional, the match scale, default is {DEFAULT_MATCH} (0.01 to 1.00)"),
    Namespace(name="-verbose", long_name="--verbose", help="Optional, verbose mode", default=False, action="store_true"),
    Namespace(name="-rag", long_name="--rag", help="Optional, activate RAG prompter, it needs the -g, --ground_truth_folder to be specified", default=None, action="store_true"),
    Namespace(name="-prompt_output", long_name="--prompt_output", type=str, help="Optional, File to which to output the LLM prompter", default=None),
    Namespace(
        name="-ground_truth_folder",
        long_name="--ground_truth_folder",
        type=str,
        help="Optional, but required for -r, --rag, The path to the folder containing the ground-truths.",
        default=None,
    ),
    Namespace(
        name="-collection", long_name="--collection", type=str, help="Optional, will override the collection name in any config file and environment variable.", default=None
    ),
    Namespace(
        name="-training_mode",
        long_name="--training_mode",
        help="Optional, triggers training mode in neural_search. Assumes logs are successful and train any new log lines to the a good collection in Qdrant",
        action="store_true",
        default=False,
    ),
    Namespace(
        name="-ini_config",
        long_name="--ini_config",
        type=str,
        help="Optional, specify a file (with path) for the configuration, e.g. /configs/custom_config1/config.ini. Default (if not specified) is the 'config.ini' in the of the log-analyzer (parent of analyser) folder.",
        default=DEFAULT_CONFIG_FILE,
    ),
    Namespace(
        name="-streamer", long_name="--streamer", type=str, help=f"Optional use a streamer to stream the log file, possible valid values are {'file', 'jenkins'}", default=None
    ),
    # Jenkins Streamer specific arguments
    Namespace(name="-jenkins_url", long_name="--jenkins_url", type=str, help="Optional, Jenkins URL (required for Jenkins streamer)", default=None),
    Namespace(name="-job_name", long_name="--job_name", type=str, help="Optional, Jenkins job name (required for Jenkins streamer)", default=None),
    Namespace(name="-build_number", long_name="--build_number", type=str, help="Optional, Jenkins build number (required for Jenkins streamer)", default=None),
    Namespace(name="-username", long_name="--username", type=str, help="Optional, Jenkins username (required for Jenkins streamer)", default=None),
    Namespace(name="-api_token", long_name="--api_token", type=str, help="Optional, Jenkins api access token (required for Jenkins streamer)", default=None),
    Namespace(name="-vocabulary", long_name="--vocabulary", type=str, help="Optional, unified vocabulary file for the selected analysis method (overrides default)", default=None),
]


def get_args():
    """Get the arguments from the command line"""
    parser = argparse.ArgumentParser(
        description=f"LogAnalyserSimilarity {VERSION} -- " f"Analyze log files similarity using different algorithms based on phrases dictionaries",
        formatter_class=argparse.RawTextHelpFormatter,
    )
    for argument in ARGUMENTS:
        arg_dict = dict(vars(argument))
        arg_name = arg_dict.pop("name")
        long_name = None
        if "long_name" in arg_dict:
            long_name = arg_dict.pop("long_name")
        if long_name:
            parser.add_argument(arg_name, long_name, **arg_dict)
        else:
            parser.add_argument(arg_name, **arg_dict)
    args = parser.parse_args()
    return args
