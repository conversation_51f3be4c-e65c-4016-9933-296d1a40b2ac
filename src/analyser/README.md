# Log Analyzer

*The primary goal of this tool is to provide anomaly detection in log files, leveraging basic methods and AI/ML techniques for detailed analysis.*

- It can perform analysis using various methods and provide analysis results files.
- It can analyse in real time log files and build logs and provide a summary of the analysis.
- It can also generate portable, interactive HTML visualizations of the analysis.
- It can also generate LLM prompts, simple or using Retrieval Augmented Generation, for translating analysis results into friendly human-readable summaries and recommendations.
- Uses a config file and environment variables for various configs, especially for Qdrant vector, configs see [config.ini](../../config.ini).
- Is capable of generating an LLM prompt, which is used with an LLM to generate descriptions and offer
  potential solutions for detected root causes. The prompt creation process (with and without Retrival Augmented Generation) is facilitated by
  the [Prompter](prompter/README.md) class.

## General Usage

**Note:** For all commands, arguments marked "optional" can be omitted unless required for specific use cases.

**Command**  
**Note:** All arguments marked as optional do not need to be specified unless required for specific functionality.

> **Note**
> With the log analyzer, you can safely use Linux style paths on Windows, the script is smart enough and will handle the conversion automatically.

Windows:

```bash
.\run\windows\analyser.cmd input [-analysis_type {keywords,regexps,neural}] [-output OUTPUT] [-match_at MATCH_AT] [-verbose] [-rag] [-prompt_output PROMPT_OUTPUT] [-ground_truth_folder GROUND_TRUTH_FOLDER] [-training_mode] [-ini_config INI_CONFIG] [-streamer STREAMER] [-jenkins_url JENKINS_URL] [-job_name JOB_NAME] [-build_number BUILD_NUMBER] [-username USERNAME] [-api_token API_TOKEN]     
```

Linux:

```bash
sh ./run/linux/analyser.sh  input [-analysis_type {keywords,regexps,neural}] [-output OUTPUT] [-match_at MATCH_AT] [-verbose] [-rag] [-prompt_output PROMPT_OUTPUT] [-ground_truth_folder GROUND_TRUTH_FOLDER] [-training_mode] [-ini_config INI_CONFIG] [-streamer STREAMER] [-jenkins_url JENKINS_URL] [-job_name JOB_NAME] [-build_number BUILD_NUMBER] [-username USERNAME] [-api_token API_TOKEN]
```

| Argument                                      | Description                                                                                                                                                                               |
|-----------------------------------------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| `input`                                       | The log file to be analyzed                                                                                                                                                               |
| `input`                                       | **Required**. The log file to be analyzed.                                                                                                                                                |
| `-analysis_type, --analysis_type`             | **Required**. Analysis type. Choose from `keywords`, `regexps`, or `neural`.                                                                                                              |
| `-match_at, --match_at`                       | (Optional, required for neural) Match scale (similarity threshold). Default is `0.8` (float, values from `0.01` to `1.00`). Recommended values: `0.95` for analysis; `0.98` for training. |
| `-verbose, --verbose`                         | (Optional) Verbose mode, will automatically trigger DEBUG logging into the log file also                                                                                                  |
| `-rag, --rag`                                 | (Optional) Activate RAG (Retrieval Augmented Generation) prompt, which combines structured data with real-time retrieval. Requires `-ground_truth_folder,--ground_truth_folder`.          |
| `-prompt_output, --prompt_output`             | (Optional) File to which to output the LLM prompt e.g.`./prompt.txt`                                                                                                                      |
| `-ground_truth_folder, --ground_truth_folder` | (Optional) Optional, but required for`-rag,--rag`. The path to the folder containing the ground-truths.                                                                                   |
| `-training_mode, --training_mode`             | (Optional) Triggers training mode in neural_search. Assumes logs are successful and train any new log lines to the good collection in Qdrant.                                             |
| `-ini_config, --ini_config`                   | (Optional) Specify a custom file (with path) for the configuration, e.g.`/configs/custom_config1/config.ini`. Defaults to `config.ini` in parent folder, if not specified                 |
| `-streamer, --streamer`                       | (Optional) Use a streamer to stream the log file, possible valid values are 'file' or 'jenkins'                                                                                           |
| `-jenkins_url, --jenkins_url`                 | (Optional, required for Jenkins streamer) Jenkins URL                                                                                                                                     |
| `-job_name, --job_name`                       | (Optional, required for Jenkins streamer) Jenkins job name                                                                                                                                |
| `-build_number, --build_number`               | (Optional, required for Jenkins streamer) Jenkins build number                                                                                                                            |
| `-username, --username`                       | (Optional, required for Jenkins streamer) Jenkins username                                                                                                                                |
| `-api_token, --api_token`                     | (Optional, required for Jenkins streamer) Jenkins api access token                                                                                                                        |
| `-vocabulary, --vocabulary`                   | (Optional) Unified vocabulary file for the selected analysis method (overrides default per-analyser vocabulary).                                                                          |

## Analysis Method: Binary Keywords

Simple binary keyword match. It matches the lines in the log file against a pre-defined keyword vocabulary. Simple, but very useful and effective in certain use-cases.

![Keywords](../../doc/LogAnalyzer-keywords.drawio.svg)

### Examples Keywords search

Using `keywords` analysis to process logs and save results to a file, generate an LLM prompt, and enable verbose mode:

```bash
.\run\windows\analyser.cmd ./samples/log-10002.txt --analysis_type keywords --output ./analysis-result_log-10002-keywords.json --prompt_output ./prompt_log-10002-keywords.txt --verbose
```

```bash
sh .\run\linux\analyser.sh ./samples/log-10002.txt --analysis_type keywords --output ./analysis-result_log-10002-keywords.json --prompt_output ./prompt_log-10002-keywords.txt --verbose
```

Using `keywords` analysis, output to a file, use RAG (Retrival Augmented Generation) for prompt output, specify ground-truth folder for rag, output prompt to file, verbose mode:

```bash
.\run\windows\analyser.cmd ./samples/log-10002.txt --analysis_type keywords --output ./analysis-result_log-10002-keywords.json  --rag --ground_truth_folder ./samples/ --prompt_output ./prompt_log-10002-keywords_RAG.txt --verbose
```

```bash
sh .\run\linux\analyser.sh ./samples/log-10002.txt --analysis_type keywords --output ./analysis-result_log-10002-keywords.json  --rag --ground_truth_folder ./samples/ --prompt_output ./prompt_log-10002-keywords_RAG.txt --verbose
```

## Analysis Method: Regular Expressions

Match based on regular expressions. It juxtaposes the lines in log files with a list of regular expressions.

![RegularExpressions](../../doc/LogAnalyzer-regexps.drawio.svg)

### Examples Regular Expressions search

Regular expressions match and output result to a file and LLM output to console:

```bash
.\run\windows\analyser.cmd ./samples/log-10002.txt --analysis_type regexps --verbose --output ./analysis-result_log-10002-regexps.json --prompt_output ./prompt_log-10002-regexps.txt
```

```bash
sh .\run\linux\analyser.sh ./samples/log-10002.txt --analysis_type regexps --verbose --output ./analysis-result_log-10002-regexps.json --prompt_output ./prompt_log-10002-regexps.txt
```

## Analysis Method: Neural Search

**Note:** Neural search is a technique that relies on trained collections of known good log lines represented in a multidimensional vector space.

## Analysis Method: Neural Search

![NeuralSearch](../../doc/LogAnalyzer-neural.drawio.svg)

**Prerequisites:** Ensure your [config.ini](../../config.ini) and environment variables are properly configured for a Qdrant vector database instance.  
The Qdrant api_key should **always** be provided in the environment variables and **never hardcoded**. To get accurate results, train the model with "good" logs—logs without errors
or failures. Avoid training with "bad" logs as it may hinder anomaly detection.

Based on log lines trainings it builds a collections of the representations of know good log lines in a multidimensional vector space.

- It uses neural searches and similarity scores to determine anomalies in new log lines.
- It has learning capability and adjusting the learned patterns based on human user input (in development).
- It creates a representation of a neural model inside the Vector DB and uses it for analysis.
- The vector Database (unlike trained models) is fully transparent, dynamic, flexible and can be adjusted on the fly, thus cheap to maintain in comparison to trained model.
- It is a very powerful tool for log analysis, but it requires training and some CPU power for generation embedding on the fly.
- Uses a Qdrant vector database server instance (ideally with scalable resources) which is configured in [config.ini](../../config.ini) and environment variables.

For a new project, you will need to start a good collection and train it with successful build logs (by running the analyzer in training mode) and analyze the failed logs as usual.
The more the neural model is trained, the better the analysis will be and the better it will identify anomalies and errors.

### Prerequisite Model

The **neural search method** requires the 'fast-bge-small-en' model (BAAI/bge-small-en).  
**Offline Setup:** If offline, set `FASTEMBED_CACHE_PATH` to the folder containing the model. Ensure the folder includes 'fast-bge-small-en' with both `.json` and `.onnx` files.  
**Example:** For `FASTEMBED_CACHE_PATH='c:\temp\'`, ensure `c:\temp\fast-bge-small-en` contains these files.

## Examples of usage

Use 'good' (without errors or failures) samples log files to train a collection in the multidimensional vector space of the vector DB. Normally, you would use multiple runs on
different "good" log files to train. The matching score for training should be about 9.8 to 9.9. **Running a train command will train a collection in the vector database. You risk
collection contamination with noise if you run this command with 'bad or failed' logs. In such cases you need to restore the collection to a 'good' clean state to achieve good
results in searches (if you have a clean saved snapshot of the collection).**

### Neural Training

Train the neural search engine to identify patterns by processing "good" logs (logs without errors or failures).  
**Caution:** Ensure logs are error-free to avoid contaminating the collection. Check configurations before proceeding. Example command:

```bash
.\run\windows\analyser.cmd ./samples/log-10117.txt --analysis_type neural --training_mode --output ./analysis-result_log-10117-neural.json --match_at 0.98 --prompt_output ./log-10117_prompt_training.txt --verbose
```

```bash
sh .\run\linux\analyser.sh ./samples/log-10117.txt --analysis_type neural --training_mode --output ./analysis-result_log-10117-neural.json --match_at 0.98 --prompt_output ./log-10117_prompt_training.txt --verbose
```

You should train only with good logs and selecting the appropriate collection for the appropriate project in the config.ini or in the environment variables.

### Neural Search

Use trained collections to analyze log files. The matching score should be about 0.95. Running a search command will analyze the log file using the trained collection.

You can test the neural search method with the good_collection `good_collection_hydra`on `xc-cp-qdrant-0` Qdrant Vector Database Server and Hydra logs (log-10XXX.txt) from the
sample folder [samples](../samples). You just need to configure it first in the [config.ini](../../config/config.ini) ([NEURAL_SEARCH] good_collection = good_collection_hydra) or
environment variables (NEURAL_SEARCH_GOOD_COLLECTION=good_collection_hydra).

```bash
.\run\windows\analyser.cmd ./samples/log-10117.txt --analysis_type neural --match_at 0.95 --output ./analysis-result_1_log-10117-neural.json  --prompt_output ./log-10117_neural_prompt_2.txt --verbose --collection good_collection_hydra
```

```bash
sh .\run\linux\analyser.sh ./samples/log-10117.txt --analysis_type neural --match_at 0.95 --output ./analysis-result_1_log-10117-neural.json  --prompt_output ./log-10117_neural_prompt_2.txt --verbose --collection good_collection_hydra
```

Same as above but with RAG (Retrival Augmented Generation) prompt output, specify ground-truth folder for rag, verbose mode:

```bash
.\run\windows\analyser.cmd ./samples/log-10117.txt --analysis_type neural --match_at 0.95 --output ./analysis-result_2_log-10117-neural.json --prompt_output ./log-10117_neural_prompt_rag.txt --verbose --rag --ground_truth_folder ./samples/ --collection good_collection_hydra
```

```bash
sh .\run\linux\analyser.sh ./samples/log-10117.txt --analysis_type neural --match_at 0.95 --output ./analysis-result_2_log-10117-neural.json --prompt_output ./log-10117_neural_prompt_rag.txt --verbose --rag --ground_truth_folder ./samples/ --collection good_collection_hydra
```

Using neural search analysis, output to a file, match scale 0.95, use RAG (Retrival Augmented Generation) for prompt
output, specify ground-truth folder for rag, output prompt to file, verbose mode, use custom configuration ini file:

```bash
.\run\windows\analyser.cmd ./samples/log-10117.txt --analysis_type neural --output ./analysis-result_3_log-10117-neural.json --match_at 0.95 --verbose --prompt_output ./log-10117-neural_prompt_rag.txt --rag --ground_truth_folder ./samples --ini_config ./samples/custom_config.ini --verbose --collection good_collection_hydra
```

```bash
sh .\run\linux\analyser.sh ./samples/log-10117.txt --analysis_type neural --output ./analysis-result_3_log-10117-neural.json --match_at 0.95 --verbose --prompt_output ./log-10117-neural_prompt_rag.txt --rag --ground_truth_folder ./samples --ini_config ./samples/custom_config.ini --verbose --collection good_collection_hydra
```

## Neural real-time analysis

Neural search method has implemented live streamers for files and Jenkins builds meaning it can analyze in paralel while those are still runining, however the analysis result and
the upserting of new training data will be done only after the stream completition.

### Jenkins

```bash
.\run\windows\analyser.cmd ./log_stream_1.txt --analysis_type neural --output ./jenkins_streamer_test_1.json --match_at 0.95 --verbose --prompt_output ./prompt_jenkins_streamer_1.txt --training_mode --streamer jenkins --jenkins_url "https://rb-jmaas.de.bosch.com/SWAC_prod/job/swacc_log_parser" --job_name "KnutGPT-Analyzer-TEST" --build_number 62 --username "<USERNAME>" --api_token "<API_TOKEN>"
```

```bash
sh .\run\linux\analyser.sh ./log_stream_1.txt --analysis_type neural --output ./jenkins_streamer_test_1.json --match_at 0.95 --verbose --prompt_output ./prompt_jenkins_streamer_1.txt --training_mode --streamer jenkins --jenkins_url "https://rb-jmaas.de.bosch.com/SWAC_prod/job/swacc_log_parser" --job_name "KnutGPT-Analyzer-TEST" --build_number 62 --username "<USERNAME>" --api_token "<API_TOKEN>"
```

### File

All files are loaded by default with the file streamer for Neural analysis.

### File Watcher

*This feature is under development and will be included in future updates.*

| Date       | Author               | Reference |
|------------|----------------------|-----------|
| 2025-01-24 | Mihai-Ciprian Chezan | CHM1LUD   |
| 2025-06-05 | Alban Courpon        | COA1LUD   |
