"""
Simple progress tracker with file locking for log analysis jobs.
"""

import json
import time
from datetime import datetime, timezone
from pathlib import Path
from typing import Dict, Optional, Any
from concurrent.futures import ThreadPoolExecutor


class ProgressTracker:
    """Simple progress tracker with file locking mechanism."""

    LOCK_WAIT_TIMEOUT = 5  # seconds to wait for lock release

    def __init__(self, output: str):
        """
        Initialize ProgressTracker.
        
        Args:
            progress_file_path (str): Path to the progress.json file
        """
        self.output = output
        self.progress_dir = Path(self.output).parent
        self.progress_file = Path(self.progress_dir) / Path("progress.json")
        self.lock_file = self.progress_file.with_suffix('.lock')
        self.temp_file = self.progress_file.with_suffix('.tmp')

        # Ensure parent directory exists
        self.progress_file.parent.mkdir(parents=True, exist_ok=True)

        # Single background thread for async writes
        self._write_executor = ThreadPoolExecutor(max_workers=1, thread_name_prefix="ProgressWriter")

    def _acquire_lock(self) -> bool:
        """Try to acquire lock by creating lock file."""
        try:
            self.lock_file.touch(exist_ok=False)
            return True
        except FileExistsError:
            return False

    def _release_lock(self):
        """Release lock by removing lock file."""
        try:
            self.lock_file.unlink(missing_ok=True)
        except Exception:
            pass  # Ignore cleanup errors

    def _wait_for_lock_release(self) -> bool:
        """Wait for lock to be released, return True if successful."""
        start_time = time.time()
        retry_intervals = [0.1, 0.2, 0.4, 0.8, 1.6]  # exponential backoff
        retry_index = 0

        while time.time() - start_time < self.LOCK_WAIT_TIMEOUT:
            if not self.lock_file.exists():
                return True

            # Use exponential backoff, then stick to last interval
            interval = retry_intervals[min(retry_index, len(retry_intervals) - 1)]
            time.sleep(interval)
            retry_index += 1

        return False

    def write(self,
              status: str,
              lines_processed: int = 0,
              total_lines: Optional[int] = None) -> None:
        """
        Write progress data asynchronously - returns immediately.

        Args:
            status (str): Current status
            lines_processed (int): Lines processed so far
            total_lines (int, optional): Total lines to process
        """
        # Submit to background thread, return immediately
        self._write_executor.submit(self._write_sync, status, lines_processed, total_lines)

    def _write_sync(self,
                   status: str,
                   lines_processed: int = 0,
                   total_lines: Optional[int] = None) -> bool:
        """
        Synchronous write implementation (runs in background thread).

        Args:
            status (str): Current status
            lines_processed (int): Lines processed so far
            total_lines (int, optional): Total lines to process

        Returns:
            bool: True if successful
        """
        # Wait for lock to be released, then try to acquire
        if self.lock_file.exists():
            if not self._wait_for_lock_release():
                return False  # Timeout waiting for lock

        if not self._acquire_lock():
            return False

        try:
            # Auto-calculate percentage
            if total_lines and total_lines > 0:
                percentage = int((lines_processed / total_lines) * 100)
                percentage = max(0, min(100, percentage))
            else:
                percentage = 0

            progress_data = {
                "file": Path(self.output).name,
                "status": status,
                "percentage": percentage,
                "lines_processed": lines_processed,
                "total_lines": total_lines,
                "updated": datetime.now(timezone.utc).isoformat()
            }

            # Write to temp file then rename (atomic operation)
            self.temp_file.write_text(
                json.dumps(progress_data, separators=(',', ':')),
                encoding='utf-8'
            )
            self.temp_file.replace(self.progress_file)
            return True

        except Exception:
            return False
        finally:
            self._release_lock()

    def read(self) -> Optional[Dict[str, Any]]:
        """
        Read current progress data.
        
        Returns:
            dict: Progress data if available, None otherwise
        """
        # Wait for lock to be released if it exists
        if self.lock_file.exists():
            if not self._wait_for_lock_release():
                return None  # Timeout waiting for lock

        try:
            if not self.progress_file.exists():
                return None

            return json.loads(self.progress_file.read_text(encoding='utf-8'))

        except (json.JSONDecodeError, OSError):
            return None

    def shutdown(self):
        """
        Shutdown the background writer thread.

        Should be called when done with the ProgressTracker to ensure
        all pending writes are completed.
        """
        self._write_executor.shutdown(wait=True)
