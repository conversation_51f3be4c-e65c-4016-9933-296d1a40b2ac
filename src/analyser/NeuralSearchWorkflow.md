# NeuralSearch Class: Workflow & Logic

## 1. **Initialization**
- Loads configuration and sets up logging, batch sizes, and other parameters.
- Loads vocabularies (error, false positive, success, hint) as keyword lists.
- Builds Aho-Corasick automatons for fast keyword matching.
- Initializes two Qdrant clients:
  - `self.qu`: Remote Qdrant (persistent vector DB)
  - `self.training`: In-memory Qdrant (fast, temporary for current session)
- Loads all existing points from the remote Qdrant collection into the in-memory Qdrant and populates the stem cache with known lines.

## 2. **Log Analysis**
- Iterates over log lines to be analyzed (from a streamer or file).
- For each line:
  - Cleans and stems the line.
  - Skips empty or already-seen lines (using the stem cache).
  - Adds new stems to the cache for batch processing.

## 3. **Batch Processing**
- When the cache reaches the batch size (or at the end), processes the batch:
  - **Qdrant Similarity Search:** Queries the in-memory Qdrant for similar lines using embeddings.
  - **Classification:** For each result, if the similarity score is below a threshold, the line is considered an anomaly.
    - Uses fast keyword matching (Aho-Corasick) to classify anomalies as error, hint, etc.
    - Adds anomalies to the training cache for potential future training.
  - **Training Data Upsert:** If in training mode, new anomalies are embedded and upserted into the in-memory Qdrant (this is the slow part).

## 4. **Post-Analysis**
- If in training mode and new patterns were found, merges the in-memory Qdrant collection into the remote Qdrant collection (making new knowledge persistent).
- Cleans up temporary collections and memory.

## 5. **Threading**
- Supports multi-threaded processing of log lines for speed, but embedding/upsert is still a bottleneck due to model computation.



## Key Points

- **Purpose:** Detects anomalies in logs by comparing new lines to known (vectorized) lines, classifies them, and (optionally) adds new patterns to the knowledge base.
- **Speed:** Matching and classification are fast; embedding/upsert is slow due to neural model computation.
- **Design:** Uses in-memory Qdrant for fast session analysis, then syncs to remote Qdrant for persistence.
- **Training Mode:** Controls whether new anomalies are actually added to the knowledge base.
