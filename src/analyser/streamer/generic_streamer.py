from abc import ABC, abstractmethod
from typing import Any


class GenericStreamer(ABC):
    """
    Abstract base class for log streamers.
    """

    @abstractmethod
    def stream(self) -> Any:
        """
        Stream log lines.
        """
        pass

    @abstractmethod
    def trigger_training_mode(self) -> Any:
        """
        Trigger training mode if applicable.
        """
        pass
