import time
import traceback
import uuid
from typing import Any, Iterator, Optional

import certifi
import requests

from analyser.streamer.generic_streamer import GenericStreamer
from utils.files import Files
from utils.logger import Logger


class JenkinsStreamer(GenericStreamer):
    """
    Streams logs from a <PERSON> job using the Jenkins API.
    """

    NAME = "JenkinsStreamer"
    DEFAULT_REQUEST_TIMEOUT = 10  # Seconds
    POLL_INTERVAL = 10  # Seconds to wait before polling again
    MAX_REDIRECTS = 5  # <PERSON> redirects before giving up
    REDIRECT_DELAY = 2  # Seconds to wait after each redirect
    LOG_LEVEL = Logger.INFO

    def __init__(self, **kwargs: Any) -> None:
        """
        Initialize JenkinsStreamer.

        Args:
            jenkins_url (str): Jenkins server URL.
            job_name (str): Jenkins job name.
            build_number (str|int): Jenkins build number.
            auth (tuple): (username, token) for authentication.
            logger (Logger, optional): Logger instance.
            timeout (int, optional): Request timeout in seconds.
            file_path (str, optional): Local file to save streamed logs.
        """
        logger = kwargs.get("logger")
        if not isinstance(logger, Logger):
            logger = Logger(
                name=f"JenkinsStreamer-{uuid.uuid4().hex}",
                logfile="JenkinsStreamer.log",
                level=self.LOG_LEVEL,
            )
        self.logger: Logger = logger
        self.jenkins_url: Optional[str] = kwargs.get("jenkins_url")
        self.job_name: Optional[str] = kwargs.get("job_name")
        self.build_number: Optional[str] = kwargs.get("build_number")
        self.auth: Optional[Any] = kwargs.get("auth")
        self.user, self.token = self.auth if self.auth else (None, None)
        self.timeout: int = kwargs.get("timeout", 5)
        self.completed: bool = False
        self.session = requests.Session()
        self.files = Files(self.logger)
        self.save_to_file: Optional[str] = kwargs.get("file_path")
        self.headers = {"Authorization": f"Bearer {self.token}"}

    def stream(self) -> Iterator[str]:
        """
        Stream logs from Jenkins job.

        Yields:
            str: Next log line.
        """
        log_url = f"{self.jenkins_url}/job/{self.job_name}/{self.build_number}/logText/progressiveText"
        start = 0
        line_count = 0
        pending_line = ""
        redirect_count = 0
        if self.save_to_file is not None:
            self.files.recreate_file(self.save_to_file)

        while not self.completed:
            try:
                self.logger.debug(f"{self.NAME}: Streaming logs from {log_url}, starting at byte offset {start}")

                if log_url is None:
                    self.logger.error(f"{self.NAME}: log_url is None, cannot stream.")
                    break
                response = self.session.get(
                    log_url,
                    params={"start": start},
                    headers=self.headers,
                    timeout=self.DEFAULT_REQUEST_TIMEOUT,
                    verify=False,
                    # verify=certifi.where(),
                    allow_redirects=False,
                )

                if response.status_code in (301, 302):
                    log_url = response.headers.get("Location")
                    self.logger.debug(f"{self.NAME}: Redirected to: {log_url}")

                    if redirect_count >= self.MAX_REDIRECTS:
                        self.logger.debug(f"{self.NAME}: Maximum redirects reached. Giving up.")
                        break

                    redirect_count += 1
                    time.sleep(self.REDIRECT_DELAY)  # Wait for a few moments after each redirect
                    continue

                if response.status_code == 401:
                    self.logger.error(f"{self.NAME}: Authentication failed. Please check your credentials.")
                    break

                response.raise_for_status()

                log_chunk = response.content.decode("utf-8")
                lines = log_chunk.splitlines()

                if pending_line and lines:
                    lines[0] = pending_line + lines[0]

                if log_chunk and log_chunk[-1] != "\n" and lines:
                    pending_line = lines.pop()
                else:
                    pending_line = ""

                for line in lines:
                    line_count += 1
                    if self.save_to_file is not None:
                        self.files.write_file(f"{line}\n", self.save_to_file, file_mode="a", silent=True)
                    yield line

                new_start = int(response.headers.get("X-Text-Size", start))

                if "X-More-Data" not in response.headers:
                    self.logger.debug(f"{self.NAME}: Build completed, stopping stream.")
                    break

                start = new_start
                redirect_count = 0  # Reset on good fetch

            except requests.exceptions.RequestException as e:
                self.logger.debug(f"{self.NAME}: Error: {e}\n{traceback.format_exc()}")
                break

        self.logger.debug(f"{self.NAME}: Streaming completed, total lines: {line_count}")
        self.completed = True

    def trigger_training_mode(self) -> bool:
        """
        Check if Jenkins build completed successfully.

        Returns:
            bool: True if build result is SUCCESS.
        """
        if self.completed:
            try:
                build_url = f"{self.jenkins_url}/job/{self.job_name}/{self.build_number}/api/json"
                response = requests.get(build_url, auth=self.auth, verify=True)
                response.raise_for_status()
                build_info = response.json()
                return build_info["result"] == "SUCCESS"
            except requests.exceptions.RequestException as e:
                self.logger.error(f"{self.NAME}: Error: {e}, {traceback.format_exc()}")
        return False

    def get_completed(self) -> Optional[Any]:
        """
        Get Jenkins build completion status.

        Returns:
            Any: Build info or None.
        """
        api_url = f"{self.jenkins_url}/job/{self.job_name}/{self.build_number}/api/json"
        response = requests.get(
            api_url,
            headers=self.headers,
            timeout=self.DEFAULT_REQUEST_TIMEOUT,
            verify=certifi.where(),
            allow_redirects=False,
        )

        if response.status_code == 200:
            job_info = response.json()
            return job_info["result"] is not None
        return False


if __name__ == "__main__":
    import logging

    # Setup logging
    logging.basicConfig(level=logging.DEBUG)
    logger = logging.getLogger("JenkinsStreamerDebug")

    # Default parameters
    jenkins_url = "https://rb-jmaas.de.bosch.com/SWAC_prod/job/swacc_log_parser"
    job_name = "KnutGPT-Analyser-TEST"
    build_number = "63"
    auth = ("", "")
    timeout = 3

    # Initialize the JenkinsStreamer with keyword arguments
    streamer = JenkinsStreamer(
        logger=logger,
        jenkins_url=jenkins_url,
        job_name=job_name,
        build_number=build_number,
        auth=auth,
        timeout=timeout,
    )

    # Start streaming logs
    with open("streamer/log_output.txt", "w", encoding="utf-8") as log_file:
        for log_line in streamer.stream():
            log_file.write(log_line + "\n")
