import time
import uuid
from typing import Any, Iterator, Optional

from analyser.streamer.generic_streamer import GenericStreamer
from utils.logger import Logger


class FileStreamer(GenericStreamer):
    """
    Streams lines from a file with an optional delay between lines. Useful for large or real-time log files.
    """

    NAME = "FileStreamer"
    LOG_LEVEL = Logger.INFO

    def __init__(self, **kwargs: Any) -> None:
        """
        Initialize FileStreamer.

        Args:
            logger (Logger, optional): Logger instance.
            file_path (str, optional): Path to the file to stream.
            timeout (int, optional): Delay between lines in seconds. Default is 1.
            trigger (bool, optional): Training mode trigger. Default is False.
        """
        logger = kwargs.get("logger")
        if not isinstance(logger, Logger):
            logger = Logger(
                name=f"FileStreamer-{uuid.uuid4().hex}",
                logfile="FileStreamer.log",
                level=self.LOG_LEVEL,
            )
        self.logger: Logger = logger
        self.file_path: Optional[str] = kwargs.get("file_path")
        self.timeout: int = kwargs.get("timeout", 1)
        self.completed: bool = False
        self.line: str = ""
        self.trigger: bool = kwargs.get("trigger", False)

    def stream(self) -> Iterator[str]:
        """
        Stream lines from the file.

        Yields:
            str: Next line from the file.
        """
        if not self.file_path:
            self.logger.error(f"{self.NAME}: No file_path provided.")
            return
        try:
            with open(self.file_path, "r", encoding="utf-8", errors="ignore") as file:
                self.logger.debug(f"{self.NAME}: Streaming file: {self.file_path}")
                for line in file:
                    self.line = line
                    yield line
                    if self.timeout:
                        time.sleep(self.timeout)
            self.completed = True
        except (OSError, FileNotFoundError) as e:
            self.logger.error(f"{self.NAME}: Error: {e}")

    def trigger_training_mode(self) -> bool:
        """
        Check if training mode should be triggered.

        Returns:
            bool: True if training mode is enabled.
        """
        return self.trigger
