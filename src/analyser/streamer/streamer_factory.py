from typing import Any, Optional

from analyser.streamer.file_streamer import FileStreamer
from analyser.streamer.jenkins_streamer import JenkinsStreamer

STREAMERS = dict(file=FileStreamer, jenkins=JenkinsStreamer)


class StreamerFactory:
    """
    Factory for creating streamer instances.
    """

    NAME = "StreamerFactory"

    def create(self, streamer_type: Optional[str], **kwargs: Any) -> Optional[Any]:
        """
        Create a streamer instance.

        Args:
            streamer_type (str): Type of streamer ('file' or 'jenkins').
            **kwargs: Arguments for streamer constructor.

        Returns:
            Any: Streamer instance or None.
        """
        if not streamer_type:
            return None
        streamer_type = streamer_type.lower()
        if streamer_type in STREAMERS.keys():
            return STREAMERS[streamer_type](**kwargs)
        else:
            raise NotImplementedError(
                f'{self.NAME}: Streamer of type "{streamer_type}" is not yet implemented, possible types are {STREAMERS.keys()} ...'
            )
