import json
import logging
import threading
import uuid
from pathlib import Path
from typing import Any, Optional, Dict

from analyser.analyser_factory.analyser_factory import AnalyserFactory
from analyser.args import DEFAULT_MATCH, get_args
from analyser.progress_tracker import ProgressTracker
from analyser.streamer.streamer_factory import StreamerFactory
from analyser.version import VER<PERSON><PERSON>
from prompter.prompter import Prompter
from utils.config_reader import ConfigReader
from utils.default_config import DEFAULT_CONFIG_FILE
from utils.files import Files
from utils.logger import Logger
from utils.time_monitor import TimeMonitor, get_hw_spec

NAME = "LogAnalyser"


class LogAnalyser:
    """
    Handles log analysis with configurable settings.

    The LogAnalyser class provides functionality to analyze logs based on various
    configurations such as analysis type, verbosity, RAG configurations, and more.
    It supports input from a file or directory and outputs the analysis results to
    a desired location or console. The class includes support for custom vocabularies
    and real-time log streaming.
    """

    NAME = NAME
    LOG_LEVEL = Logger.INFO

    def __init__(
        self,
        input: str,
        analysis_type: Optional[str] = None,
        match_at: float = DEFAULT_MATCH,
        verbose: bool = False,
        output: Optional[str] = None,
        prompt_output: Optional[str] = None,
        training_mode: bool = False,
        ground_truth_folder: Optional[str] = None,
        rag: Optional[str] = None,
        ini_config: Path = DEFAULT_CONFIG_FILE,
        streamer: Any = None,
        logger: Optional[Logger] = None,
        collection: Optional[str] = None,
        vocabulary: Optional[str] = None,
    ):
        """
        Initialize the LogAnalyser class with the provided parameters.

        Parameters:
            input (str): Path to the input log file or directory.
            analysis_type (str, optional): Type of analysis to perform.
            match_at (float, optional): Matching criteria for the analysis.
            verbose (bool, optional): Enable verbose logging.
            output (str, optional): Path to the output file.
            prompt_output (str, optional): Path to the prompter output file.
            training_mode (bool, optional): Enable training mode.
            ground_truth_folder (str, optional): Path to the ground truth folder.
            rag (str, optional): RAG (Retrieval-Augmented Generation) configuration.
            ini_config (Path, optional): Path to the INI configuration file.
            streamer (Streamer, optional): Streamer instance for real-time log streaming.
            logger (Logger, optional): Logger instance for logging messages.
            collection (str, optional): Name of the collection for neural search.
            vocabulary (str, optional): Path to the vocabulary file.

        """
        self.logger = (
            logger
            if logger
            else Logger(
                name=f"LogAnalyser-{uuid.uuid4().hex}",
                logfile="LogAnalyser.log",
                level=self.LOG_LEVEL,
            )
        )
        self.files = Files(self.logger)
        self.input = str(input)
        self.match_at = float(match_at)
        self.verbose = bool(verbose)
        self.analysis_type = str(analysis_type) if analysis_type is not None else "default"
        self.output = str(output) if output else None
        self.prompt_output = str(prompt_output) if prompt_output else None
        self.training_mode = bool(training_mode)
        self.ground_truth_folder = str(ground_truth_folder) if ground_truth_folder else None
        self.rag = str(rag) if rag else None
        self.ini_config = str(ini_config) if ini_config is not None else str(DEFAULT_CONFIG_FILE)
        self.streamer = streamer

        self.cfg = ConfigReader(ini_config, self.logger)
        if collection:
            self.cfg.set("NEURAL_SEARCH", "good_collection", str(collection))

        if self.verbose:
            self.logger.logger.setLevel(Logger.DEBUG)

        self.vocabulary = str(vocabulary) if vocabulary else None

    def analyze(self) -> Dict:
        """
        Analyze the log files based on the provided arguments.

        Returns:
            dict: Result of the analysis.
        """
        self.logger.info(f"{self.NAME}: Performing analysis with Analyser {VERSION} -- analysis type: {self.analysis_type}")

        # Setup progress tracking

        progress_tracker = ProgressTracker(str(self.output))
        total_lines = self.files.count_lines_fast(self.input)
        progress_tracker.write("starting", 0, total_lines)

        factory = AnalyserFactory(
            input=self.input,
            analysis_type=self.analysis_type,
            match_at=self.match_at,
            verbose=self.verbose,
            output=self.output,
            prompt_output=self.prompt_output,
            training_mode=self.training_mode,
            ground_truth_folder=self.ground_truth_folder,
            rag=self.rag,
            logger=self.logger,
            ini_config=self.ini_config,
            streamer=self.streamer,
            cfg=self.cfg,
            vocabulary=self.vocabulary,
            progress_tracker=progress_tracker,
            total_lines=total_lines,
        )
        analyser = factory.create(self.analysis_type)
        return analyser.analyze()

    def output_result(self, result: Dict) -> None:
        """
        Output the result of the analysis.

        Parameters:
            result (dict): Result of the analysis to be output.
        """
        result_json = json.dumps(result, indent=2)
        if self.output:
            self.files.write_file(result_json, self.output)
        else:
            print(result_json)


def stream_logs(streamer: Any) -> None:
    """Stream logs using the provided streamer."""
    streamer.stream()


def run_analysis(analyser: LogAnalyser) -> None:
    """Run the log analysis based on the provided arguments."""
    analysis = analyser.analyze()
    analyser.logger.info("<<< Analysis: COMPLETED")
    analyser.output_result(analysis)

    generate_prompt(analyser, analysis)
    analyser.logger.info("<<< LLM Prompt generation: COMPLETED")


def generate_prompt(analyser: LogAnalyser, analysis: Dict) -> None:
    """Generate a prompt based on the analysis results."""
    pg = Prompter(
        max_solution_length=1200,
        max_description_length=1200,
        logger=analyser.logger,
        ini_config=analyser.ini_config,
    )
    analysis_with_lines = pg.lg.get_analysis_lines(analyser.input, analysis)
    prompt = pg.generate(analysis_with_lines, analyser.ground_truth_folder, bool(analyser.rag))

    if not analyser.prompt_output:
        print(f"\n{prompt}")
    else:
        pg.files.write_file(prompt, analyser.prompt_output)


def get_streamer(args: Any, logger: Logger) -> Optional[Any]:
    """
    Create and return a streamer instance based on the provided arguments.

    Parameters:
        args (Namespace): Command-line arguments containing streamer configuration.
        logger (Logger): A logger instance

    Returns:
        Streamer: An instance of the Streamer class if streamer argument is provided, otherwise None.
    """
    if args.streamer:
        return StreamerFactory().create(
            streamer_type=args.streamer,
            file_path=args.input,
            logger=logger,
            timeout=None,
            trigger=args.training_mode,
            jenkins_url=args.jenkins_url,
            job_name=args.job_name,
            build_number=args.build_number,
            auth=(args.username, args.api_token),
        )
    return None


def get_main_logger(
    name: str = f"LogAnalyser-{uuid.uuid4().hex}",
    logfile: str = "LogAnalyser.log",
    level: int = logging.INFO,
) -> Logger:
    """Create and return a logger instance for the main function."""
    return Logger(name=name, logfile=logfile, level=level)


def main_fn() -> None:
    """
    Main function to run the log analysis.

    This function retrieves the command-line arguments, initializes the LogAnalyser
    and Streamer instances, and performs the log analysis. It also handles threading
    for the streamer and ensures proper cleanup after analysis.
    """
    args = get_args()
    # We force neural to always use the streamers if not specified defaults to file streamer
    if args.analysis_type == "neural" and not args.streamer:
        args.streamer = "file"
    logger = get_main_logger()
    logger.info(f":::::::::: {NAME} {VERSION} ::::::::::")
    streamer = get_streamer(args, logger)

    analyser = LogAnalyser(
        input=args.input,
        analysis_type=args.analysis_type,
        match_at=args.match_at,
        verbose=args.verbose,
        output=args.output,
        prompt_output=args.prompt_output,
        training_mode=args.training_mode,
        ground_truth_folder=args.ground_truth_folder,
        rag=args.rag,
        ini_config=args.ini_config or DEFAULT_CONFIG_FILE,
        streamer=streamer,
        logger=logger,
        collection=args.collection,
        vocabulary=args.vocabulary,
    )

    streamer_thread = None
    with TimeMonitor(
        f":::::::::: {NAME} {VERSION}, analysis type: {args.analysis_type}, operations completed in: ",
        analyser.logger,
    ):
        analyser.logger.debug(f"{get_hw_spec()}")
        if streamer:
            # We start the streamer on a parallel thread
            streamer_thread = threading.Thread(target=stream_logs, args=(streamer,))
            streamer_thread.start()
        try:
            run_analysis(analyser)
        except Exception as e:
            analyser.logger.critical(f"{NAME}: Can not connect to Qdrant instance! {e}")
            raise e
        finally:
            # Once completed safely close the streamer thread
            if streamer and streamer_thread is not None:
                streamer_thread.join()


if __name__ == "__main__":
    main_fn()
