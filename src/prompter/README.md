# Prompter (Prompt Generator)

*The primary goal of this tool is to simplify the process of generating analysis results related LLM prompts.*

Prompter is a library designed to generate LLM Prompts from log analysis results, which an LLM can use to generate descriptions and offer potential solutions for detected root
causes. It can be used as a standalone command-line utility or integrated into Python project as a library. It can also be used to generate RAG (Retrieval Augmented Generation)
prompts.

## Retrieval Augmented Generation flow

The RAG prompt creation process is facilitated by
the [prompter.py](prompter.py) class.

![Retrieval Augmented Generation flow](../../doc/PromptGeneratorRagFlow.svg)

The analyzer also allows standalone script-based operation. It can generate the prompt automatically post-analysis.
Nonetheless, running the prompt generation as a separate script can prove highly beneficial when working with prompt
engineering or enhancing the prompts. In such cases, running repeated analysis would be unnecessary; rather, you could
run prompt generation on a previously created analysis, which is a significant time-saver.

## Usage

The main script can be run with the following command:

```console
prompter.cmd <full_log_file> <result_analysis_file> <options...> 
```

The following usage example assumes `log-10002-regexps.json` is already available as an analysis result generated by the
Log Analyzer.

> **Warning**: MAKE SURE YOU ARE IN THE `<repo_dir>/src` DIRECTORY OF THE PROJECT BEFORE RUNNING THE COMMANDS

~~Output the RAG extended LLM prompt to a file named `./prompt_ex1.txt`:~~

~~Output the RAG extended LLM prompt to a file named `./prompt_ex2_rag.txt` (works for both Windows and Linux):~~

## Output a simple LLM prompt to the console:

```bash
.\run\windows\prompter.cmd ./samples/log-10002.txt ./samples/log-10002-regexps.json --verbose --vocabulary ./vocabularies/keywords_unified.json --output ./prompt.txt
```

```bash
sh ./run/linux/prompter.sh ./samples/log-10002.txt ./samples/log-10002-regexps.json --verbose --vocabulary ./vocabularies/keywords_unified.json --output ./prompt.txt
```


| Argument Name                                 | Description                                                                             |
| --------------------------------------------- | --------------------------------------------------------------------------------------- |
| `input`                                       | Log file, including path.                                                               |
| `analysis`                                    | Analysis result file, including path.                                                   |
| `-ground_truth_folder, --ground_truth_folder` | Optional but needed for RAG. The path to the folder containing the ground truth.        |
| `-output, --output`                           | Optional. Writes output to file.                                                        |
| `-verbose, --verbose`                         | Optional. Verbose mode will automatically trigger DEBUG logging into the log file also. |
| `-vocabulary, --vocabulary`                   | Optional. Unified vocabulary file for prompt generation (overrides default per-analyser vocabulary). |

### Update History


| Date       | Author               | Reference |
| ---------- | -------------------- | --------- |
| 2025-01-24 | Mihai-Ciprian Chezan | CHM1LUD   |

[//]: #
[//]: #
[//]: #
[//]: #
[//]: #
[//]: #
[//]: #
[//]: #
[//]: #
[//]: #
[//]: #
[//]: #
[//]: #
[//]: #
