import json
import os
import re
import traceback
import uuid
from pathlib import Path
from typing import Any, Optional, Tuple, Dict, List

import tiktoken  # OpenAI's official tokenizer; most accurate for GPT-3/4. For other LLMs, use their official tokenizers (see calculate_tokens docstring).

from utils.config_reader import ConfigReader
from utils.default_config import DEFAULT_CONFIG_FILE
from utils.files import Files
from utils.logger import Logger
from utils.logs import Logs
from utils.rag import RetrivalAugmentedGeneration
from utils.tokenizer import Tokenizer


class Prompter:
    """
    Generates prompts based on log file analysis, with optional RAG support.
    """

    NAME = "Prompter"
    PROMPT_TASK = """Analyze the given log file extracts (never mention or comment on "extracts") as if it were a 'full log file' and generate plain text without any tags, formatting, or lists.
Treat the log file extracts as plain text with line numbers as keys and log strings as values.
A line containing 'Finished:' cannot be a root cause.
DO NOT mention that the analysis is based on 'log extracts' or anything about 'extracts'.
When unsure, suggest a thorough system and code examination.
DO NOT comment on JSON structure or keys.
DO NOT mention "success_lines" or '"success_lines":{{}}' in the output.
'Root cause' means the most critical errors that directly cause the system to fail or stop functioning.

1. If "success_lines" is empty (e.g., [] or {{}}):
   - Combine all findings into one single paragraph containing four labeled sections, each section separated by two new lines:
       (name of the most important error(s), a string with a title-like length) 'Error Name: ...'
       (brief description of the errors and causes, maximum {max_description} characters) 'Description: ...'
       (proposed solution to fix the errors, maximum {max_solution} characters) 'Solution: ...'
       (ideally 1, maximum of 3, only numeric line numbers, listing unique line number(s) of the main root cause) 'Root Cause Line: ...'

2. If "success_lines" is not empty (e.g., {{...}}] or [{{...}}]] or [...]) and contains line numbers and text:
   - Assume the logs indicate an overall successful outcome.
   - Write one single paragraph stating that the logs are most likely successful.
   - Briefly comment on any errors or potential fixes as warnings.
        (details, descriptions, etc.) '...'
"""
    PROMPT_BACKUP = (
        "Do not ask for additional details, do not comment on any specific data or previous comments, just reply exactly with this text: "
        "The provided data is insufficient for me to generate an accurate analysis and solution, "
        "I suggest that a thorough examination of the logs is necessary for an accurate diagnosis and solution."
    )
    PROMPT_RAG_TASK = "Do not comment on the 'EXAMPLES' section. Use it only as an internal reference and guidance for a similar analysis."
    PROMPT = "INSTRUCTIONS:\n{task}\n" "LOG FILE EXTRACTS:\n{analysis}\n" "{append}"
    PROMPT_EXAMPLES = "EXAMPLES (provided as examples and context only, do not analyze or comment on them):\n{examples}"
    PROMPT_BASE_TEXT = "".join([PROMPT_TASK, PROMPT_RAG_TASK, PROMPT_EXAMPLES])

    TOKENS_MAX = 4090  # Maximum word tokens (user prompter + LLM response cumulated) allowed for an LLM session with GPT-3, GPT-4
    TOKENS_OVERFLOW_LIMIT = TOKENS_MAX * 1024 * 10  # Max tokens allowed in adjustment calculations ~40mil tokens. Limit is not necessary, but we make sure we prevent any issues
    TOKENS_WARN = 3500  # Current prompter LLM response = ~150 - ~200 tokens, to be safe we save ~600 tokens for the LLM response
    CURRENT_PATH = Path(__file__).resolve().parent
    TIKTOKEN_CACHE_FILE = CURRENT_PATH / Path("9b5ad71b2ce5302211f9c61530b329a4922fc6a4.gz")
    TIKTOKEN_CACHE_DIR = CURRENT_PATH / Path("tiktoken_cache")

    # Critical information, such as error codes, timestamps, and brief descriptions in log are in the first 256 characters.
    MAX_LOG_LINE_LENGTH = 256

    TOKEN_PATTERN = re.compile(r'\w+|[^\w\s]|"(?:\\.|[^"\\])*"|\'(?:\\.|[^\'\\])*\'|\{|}|\[|]|[:,]', re.UNICODE)
    SPECIAL_PATTERN = re.compile(r"\w+|[^\w\s]", re.UNICODE)
    LOG_LEVEL = Logger.DEBUG

    def __init__(
        self,
        max_solution_length: int = 1200,
        max_description_length: int = 1200,
        logger: Optional[Logger] = None,
        ini_config: str = str(DEFAULT_CONFIG_FILE),
        cfg: Any = None,
    ) -> None:
        """
        Initialize Prompter.

        Args:
            max_solution_length (int): Max solution length.
            max_description_length (int): Max description length.
            logger (Logger, optional): Logger instance.
            ini_config (str, optional): Path to config file.
            cfg (Any, optional): Config object.
        """
        self.logger = logger if logger else Logger(name=f"RAG-{uuid.uuid4().hex}", logfile="Rag.log", level=self.LOG_LEVEL)
        self.files = Files(self.logger)
        self.lg = Logs(self.logger)
        self.cfg = cfg if cfg else ConfigReader(ini_config, self.logger)
        self.rag = None
        self.max_solution_length = self.cfg.get("PROMPT_GENERATOR", "max_solution_length", max_solution_length)
        self.max_description_length = self.cfg.get("PROMPT_GENERATOR", "max_description_length", max_description_length)
        self.tiktoken_cache = self.init_tiktoken_cache()
        self.base_tokens = self.calculate_tokens(self.PROMPT_BASE_TEXT)
        self.tokenizer = Tokenizer()
        self.base_tokens_approximated = self.tokenizer.approximate(self.PROMPT_BASE_TEXT)
        self.available_tokens = self.TOKENS_WARN - self.base_tokens
        self.rag = RetrivalAugmentedGeneration(self.logger, ini_config=Path(ini_config), cfg=self.cfg)

    def init_tiktoken_cache(self) -> bool:
        """
        Initialize tiktoken local cache.

        Returns:
            bool: True if successful, False otherwise.
        """
        try:
            self.files.extract_gz_file(str(self.TIKTOKEN_CACHE_FILE), str(self.TIKTOKEN_CACHE_DIR))
            os.environ["TIKTOKEN_CACHE_DIR"] = str(self.TIKTOKEN_CACHE_DIR)
        except Exception as e:
            self.logger.error(f"{self.NAME}: Failed to initialize tiktoken cache: {e}, {traceback.format_exc()}")
            return False
        return True

    @staticmethod
    def compact_json(dct: Any) -> str:
        """
        Compact a dictionary into a JSON string.

        Args:
            dct (Any): Dictionary to compact.

        Returns:
            str: Compacted JSON string.
        """
        return json.dumps(dct, separators=(",", ":"), sort_keys=True, ensure_ascii=True)

    def create_root_causes(self, ground_truth_folder: str, collection_name: Optional[str] = None) -> None:
        """
        Create embeddings and upload to Qdrant.

        Args:
            ground_truth_folder (str): Path to ground truth folder.
            collection_name (str, optional): Qdrant collection name.
        """

        self.logger.debug(f"{self.NAME}: Creating embeddings and uploading to Qdrant ...")
        if self.rag is None:
            self.logger.error(f"{self.NAME}: RAG instance is not initialized.")
            raise RuntimeError("RAG instance is not initialized.")
        root_causes = self.rag.get_root_causes(ground_truth_folder) if hasattr(self.rag, "get_root_causes") else None
        if not root_causes:
            self.logger.error(f"{self.NAME}: No root causes found in '{ground_truth_folder}'.")
            raise FileNotFoundError(f"No root causes found in '{ground_truth_folder}'.")
        if collection_name is None:
            collection_name = "root_causes"
        if hasattr(self.rag, "qh") and self.rag.qh is not None and hasattr(self.rag.qh, "create_multi_vector"):
            self.rag.qh.create_multi_vector(collection_name, ["errors_match", "hints_match"])
        if hasattr(self.rag, "vectorize_multi"):
            embeddings = self.rag.vectorize_multi(["errors_match", "hints_match"], root_causes)
        else:
            embeddings = None
        if hasattr(self.rag, "qh") and self.rag.qh is not None and hasattr(self.rag.qh, "upload") and embeddings is not None:
            self.rag.qh.upload(collection_name, embeddings)

    def get_rag_prompt(
        self,
        analysis_with_lines: Dict,
        ground_truth_folder: Optional[str] = None,
        task: Optional[str] = None,
    ) -> Tuple[Optional[str], Optional[str]]:
        """
        Generate a RAG prompt.

        Args:
            analysis_with_lines (dict): Analysis with log lines.
            ground_truth_folder (str, optional): Path to ground truth folder.
            task (str, optional): Task description.

        Returns:
            tuple: (task, append) strings.
        """
        if ground_truth_folder and self.rag is not None:
            self.logger.debug("RAG -- Started")
            if hasattr(self.rag, "init_rag"):
                self.rag.init_rag()
            if task is None:
                task = ""
            task = str(task) + str(self.PROMPT_RAG_TASK)
            self.create_root_causes(ground_truth_folder, "root_causes")
            matches = self.rag.get_matches(analysis_with_lines, "root_causes") if hasattr(self.rag, "get_matches") else None
            context_objects = self.rag.get_payload(matches) if hasattr(self.rag, "get_payload") else None
            append = self.PROMPT_EXAMPLES.format(examples=self.compact_json(context_objects))
            self.logger.debug("RAG -- Context Generation Completed")
            return task, append
        else:
            self.logger.warning(f"{self.NAME}: RAG requires 'ground_truth_folder' to generate context.")
        return None, None

    def calculate_tokens(self, text: str, encoding: str = "cl100k_base") -> int:
        """
        Calculate number of tokens in text using OpenAI's tiktoken (most accurate for GPT-3/4 models).
        For other LLMs (e.g., Llama, Mistral, Claude), use their official tokenizer libraries for best accuracy.
        If tiktoken is unavailable, falls back to a fast approximation.
        No better method exists for OpenAI models as of June 2025.

        Args:
            text (str): Text to tokenize.
            encoding (str, optional): Encoding to use.

        Returns:
            int: Number of tokens.
        """
        try:
            tokenizer = tiktoken.get_encoding(encoding)
            tokens_count = len(tokenizer.encode(text))
        except Exception as e:
            self.logger.error(f"{self.NAME}: Tiktoken has caused an exception {e}, {traceback.format_exc()}")
            self.logger.warning(f"{self.NAME}: Using approximation method to count tokens...")
            tokens_count = self.tokenizer.approximate(text)
        return tokens_count

    @staticmethod
    def compact_prompt_object(prompt_object: Dict) -> Dict:
        """
        Remove one line at a time from dicts, starting with hints then errors.

        Args:
            prompt_object (dict): Prompter object to compact.

        Returns:
            dict: Compacted prompter object.
        """
        for rc in prompt_object.get("root_causes", []):
            hint_lines = rc.get("hint_lines", {})
            error_lines = rc.get("error_lines", {})
            if hint_lines:
                hint_lines.pop(next(iter(hint_lines)))
            elif error_lines:
                error_lines.pop(next(iter(error_lines)))
        return prompt_object

    @staticmethod
    def clean_log_line(line: str) -> str:
        """
        Clean a log line by stripping leading/trailing whitespace and collapsing multiple spaces to one.

        Args:
            line (str): The log line to clean.

        Returns:
            str: Cleaned log line.
        """
        if not isinstance(line, str):
            return line
        # Strip and collapse whitespace
        return re.sub(r"\s+", " ", line.strip())

    @staticmethod
    def clean_log_lines_in_object(obj: Dict) -> Dict:
        """
        Recursively clean all log lines in the prompt object (error_lines, hint_lines, success_lines).
        Preserves mapping: line_number -> cleaned log line.

        Args:
            obj (dict): The prompt object.

        Returns:
            dict: Cleaned prompt object.
        """
        for rc in obj.get("root_causes", []):
            for key in ["error_lines", "hint_lines", "success_lines"]:
                lines = rc.get(key, {})
                if isinstance(lines, dict):
                    for k in list(lines.keys()):
                        lines[k] = Prompter.clean_log_line(lines[k])
                elif isinstance(lines, list):
                    # Convert list to dict with dummy line numbers if needed (shouldn't happen in new logic)
                    rc[key] = {str(i): Prompter.clean_log_line(val) for i, val in enumerate(lines)}
        return obj

    def generate_prompt(self, prompt_object: Dict, task: str = "", append: str = "") -> Tuple[str, int]:
        """
        Generate a prompt and return it with token count.
        Ensures PROMPT_TASK is always at the beginning and log lines are cleaned.

        Args:
            prompt_object (dict): Prompter object.
            task (str, optional): Task description.
            append (str, optional): Append string.

        Returns:
            tuple: (prompt, token_count)
        """
        # Clean log lines before generating prompt
        prompt_object = self.clean_log_lines_in_object(prompt_object)
        # Always ensure PROMPT_TASK is at the beginning
        if not task:
            task = self.PROMPT_TASK.format(
                max_description=self.max_description_length,
                max_solution=self.max_solution_length,
            )
        prompt = self.PROMPT.format(task=task, analysis=self.compact_json(prompt_object), append=append)
        token_count = self.calculate_tokens(prompt)
        token_approximated = self.tokenizer.approximate(prompt)
        self.logger.debug(f"{self.NAME}: Generated prompter token count: {token_count} (approximated: {token_approximated})")
        return prompt, token_count

    def limit_root_causes2(self, root_causes: List[Dict], max_iterations: int) -> List[Dict]:
        """
        Limit total items in root_causes to max_iterations.

        Args:
            root_causes (list): List of root causes.
            max_iterations (int): Max items allowed.

        Returns:
            list: Limited root causes.
        """
        total_items = sum(len(rc.get("error_lines", [])) + len(rc.get("hint_lines", [])) for rc in root_causes)
        self.logger.debug(f"Reducing {total_items} root causes to max {max_iterations} ...")

        while total_items > max_iterations:
            for root_cause in root_causes:
                if isinstance(root_cause.get("hint_lines"), dict) and root_cause["hint_lines"]:
                    root_cause["hint_lines"].popitem()
                    total_items -= 1
                elif isinstance(root_cause.get("hint_lines"), list) and root_cause["hint_lines"]:
                    root_cause["hint_lines"].pop()
                    total_items -= 1
                elif isinstance(root_cause.get("error_lines"), dict) and root_cause["error_lines"]:
                    root_cause["error_lines"].popitem()
                    total_items -= 1
                elif isinstance(root_cause.get("error_lines"), list) and root_cause["error_lines"]:
                    root_cause["error_lines"].pop()
                    total_items -= 1
                if total_items <= max_iterations:
                    break

        return root_causes

    def limit_root_causes(self, root_causes: List[Dict], max_iterations: int) -> List[Dict]:
        """
        Limit total items in root_causes to max_iterations.

        Args:
            root_causes (list): List of root causes.
            max_iterations (int): Max items allowed.

        Returns:
            list: Limited root causes.
        """
        total_items = sum(len(rc.get("error_lines", [])) + len(rc.get("hint_lines", [])) for rc in root_causes)
        self.logger.debug(f"Reducing {total_items} root causes to max {max_iterations} ...")
        while total_items > max_iterations:
            for root_cause in root_causes:
                for key in ["hint_lines", "error_lines"]:
                    lines = root_cause.get(key)
                    if isinstance(lines, dict) and lines:
                        lines.popitem()
                        total_items -= 1
                    elif isinstance(lines, list) and lines:
                        lines.pop()
                        total_items -= 1
                    if total_items <= max_iterations:
                        break
        return root_causes

    def constrain_prompt(
        self,
        token_count: int,
        prompt_object: Dict,
        task: str,
        append: str = "",
        max_iterations: int = 200,
    ) -> str:
        """
        Compact the prompt to fit within token limit.

        Args:
            token_count (int): Current token count.
            prompt_object (dict): Prompter object.
            task (str): Task description.
            append (str, optional): Append string.
            max_iterations (int, optional): Max iterations to reduce prompter.

        Returns:
            str: Compacted prompt.
        """
        iteration_count = 0
        if token_count > self.TOKENS_WARN:
            prompt_object["root_causes"] = self.limit_root_causes(prompt_object["root_causes"], max_iterations)
            self.logger.warning(f"{self.NAME}: Token count {token_count} exceeds the limit of {self.TOKENS_WARN}, compacting and regenerating ...")
        while token_count > self.TOKENS_WARN and iteration_count < max_iterations:
            iteration_count += 1
            prompt_object = self.compact_prompt_object(prompt_object)
            prompt, token_count = self.generate_prompt(prompt_object, task, append)
            if self.lg.is_dict_fully_empty(prompt_object["root_causes"]):
                self.logger.warning(f"{self.NAME}: Prompt generation failed. No more lines to remove, resorting to backup prompter...")
                return self.PROMPT_BACKUP
            if token_count < self.TOKENS_WARN:
                self.logger.debug(f"Generation success, token count: {token_count} is within the limit of {self.TOKENS_WARN}.")
                return prompt
        self.logger.warning(f"{self.NAME}: Prompt generation failed, maximum iteration limit reached. Resorting to backup prompter...")
        return self.PROMPT_BACKUP

    def get_vocabularies(self) -> Dict[str, List[str]]:
        """
        Load all keyword vocabularies used for intelligent token trimming.
        Returns a dictionary with keys 'errors', 'hints', and 'success', each containing
        a list of patterns in order of severity/importance.

        Returns:
            dict: Dictionary of vocabulary lists
        """
        try:
            # Try to get vocabularies from the vocabulary file in a similar way to Keywords class
            vocab_path = Path(self.cfg.get("ANALYSIS", "vocabulary_path", str(Path(__file__).parent.parent / "vocabularies")))
            vocab_file = self.cfg.get("ANALYSIS", "vocabulary_file", "keywords_unified.json")
            vocab_filepath = vocab_path / vocab_file

            if not vocab_filepath.exists():
                self.logger.warning(f"{self.NAME}: Vocabulary file {vocab_filepath} not found, using empty vocabularies")
                return {"errors": [], "hints": [], "success": []}

            vocab = self.files.get_json(str(vocab_filepath))
            return {"errors": vocab.get("errors", []), "hints": vocab.get("hints", []), "success": vocab.get("successes", [])}
        except Exception as e:
            self.logger.error(f"{self.NAME}: Failed to load vocabularies: {e}, {traceback.format_exc()}")
            return {"errors": [], "hints": [], "success": []}

    # TODO: add a line processor for pattern matching similar to rag for e-bike.

    def generate(
        self,
        analysis_with_lines: Dict,
        ground_truth_folder: Optional[str] = None,
        rag: bool = False,
        normalize_len: bool = True,
        use_advanced_trimming: bool = True,  # Enable the new trimming methods by default
    ) -> str:
        """
        Generate the final prompt.

        Args:
            analysis_with_lines (dict): Analysis with log lines.
            ground_truth_folder (str, optional): Path to ground truth folder.
            rag (bool, optional): Use RAG if True.
            normalize_len (bool, optional): Normalize line lengths if True.
            use_advanced_trimming (bool, optional): Use advanced trimming methods if True.

        Returns:
            str: Generated prompt.
        """
        tokens_available = self.TOKENS_WARN - self.base_tokens
        self.logger.debug(
            f"{self.NAME}: Generating LLM prompter (RAG:{rag}), base prompter tokens, calculated: {self.base_tokens} "
            f"(approximated: {self.base_tokens_approximated}), tokens remaining {tokens_available} ..."
        )
        task = self.PROMPT_TASK.format(
            max_description=self.max_description_length,
            max_solution=self.max_solution_length,
        )
        if normalize_len:
            analysis_with_lines = self.lg.normalize_line_length(analysis_with_lines)
        prompt_object = self.lg.clean_dict_fields(["errors_match", "hints_match"], analysis_with_lines)
        append = ""
        if rag:
            task_rag, append_rag = self.get_rag_prompt(analysis_with_lines, ground_truth_folder, task)
            if task_rag is not None:
                task = task_rag
            if append_rag is not None:
                append = append_rag

        # Get vocabularies for advanced trimming
        vocabularies = {}
        if use_advanced_trimming:
            # Use our local get_vocabularies method
            vocabularies = self.get_vocabularies()

        prompt, token_count = self.generate_prompt(prompt_object, task, append)

        if token_count > self.TOKENS_WARN:
            if use_advanced_trimming and vocabularies:
                self.logger.debug(f"{self.NAME}: Using advanced token trimming methods...")
                # Use construct_prompt_within_limit for a fresh build approach
                prompt_object = self.construct_prompt_within_limit(prompt_object, vocabularies, self.TOKENS_WARN, self.MAX_LOG_LINE_LENGTH)
                prompt, token_count = self.generate_prompt(prompt_object, task, append)

                # If still too large, fall back to balanced trimming
                if token_count > self.TOKENS_WARN:
                    prompt_object = self.balanced_token_trim(prompt_object, vocabularies, self.TOKENS_WARN, self.MAX_LOG_LINE_LENGTH)
                    prompt, token_count = self.generate_prompt(prompt_object, task, append)
            else:
                # Fall back to the original method if advanced trimming is disabled
                prompt = self.constrain_prompt(token_count, prompt_object, task, append)

        return prompt

    def balanced_token_trim(
        self,
        prompt_object: Dict,
        vocabularies: Dict[str, List[str]],
        max_tokens: int = None,
        max_line_length: int = 256,
    ) -> Dict:
        """
        Reduce prompt_object to fit within max_tokens, using severity order from vocabularies.
        Always trims lines, never removes the last success line if present.

        Args:
            prompt_object (dict): The prompt object with root_causes.
            vocabularies (dict): Dict with keys 'errors', 'hints', 'success', etc., each a list in severity order.
            max_tokens (int): Token limit (default: self.TOKENS_WARN).
            max_line_length (int): Max chars per log line.

        Returns:
            dict: Reduced prompt_object.
        """
        if max_tokens is None:
            max_tokens = self.TOKENS_WARN

        # Create a copy of the prompt_object to ensure we preserve metadata
        result_object = prompt_object.copy()
        # Get just the root_causes for trimming
        root_causes = prompt_object.get("root_causes", [])

        # 1. Trim all log lines
        for rc in root_causes:
            for key in ["error_lines", "hint_lines", "success_lines"]:
                lines = rc.get(key, {})
                if isinstance(lines, dict):
                    for k in lines:
                        if isinstance(lines[k], str):
                            lines[k] = lines[k][:max_line_length]
                elif isinstance(lines, list):
                    for idx, val in enumerate(lines):
                        if isinstance(val, str):
                            lines[idx] = val[:max_line_length]

        # 2. Never remove the last success line if present
        def has_success(rc):
            lines = rc.get("success_lines", {})
            return bool(lines)

        # 3. Remove hints from the bottom, then errors by severity, then extra successes
        def remove_hint(rc):
            lines = rc.get("hint_lines", {})
            if isinstance(lines, dict) and lines:
                lines.pop(next(reversed(lines)))
                return True
            elif isinstance(lines, list) and lines:
                lines.pop()
                return True
            return False

        def remove_error(rc, error_order):
            lines = rc.get("error_lines", {})
            if isinstance(lines, dict) and lines:
                # Remove the least important error (last in vocab order)
                for err in reversed(error_order):
                    for k in list(lines.keys()):
                        if err in lines[k]:
                            del lines[k]
                            return True
                # fallback: just pop last
                lines.pop(next(reversed(lines)))
                return True
            elif isinstance(lines, list) and lines:
                # Remove by vocab order
                for err in reversed(error_order):
                    for idx, val in enumerate(lines):
                        if err in val:
                            lines.pop(idx)
                            return True
                lines.pop()
                return True
            return False

        def remove_extra_success(rc):
            lines = rc.get("success_lines", {})
            if isinstance(lines, dict) and len(lines) > 1:
                # Remove all but last
                keys = list(lines.keys())
                for k in keys[:-1]:
                    del lines[k]
                    return True
            elif isinstance(lines, list) and len(lines) > 1:
                del lines[0]
                return True
            return False

        # 4. Main reduction loop
        temp_object = prompt_object.copy()
        prompt, token_count = self.generate_prompt(temp_object)
        iteration_count = 0
        error_order = vocabularies.get("errors", [])
        while token_count > max_tokens and iteration_count < 200:
            iteration_count += 1
            changed = False
            for rc in root_causes:
                # Remove hints first
                if remove_hint(rc):
                    changed = True
                    break
            if not changed:
                for rc in root_causes:
                    # Remove errors by severity
                    if remove_error(rc, error_order):
                        changed = True
                        break
            if not changed:
                for rc in root_causes:
                    # Remove extra success lines, but never the last
                    if remove_extra_success(rc):
                        changed = True
                        break

            # Update temp object with modified root_causes for token counting
            temp_object = prompt_object.copy()
            temp_object["root_causes"] = root_causes
            prompt, token_count = self.generate_prompt(temp_object)

            # If nothing left to remove, break
            if not changed:
                break

        # Update the result with trimmed root_causes
        result_object["root_causes"] = root_causes
        return result_object

    def construct_prompt_within_limit(
        self, prompt_object: Dict, vocabularies: Dict[str, List[str]], max_tokens: int = None, max_line_length: int = 256, skip_duplicates: bool = True
    ) -> Dict:
        """
        Efficiently construct a prompt object within the token limit, prioritizing most important lines.
        Always includes at least the last success line if present. Skips duplicates by default.
        Preserves line numbers as keys.

        Args:
            prompt_object (dict): The prompt object with root_causes.
            vocabularies (dict): Dict with keys 'errors', 'hints', 'successes', etc., each a list in severity order.
            max_tokens (int): Token limit (default: self.TOKENS_WARN).
            max_line_length (int): Max chars per log line.
            skip_duplicates (bool): If True, skip duplicate log lines.

        Returns:
            dict: New prompt_object containing only the selected lines (with line numbers as keys).
        """
        if max_tokens is None:
            max_tokens = self.TOKENS_WARN

        # Create a copy of the original prompt_object to preserve metadata fields
        new_prompt_object = {k: v for k, v in prompt_object.items() if k != "root_causes"}
        new_root_causes = []

        for rc in prompt_object.get("root_causes", []):
            new_rc = {"error_lines": {}, "hint_lines": {}, "success_lines": {}}
            seen_lines = set() if skip_duplicates else set()
            # 1. Always include the last success line if present
            success_lines = rc.get("success_lines", {})
            if isinstance(success_lines, list):
                # Convert to dict with dummy line numbers if needed
                success_lines = {str(i): val for i, val in enumerate(success_lines)}
            if success_lines:
                last_key = list(success_lines.keys())[-1]
                last_success = success_lines[last_key][:max_line_length]
                new_rc["success_lines"][last_key] = last_success
                if skip_duplicates:
                    seen_lines.add(last_success)
            # 2. Add errors in order of importance (from vocabularies)
            error_lines = rc.get("error_lines", {})
            if isinstance(error_lines, list):
                error_lines = {str(i): val for i, val in enumerate(error_lines)}
            error_priority = vocabularies.get("errors", [])
            error_priority_map = {kw: i for i, kw in enumerate(error_priority)}

            def error_score(line):
                for kw in error_priority:
                    if kw in line:
                        return error_priority_map[kw]
                return len(error_priority)

            # Sort by priority
            sorted_errors = sorted(error_lines.items(), key=lambda kv: error_score(kv[1]))
            for k, line in sorted_errors:
                trimmed = line[:max_line_length]
                if skip_duplicates and trimmed in seen_lines:
                    continue
                new_rc["error_lines"][k] = trimmed
                if skip_duplicates:
                    seen_lines.add(trimmed)
                temp_prompt_object = {**new_prompt_object, "root_causes": [new_rc]}
                prompt, token_count = self.generate_prompt(temp_prompt_object)
                if token_count > max_tokens:
                    del new_rc["error_lines"][k]
                    break
            # 3. Add hints (bottom up)
            hint_lines = rc.get("hint_lines", {})
            if isinstance(hint_lines, list):
                hint_lines = {str(i): val for i, val in enumerate(hint_lines)}
            for k in reversed(list(hint_lines.keys())):
                line = hint_lines[k]
                trimmed = line[:max_line_length]
                if skip_duplicates and trimmed in seen_lines:
                    continue
                # Insert at beginning to maintain order
                new_rc["hint_lines"] = {k: trimmed, **new_rc["hint_lines"]}
                if skip_duplicates:
                    seen_lines.add(trimmed)
                temp_prompt_object = {**new_prompt_object, "root_causes": [new_rc]}
                prompt, token_count = self.generate_prompt(temp_prompt_object)
                if token_count > max_tokens:
                    del new_rc["hint_lines"][k]
                    break
            # 4. Optionally add more success lines (except last)
            if success_lines and len(success_lines) > 1:
                for k in list(success_lines.keys())[:-1]:
                    line = success_lines[k]
                    trimmed = line[:max_line_length]
                    if skip_duplicates and trimmed in seen_lines:
                        continue
                    new_rc["success_lines"][k] = trimmed
                    if skip_duplicates:
                        seen_lines.add(trimmed)
                    temp_prompt_object = {**new_prompt_object, "root_causes": [new_rc]}
                    prompt, token_count = self.generate_prompt(temp_prompt_object)
                    if token_count > max_tokens:
                        del new_rc["success_lines"][k]
                        break

            # Copy any other fields from original root cause
            for k, v in rc.items():
                if k not in ["error_lines", "hint_lines", "success_lines"]:
                    new_rc[k] = v

            new_root_causes.append(new_rc)

        # Return new prompt object with all metadata fields preserved
        new_prompt_object["root_causes"] = new_root_causes
        return new_prompt_object
