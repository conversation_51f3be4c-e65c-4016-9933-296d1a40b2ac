import argparse
import json
import traceback

from prompter.prompter import Prompter
from prompter.version import <PERSON><PERSON><PERSON><PERSON>
from utils.default_config import DEFAULT_CONFIG_FILE
from utils.logger import Logger


def get_args():
    """
    Returns the command line arguments.

    This function sets up an argument parser with various options for the log analysis tool.
    It includes arguments for input and output files, ground truth folder, verbosity, RAG prompter activation,
    and configuration file.

    Returns:
        argparse.Namespace: Parsed command line arguments.
    """
    parser = argparse.ArgumentParser(
        description=f".:. Prompt Generator with Retrival Augmented Generation {VERSION} -- Assembles log analysis context for an LLM", formatter_class=argparse.RawTextHelpFormatter
    )
    parser.add_argument("input", help="Log file including path")
    parser.add_argument("analysis", help="Analysis result file including path")
    parser.add_argument("-ground_truth_folder", "--ground_truth_folder", help="The path to the folder containing the ground truth")
    parser.add_argument("-output", "--output", help="JSON file containing the prompter and the top matches as context")
    parser.add_argument("-verbose", "--verbose", help="Optional, verbose mode", default=False, action="store_true")
    parser.add_argument("-rag", "--rag", help="Optional, activate RAG prompter, it needs the -g, --ground_truth_folder to be specified", default=False, action="store_true")
    parser.add_argument(
        "-ini_config",
        "--ini_config",
        help="Optional, specify a file (with path) for the configuration, e.g. /configs/custom_config1/config.ini. Default (if not specified) is the 'config.ini' in the of the log-analyzer (parent of analyser) folder.",
        default=DEFAULT_CONFIG_FILE,
    )
    parser.add_argument("-vocab", "--vocabulary", help="Path to the unified vocabulary JSON file", required=True)
    return parser.parse_args()


def main_fn():
    # Parse command line arguments
    args = get_args()

    # Initialize the prompter generator with specified parameters
    pg = Prompter(max_solution_length=1200, max_description_length=1200, logger=None, ini_config=args.ini_config or DEFAULT_CONFIG_FILE)
    try:
        if args.verbose:
            pg.logger.logger.setLevel(Logger.DEBUG)
        pg.logger.debug(f"[{pg.NAME}] --- Prompt generation started...")
        analysis = pg.lg.get_analysis(args.input, args.analysis)
        with open(args.vocabulary, "r", encoding="utf-8") as f:
            vocabularies = json.load(f)
        # Clean and trim prompt object
        prompt_object = pg.lg.clean_dict_fields(["errors_match", "hints_match"], analysis)
        trimmed_prompt_object = pg.construct_prompt_within_limit(prompt_object, vocabularies)
        prompt, _ = pg.generate_prompt(trimmed_prompt_object)
        if not args.output:
            print(f"{prompt}")
        else:
            pg.files.write_file(prompt, args.output)
        pg.logger.debug(f"[{pg.NAME}] --- Prompt generation completed.")
    except Exception as e:
        pg.logger.error(f"{e}, {traceback.format_exc()}")
        raise e


if __name__ == "__main__":
    main_fn()
