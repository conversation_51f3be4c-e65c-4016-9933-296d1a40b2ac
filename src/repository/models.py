import os
import shutil
from sqlalchemy import <PERSON>umn, Integer, String, JSO<PERSON>, DateT<PERSON>, <PERSON>ole<PERSON>, create_engine, event
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from pathlib import Path
from datetime import datetime
import sqlcipher3
from datetime import timed<PERSON><PERSON>
from config.constants import JOB_EXPIRATION_DAYS

# Using an encryption key, beware that losing the key will make the database unrecoverable
KNUT_DB_KEY = os.environ.get("KNUT_DBREPO_ENCRYPTION_KEY", "N-fkjvv-MHnSVtaIropSBSDYR8Noc8-u_PvJDpV4BPv71hTczTqoXgnFX1YRD4qNtFUKljdYBjHx5naLqO9HRA")

Base = declarative_base()

class User(Base):
    """SQLAlchemy model for users"""
    __tablename__ = 'users'

    id = Column(Integer, primary_key=True)
    username = Column(String, unique=True, nullable=False)
    # Password is stored as SHA-256 hash
    password = Column(String, nullable=False)
    role = Column(String, nullable=False)
    # Store groups as JSON to maintain flexibility
    groups = Column(JSON, nullable=False, default=[])

    def to_dict(self):
        """Convert model to dictionary format, similar to TinyDB format"""
        return {
            "id": self.id,
            "username": self.username,
            "password": self.password,
            "role": self.role,
            "groups": self.groups
        }

    @classmethod
    def from_dict(cls, data):
        """Create model instance from dictionary"""
        return cls(
            id=data.get("id"),
            username=data.get("username"),
            password=data.get("password"),
            role=data.get("role"),
            groups=data.get("groups", [])
        )


class Project(Base):
    """SQLAlchemy model for projects"""
    __tablename__ = 'projects'

    id = Column(Integer, primary_key=True)
    name = Column(String, nullable=False)
    description = Column(String)
    # Store groups as JSON to maintain flexibility
    groups = Column(JSON, nullable=False, default=[])
    cost_center = Column(String)

    def to_dict(self):
        """Convert model to dictionary format, similar to TinyDB format"""
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "groups": self.groups,
            "cost_center": self.cost_center
        }

    @classmethod
    def from_dict(cls, data):
        """Create model instance from dictionary"""
        return cls(
            id=data.get("id"),
            name=data.get("name"),
            description=data.get("description"),
            groups=data.get("groups", []),
            cost_center=data.get("cost_center")
        )


class Job(Base):
    """SQLAlchemy model for jobs (analysis and training tasks)"""
    __tablename__ = 'jobs'

    id = Column(String, primary_key=True)  # UUID
    user_id = Column(Integer, nullable=False)  # Keep for DB foreign keys
    username = Column(String, nullable=False)  # NTID for file operations and logging
    project_id = Column(Integer, nullable=False)

    # Job metadata
    job_type = Column(String, nullable=False)  # 'analyze' or 'train'
    method = Column(String)  # 'neural', 'keywords', 'regex'
    status = Column(String, nullable=False, default='created')  # 'created', 'uploading', 'processing', 'completed', 'failed'

    # Job details
    name = Column(String)  # User-friendly job name
    project_name = Column(String)  # Project name snapshot
    knowledge_collection = Column(String)  # Collection name
    log_files = Column(JSON)  # List of log file names

    # Configuration
    config = Column(JSON)  # Job-specific configuration (keywords, regexes, etc.)

    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    started_at = Column(DateTime)
    completed_at = Column(DateTime)
    upload_started_at = Column(DateTime)
    upload_completed_at = Column(DateTime)
    processing_started_at = Column(DateTime)
    expire_at = Column(DateTime)

    # Progress and results
    progress = Column(Integer, default=0)  # 0-100 (overall progress)
    upload_progress = Column(Integer, default=0)  # 0-100 (upload progress)
    processing_progress = Column(Integer, default=0)  # 0-100 (processing progress)
    message = Column(String, default='Job created')  # Current status message
    has_results = Column(Boolean, default=False)
    result_html = Column(String)  # HTML visualization filename
    result_summary = Column(String)  # Analysis summary
    error_message = Column(String)

    def to_dict(self):
        """Convert model to dictionary format matching mock server structure"""
        # Format expiration date properly - use real expiration or calculate from creation
        if self.expire_at:
            expire_date = self.expire_at.isoformat() + "Z"
        elif self.created_at:
            # Calculate expiration as 30 days from creation

            calculated_expiry = self.created_at + timedelta(days=JOB_EXPIRATION_DAYS)
            expire_date = calculated_expiry.isoformat() + "Z"
        else:
            expire_date = "2025-12-31T23:59:59Z"  # Fallback

        # Format log files properly
        log_file_display = ", ".join(self.log_files) if self.log_files else ""

        # Determine result status
        result_status = "Available" if self.has_results and self.status == "completed" else "N/A"

        return {
            "id": self.id,
            "jobId": self.id,  # Primary ID field - frontend expects jobId
            "sessionId": self.id,  # Keep for backward compatibility
            "userId": self.user_id,  # Frontend compatibility
            "user_id": self.user_id,  # Backend compatibility
            "username": self.username,  # NTID for identification
            "project": self.project_name or "Unknown Project",
            "jobType": self.job_type,  # Frontend expects jobType
            "sessionType": self.job_type,  # Keep for backward compatibility
            "method": self.method.title() if self.method else "Neural",
            "knowledgeCollection": self.knowledge_collection or "",
            "logFile": log_file_display,
            "status": self.status,
            "resultHTML": self.result_html or "",
            "resultSummary": self.result_summary or "",  # Actual summary text
            "expire": expire_date,
            "message": self.message or "",
            "progress": self.progress or 0,
            "upload_progress": self.upload_progress or 0,
            "processing_progress": self.processing_progress or 0
        }

    @classmethod
    def from_dict(cls, data):
        """Create model instance from dictionary"""
        return cls(
            id=data.get("id"),
            user_id=data.get("user_id"),
            username=data.get("username"),
            project_id=data.get("project_id"),
            job_type=data.get("job_type"),
            method=data.get("method"),
            status=data.get("status", "created"),
            name=data.get("name"),
            project_name=data.get("project_name"),
            knowledge_collection=data.get("knowledge_collection"),
            log_files=data.get("log_files", []),
            config=data.get("config", {}),
            progress=data.get("progress", 0),
            message=data.get("message", "Job created"),
            has_results=data.get("has_results", False),
            result_html=data.get("result_html"),
            result_summary=data.get("result_summary"),
            error_message=data.get("error_message")
        )


class UserVocabulary(Base):
    """User-specific vocabulary modifications (only stores deltas from defaults)"""
    __tablename__ = 'user_vocabularies'

    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, nullable=False)  # Foreign key to user
    username = Column(String, nullable=False)  # NTID for easy identification
    vocab_type = Column(String, nullable=False)  # 'keywords' or 'regex'
    variant = Column(String, nullable=False)    # 'error', 'false_positive', 'hints', 'success'
    added_items = Column(JSON, nullable=True)   # Items user added to default vocabulary
    removed_items = Column(JSON, nullable=True) # Items user removed from default vocabulary
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def to_dict(self):
        """Convert model to dictionary"""
        return {
            "id": self.id,
            "user_id": self.user_id,
            "username": self.username,
            "vocab_type": self.vocab_type,
            "variant": self.variant,
            "added_items": self.added_items or [],
            "removed_items": self.removed_items or [],
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }

    @classmethod
    def from_dict(cls, data):
        """Create model instance from dictionary"""
        return cls(
            user_id=data.get("user_id"),
            username=data.get("username"),
            vocab_type=data.get("vocab_type"),
            variant=data.get("variant"),
            added_items=data.get("added_items", []),
            removed_items=data.get("removed_items", [])
        )


class Database:
    """Database connection manager with encryption support"""

    def __init__(self, db_folder: str = None, use_encryption: bool = True):
        if db_folder is None:
            db_folder = Path(__file__).parent.parent.parent / "sqldb_data"

        # Ensure the database directory exists
        db_path = Path(db_folder)
        db_path.mkdir(parents=True, exist_ok=True)

        # Full path to database file
        self.db_file = db_path / "database.db"

        # Handle existing database - if it exists but causes errors, back it up
        if self.db_file.exists():
            backup_file = self.db_file.with_suffix('.db.bak')
            try:
                # Check if we're switching encryption modes
                if use_encryption:
                    # Try to access with SQLCipher
                    conn = sqlcipher3.dbapi2.connect(str(self.db_file))
                    conn.execute(f"PRAGMA key='{KNUT_DB_KEY}'")
                    # Test if it works
                    conn.execute("SELECT 1")
                    conn.close()
                else:
                    # Try to access with SQLite
                    import sqlite3
                    conn = sqlite3.connect(str(self.db_file))
                    conn.execute("SELECT 1")
                    conn.close()
            except Exception as e:
                # If access fails, back up the file
                if self.db_file.exists():
                    shutil.move(str(self.db_file), str(backup_file))
                    print(f"Backed up incompatible database to {backup_file}")

        self.use_encryption = use_encryption

        # For encrypted database, we need to create it first with SQLCipher
        if use_encryption and not self.db_file.exists():
            self._initialize_encrypted_db()

        # Create the SQLAlchemy engine
        self._setup_engine()

        # Create tables if they don't exist
        Base.metadata.create_all(self.engine)

        # Create session factory
        self.Session = sessionmaker(bind=self.engine)

    def _initialize_encrypted_db(self):
        """Initialize an encrypted database file with SQLCipher"""
        # Create a new encrypted database directly with SQLCipher
        conn = sqlcipher3.dbapi2.connect(str(self.db_file))
        cursor = conn.cursor()

        # Set encryption key first
        cursor.execute(f"PRAGMA key='{KNUT_DB_KEY}'")

        # Then set other PRAGMA settings
        cursor.execute("PRAGMA journal_mode=WAL")
        cursor.execute("PRAGMA synchronous=NORMAL")
        cursor.execute("PRAGMA foreign_keys=ON")

        # Commit and close
        conn.commit()
        conn.close()
        print(f"Initialized new encrypted database at {self.db_file}")

    def _setup_engine(self):
        """Setup the SQLAlchemy engine based on encryption settings"""
        if self.use_encryption:
            # Use SQLCipher for encrypted database
            self.db_url = f"sqlite:///{self.db_file}"
            self.engine = create_engine(
                self.db_url,
                module=sqlcipher3.dbapi2,
                connect_args={"check_same_thread": False}
            )

            # Define the event listeners for encryption
            @event.listens_for(self.engine, "connect", insert=True)
            def set_sqlite_pragma(dbapi_connection, connection_record):
                """Set SQLCipher key and pragmas for each connection"""
                cursor = dbapi_connection.cursor()
                # Set the encryption key FIRST, before any other operations
                cursor.execute(f"PRAGMA key='{KNUT_DB_KEY}'")
                # Then set the other pragmas
                cursor.execute("PRAGMA journal_mode=WAL")
                cursor.execute("PRAGMA synchronous=NORMAL")
                cursor.execute("PRAGMA foreign_keys=ON")
                cursor.close()
        else:
            # Regular SQLite for non-encrypted database
            self.db_url = f"sqlite:///{self.db_file}"
            self.engine = create_engine(
                self.db_url,
                connect_args={"check_same_thread": False}
            )

            # Set up SQLite pragmas for better concurrency
            @event.listens_for(self.engine, "connect")
            def set_sqlite_pragma(dbapi_connection, connection_record):
                cursor = dbapi_connection.cursor()
                cursor.execute("PRAGMA journal_mode=WAL")
                cursor.execute("PRAGMA synchronous=NORMAL")
                cursor.execute("PRAGMA foreign_keys=ON")
                cursor.close()

    def get_session(self):
        """Get a new database session"""
        return self.Session()
