import os
import secrets
from pathlib import Path
from typing import List, Dict, Optional, Any
from datetime import datetime
from sqlalchemy.exc import SQLAlchemyError
from contextlib import contextmanager

from utils.password_hasher import hash_password
from .models import Database, User, Project, Job, UserVocabulary
from utils.logger import Logger


class Repository:
    """Repository for users and projects using SQLAlchemy and SQLite"""

    def __init__(self, db_folder: str = None, use_encryption: bool = True):
        self.logger = Logger(name="Repository", logfile="Repository.log", level=Logger.INFO)

        # Create database connection
        if db_folder is None:
            db_folder = Path(__file__).parent.parent.parent / Path("sqldb_data")

        self.db = Database(db_folder=db_folder, use_encryption=use_encryption)
        self.logger.debug(f"Using SQLite database at {db_folder}")

        # Initialize with sample data if empty
        self._initialize_data()

    @contextmanager
    def _session_scope(self):
        """Provide a transactional scope around a series of operations."""
        session = self.db.get_session()
        try:
            yield session
            session.commit()
        except SQLAlchemyError as e:
            self.logger.error(f"Database error: {str(e)}")
            session.rollback()
            raise
        finally:
            session.close()

    def _initialize_data(self):
        """Initialize with sample data if databases are empty"""
        with self._session_scope() as session:
            # Check if users table is empty
            if session.query(User).count() == 0:
                # Same sample data as in the original implementation
                sample_users = [
                    {
                        "id": 1,
                        "username": "cai1lud",
                        "password": hash_password("cai1lud"),
                        "role": "administrator",
                        "groups": ["IDM_KNUT_USER", "IDM_KNUT_SYSTEMCBUILDTOOLS", "IDM_KNUT_HYDRA"],
                    },
                    {
                        "id": 2,
                        "username": "pes1lud",
                        "password": hash_password("pes1lud"),
                        "role": "administrator",
                        "groups": ["IDM_KNUT_USER", "IDM_KNUT_SYSTEMCBUILDTOOLS", "IDM_KNUT_HYDRA"],
                    },
                    {
                        "id": 3,
                        "username": "chm1lud",
                        "password": hash_password("mihai"),
                        "role": "administrator",
                        "groups": ["IDM_KNUT_USER", "IDM_KNUT_MAINTAINER", "IDM_KNUT_ADMINISTRATOR", "IDM_KNUT_SYSTEMCBUILDTOOLS", "IDM_KNUT_HYDRA", "IDM_KNUT_GMVCU"],
                    },
                ]
                for user_data in sample_users:
                    user = User.from_dict(user_data)
                    session.add(user)

            # Check if projects table is empty
            if session.query(Project).count() == 0:
                sample_projects = [
                    {"id": 1, "name": "E-Bike", "description": "E-Bike project", "groups": ["IDM_KNUT_EBIKE"], "cost_center": "CC-12345"},
                    {
                        "id": 2,
                        "name": "SystemC Build Tools",
                        "description": "SystemC build and analysis tools",
                        "groups": ["IDM_KNUT_SYSTEMCBUILDTOOLS"],
                        "cost_center": "CC-12346",
                    },
                    {"id": 3, "name": "Hydra", "description": "Hydra log monitoring and analysis", "groups": ["IDM_KNUT_HYDRA"], "cost_center": "CC-12347"},
                    {"id": 4, "name": "GM VCU", "description": "GM VCU Project", "groups": ["IDM_KNUT_GMVCU"], "cost_center": "CC-12348"},
                    {"id": 5, "name": "Motronic", "description": "Motronic Project", "groups": ["IDM_KNUT_MOTRONIC"], "cost_center": "CC-12348"},
                ]
                for project_data in sample_projects:
                    project = Project.from_dict(project_data)
                    session.add(project)

    # User methods
    def get_user_by_username(self, username: str) -> List[Dict]:
        """Get user by username"""
        with self._session_scope() as session:
            users = session.query(User).filter(User.username == username).all()
            return [user.to_dict() for user in users]

    def get_user_by_id(self, user_id: int) -> Optional[Dict]:
        """Get user by ID"""
        with self._session_scope() as session:
            user = session.query(User).filter(User.id == user_id).first()
            return user.to_dict() if user else None

    def get_all_users(self) -> List[Dict]:
        """Get all users"""
        with self._session_scope() as session:
            users = session.query(User).all()
            return [user.to_dict() for user in users]

    def create_user(self, user_data: Dict) -> Dict:
        """Create new user"""
        with self._session_scope() as session:
            user = User.from_dict(user_data)
            session.add(user)
            session.flush()  # Flush to get the ID if it was auto-generated
            session.refresh(user)
            return user.to_dict()

    def update_user(self, user_id: int, user_data: Dict) -> bool:
        """Update user"""
        with self._session_scope() as session:
            user = session.query(User).filter(User.id == user_id).first()
            if not user:
                return False

            # Update attributes
            for key, value in user_data.items():
                if hasattr(user, key):
                    setattr(user, key, value)

            return True

    def delete_user(self, user_id: int) -> bool:
        """Delete user"""
        with self._session_scope() as session:
            user = session.query(User).filter(User.id == user_id).first()
            if not user:
                return False

            session.delete(user)
            return True

    # Project methods
    def get_project_by_id(self, project_id: int) -> Optional[Dict]:
        """Get project by ID"""
        with self._session_scope() as session:
            project = session.query(Project).filter(Project.id == project_id).first()
            return project.to_dict() if project else None

    def get_projects_by_user_id(self, user_id: int) -> List[Dict]:
        """Get all projects accessible by user"""
        with self._session_scope() as session:
            # For now, return all projects (TODO: implement proper user-project relationships)
            projects = session.query(Project).all()
            return [project.to_dict() for project in projects]

    # Job methods
    def create_job(self, job_data: Dict) -> Dict:
        """Create new job"""
        with self._session_scope() as session:
            job = Job.from_dict(job_data)
            session.add(job)
            session.flush()  # Flush to get the ID
            session.refresh(job)
            return job.to_dict()

    def get_job_by_id(self, job_id: str) -> Optional[Dict]:
        """Get job by ID"""
        with self._session_scope() as session:
            job = session.query(Job).filter(Job.id == job_id).first()
            return job.to_dict() if job else None

    def get_jobs_by_user(self, user_id: int) -> List[Dict]:
        """Get all jobs for a user"""
        with self._session_scope() as session:
            jobs = session.query(Job).filter(Job.user_id == user_id).order_by(Job.created_at.desc()).all()
            return [job.to_dict() for job in jobs]

    def get_jobs_by_user_and_project(self, user_id: int, project_id: int) -> List[Dict]:
        """Get jobs for a user in a specific project"""
        with self._session_scope() as session:
            jobs = session.query(Job).filter(
                Job.user_id == user_id,
                Job.project_id == project_id
            ).order_by(Job.created_at.desc()).all()
            return [job.to_dict() for job in jobs]

    def update_job(self, job_id: str, job_data: Dict) -> Optional[Dict]:
        """Update job and return updated job data"""
        with self._session_scope() as session:
            job = session.query(Job).filter(Job.id == job_id).first()
            if not job:
                return None

            # Update attributes
            for key, value in job_data.items():
                if hasattr(job, key):
                    setattr(job, key, value)

            session.flush()  # Ensure changes are written
            session.refresh(job)  # Refresh to get updated data
            return job.to_dict()

    def delete_job(self, job_id: str) -> bool:
        """Delete job"""
        with self._session_scope() as session:
            job = session.query(Job).filter(Job.id == job_id).first()
            if not job:
                return False

            session.delete(job)
            return True

    def get_all_jobs(self) -> List[Dict]:
        """Get all jobs (for admin use)"""
        with self._session_scope() as session:
            jobs = session.query(Job).order_by(Job.created_at.desc()).all()
            return [job.to_dict() for job in jobs]

    def get_expired_jobs(self) -> List[Dict]:
        """Get all expired jobs for cleanup"""
        with self._session_scope() as session:
            expired_jobs = session.query(Job).filter(
                Job.expire_at < datetime.utcnow()
            ).all()
            return [job.to_dict() for job in expired_jobs]

    def get_last_cleanup_time(self) -> Optional[datetime]:
        """Get timestamp of last cleanup operation"""
        # TODO: Implement SystemMaintenance table query
        # For now, return None to always trigger cleanup
        return None

    def update_cleanup_time(self, timestamp: datetime) -> None:
        """Update last cleanup timestamp"""
        # TODO: Implement SystemMaintenance table update
        pass

    def get_projects_by_ids(self, project_ids: List[int]) -> List[Dict]:
        """Get multiple projects by IDs"""
        with self._session_scope() as session:
            projects = session.query(Project).filter(Project.id.in_(project_ids)).all()
            return [project.to_dict() for project in projects]

    def get_projects_by_groups(self, groups: List[str]) -> List[Dict]:
        """Get projects that match any of the user's groups"""
        with self._session_scope() as session:
            # This is a more complex query with JSON arrays
            # We need to check if any element in the groups column matches any element in the provided groups list
            projects = []
            for project in session.query(Project).all():
                # Check if any group in the project's groups matches any group in the input groups
                if any(group in groups for group in project.groups):
                    projects.append(project)

            return [project.to_dict() for project in projects]

    def get_all_projects(self) -> List[Dict]:
        """Get all projects"""
        with self._session_scope() as session:
            projects = session.query(Project).all()
            return [project.to_dict() for project in projects]

    def create_project(self, project_data: Dict) -> Dict:
        """Create new project"""
        with self._session_scope() as session:
            project = Project.from_dict(project_data)
            session.add(project)
            session.flush()  # Flush to get the ID if it was auto-generated
            session.refresh(project)
            return project.to_dict()

    def update_project(self, project_id: int, project_data: Dict) -> bool:
        """Update project"""
        with self._session_scope() as session:
            project = session.query(Project).filter(Project.id == project_id).first()
            if not project:
                return False

            # Update attributes
            for key, value in project_data.items():
                if hasattr(project, key):
                    setattr(project, key, value)

            return True

    def delete_project(self, project_id: int) -> bool:
        """Delete project"""
        with self._session_scope() as session:
            project = session.query(Project).filter(Project.id == project_id).first()
            if not project:
                return False

            session.delete(project)
            return True

    # Vocabulary Management Methods
    def get_user_vocabulary_delta(self, user_id: int, vocab_type: str, variant: str) -> Optional[Dict]:
        """Get user's vocabulary modifications (delta from defaults)"""
        with self._session_scope() as session:
            vocab = session.query(UserVocabulary).filter(
                UserVocabulary.user_id == user_id,
                UserVocabulary.vocab_type == vocab_type,
                UserVocabulary.variant == variant
            ).first()
            return vocab.to_dict() if vocab else None

    def save_user_vocabulary_delta(self, user_id: int, username: str, vocab_type: str, variant: str,
                                 added_items: List[str], removed_items: List[str]) -> bool:
        """Save user's vocabulary modifications"""
        try:
            with self._session_scope() as session:
                # Check if record exists
                vocab = session.query(UserVocabulary).filter(
                    UserVocabulary.user_id == user_id,
                    UserVocabulary.vocab_type == vocab_type,
                    UserVocabulary.variant == variant
                ).first()

                if vocab:
                    # Update existing record
                    vocab.added_items = added_items
                    vocab.removed_items = removed_items
                    vocab.updated_at = datetime.utcnow()
                else:
                    # Create new record
                    vocab = UserVocabulary(
                        user_id=user_id,
                        username=username,
                        vocab_type=vocab_type,
                        variant=variant,
                        added_items=added_items,
                        removed_items=removed_items
                    )
                    session.add(vocab)

                return True
        except Exception as e:
            self.logger.error(f"Error saving vocabulary delta: {e}")
            return False

    def delete_user_vocabulary_delta(self, user_id: int, vocab_type: str, variant: str) -> bool:
        """Delete user's vocabulary modifications (reset to defaults)"""
        try:
            with self._session_scope() as session:
                vocab = session.query(UserVocabulary).filter(
                    UserVocabulary.user_id == user_id,
                    UserVocabulary.vocab_type == vocab_type,
                    UserVocabulary.variant == variant
                ).first()

                if vocab:
                    session.delete(vocab)
                    return True
                return False
        except Exception as e:
            self.logger.error(f"Error deleting vocabulary delta: {e}")
            return False

