from testsuites.testsuite import TestSuite


class RegexTestSuite(TestSuite):
    """Test suite for analyzing the logfile for regular expressions  matching"""

    def __init__(self, name="Regex analysis", description="Test suite for analyzing the logfile using regular expressions", logger=None, args=None):
        if args:
            args.match_at = 0.80
            args.training_mode = False
        super().__init__(name, description, logger, args)

    def run(self, input_file, gt):
        super().run(input_file, gt, "regexps")
