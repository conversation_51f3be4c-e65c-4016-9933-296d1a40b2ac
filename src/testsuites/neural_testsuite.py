import os

from testsuites.testsuite import TestSuite


class NeuralTestSuite(TestSuite):
    """ Test suite for analyzing the logfile for vectors matching """
    NAME = "NeuralTestsuite"

    def __init__(self,
                 name="NeuralSearch analysis (*Hydra only)",
                 description="Test suite for analyzing the logfile using neural search",
                 logger=None, args=None):
        if args:
            args.match_at = 0.95
            # training_mode must always be False when analyzing and not training else it will mess up the collections
            args.training_mode = False
        super().__init__(name, description, logger, args)

    def run(self, input_file, gt):
        if not os.getenv('NEURAL_SEARCH_API_KEY'):
            self.logger.warning(
                f"{self.NAME}: To run NeuralSearch please provide the api_key in the the environement variable NEURAL_SEARCH_API_KEY for the xc-cp-qdrant-0 Qdrant DB Server instance.")
            self.logger.warning(f"{self.NAME}: >>> Skipping for now...")
        else:
            # Check if the input file is a Hydra Project log file style stating with 'log-'
            if str(input_file.name).startswith('log-'):
                os.environ['NEURAL_SEARCH_PORT'] = "443"
                os.environ[
                    'NEURAL_SEARCH_HOST'] = "https://xc-cp-qdrant-0.apps.606a86d3bb.ccsd.ipz001.internal.bosch.cloud"
                os.environ['NEURAL_SEARCH_GOOD_COLLECTION'] = "good_collection_hydra"
                super().run(input_file, gt, 'neural')
            else:
                self.logger.warning(
                    f"{self.NAME}: Only [Hydra Project] logs are supported at this moment...")
                self.logger.warning(
                    f"{self.NAME}: >>> Skipping '{input_file}' because it does not seem to be a [Hydra Project] type log file...")
