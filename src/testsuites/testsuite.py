import shutil
import uuid
from pathlib import Path

from analyser.analyser import LogAnalyser
from testsuites.version import VERSION
from utils.logger import Logger
from utils.logs import Logs


class TestSuite:
    """
    A class to represent a test suite for the log analyser.
    """

    NAME = "TestSuite"
    LOG_LEVEL = Logger.INFO

    def __init__(self, name="TestSuite", description="Test suite for the log analyser", logger=None, args=None):
        """
        Initialize the TestSuite with a name, description, logger, and arguments.

        Parameters:
            name (str): The name of the test suite.
            description (str): The description of the test suite.
            logger (Logger): The logger instance for logging messages.
            args (argparse.Namespace): The parsed command line arguments.
        """
        self.logger = logger if logger else Logger(name=f"TestSuite-{uuid.uuid4().hex}", logfile="TestSuite.log", level=self.LOG_LEVEL)
        self.name = name
        self.description = description
        self.results = {
            "nbr_executions": 0,
            "hints": {"true_positives": 0, "false_positives": 0, "false_negatives": 0, "nbr_lines": 0, "precision": 0, "recall": 0, "f1_score": 0},
            "errors": {"true_positives": 0, "false_positives": 0, "false_negatives": 0, "nbr_lines": 0, "precision": 0, "recall": 0, "f1_score": 0},
        }
        self.working_dir = Path(__file__).resolve().parent / Path(".temp")
        self.args = args

    def clean_working_dir(self):
        """
        Clean the working directory by removing all its contents.
        """
        self.logger.debug(f"{self.NAME}: Cleaning work directory...")
        shutil.rmtree(self.working_dir, ignore_errors=True)

    def analyze(self, input_file, analyser_type):
        """
        Analyze the input file using the specified analyser type.

        Parameters:
            input_file (str): The path to the input file.
            analyser_type (str): The type of analyser to use.

        Returns:
            dict: The analysis result.
        """
        analyser = LogAnalyser(
            input=input_file,
            analysis_type=analyser_type,
            ini_config=self.args.ini_config,
            match_at=self.args.match_at,
            training_mode=self.args.training_mode,
            verbose=self.args.verbose,
        )
        analysis_result = analyser.analyze()
        if "root_causes" in analysis_result and analysis_result["root_causes"][0]:
            return analysis_result["root_causes"][0]
        self.logger.warning(f"{self.NAME}: Warning, empty analysis for {input_file} with {analyser_type}")

    def analyze_with_output(self, input_file, analyser_type):
        """
        Analyze the input file and save the output to a file.

        Parameters:
            input_file (str): The path to the input file.
            analyser_type (str): The type of analyser to use.

        Returns:
            dict: The analysis result.
        """
        output_file = Path(f"{Path(input_file).stem}_{analyser_type}.json")
        self.working_dir.mkdir(parents=True, exist_ok=True)
        output = self.working_dir / output_file
        analyser = LogAnalyser(input=input_file, analysis_type=analyser_type, output=output, ini_config=self.args.ini_config)
        analysis_result = analyser.analyze()
        if "root_causes" in analysis_result and analysis_result["root_causes"][0]:
            return analysis_result["root_causes"][0]
        self.logger.warning(f"{self.NAME}: Warning, empty analysis for {output_file} with {analyser_type}")

    def get_gt_dict(self, file, gt):
        """
        Get the ground truth dictionary from the file and ground truth data.

        Parameters:
            file (str): The path to the file.
            gt (dict): The ground truth data.

        Returns:
            dict: The ground truth dictionary.
        """
        if "root_causes" in gt and gt["root_causes"][0]:
            return gt["root_causes"][0]
        self.logger.warning(f"{self.NAME}: Warning, empty analysis for pair ground truth of '{Path(file).name}'")

    def run(self, input_file, gt, analyser_type):
        """
        Run the analysis on the input file and compare the results with the ground truth.

        Parameters:
            input_file (str): The path to the input file.
            gt (dict): The ground truth data.
            analyser_type (str): The type of analyser to use.
        """
        self.logger.debug(f"{self.NAME}: Testsuite version{VERSION} ::: Running analysis...")
        result = self.analyze(input_file, analyser_type)
        self.logger.debug(f"{self.NAME}: Result ---------------------------------------------------------")
        self.logger.debug(f"{self.NAME}: {result}")
        self.logger.debug(f"{self.NAME}: ----------------------------------------------------------------")
        gt = self.get_gt_dict(input_file, gt)
        self.analyze_results(result, gt, "hints")
        self.analyze_results(result, gt, "errors")
        self.results["nbr_executions"] += 1
        self.clean_working_dir()

    def print_results(self):
        """
        Print the results of the test suite.
        """
        print(
            f"{self.name} analysis:\n"
            f"Files read: {self.results['nbr_executions']}\n"
            f"Found hints: {self.results['hints']['true_positives']}/{self.results['hints']['nbr_lines']}\n"
            f"False hints: {self.results['hints']['false_positives']}\n"
            f"Missed hints: {self.results['hints']['false_negatives']}\n"
            f"Percentage of retrieved hints that are revelant (precision): {self.results['hints']['precision']*100}%\n"
            f"Percentage of revelant hints that are retrieved (recall): {self.results['hints']['recall']*100}%\n"
            f"Harmonic mean of precision and recall for hints (f1_score): {self.results['hints']['recall']*100}%\n"
            f"Found errors: {self.results['errors']['true_positives']}/{self.results['errors']['nbr_lines']}\n"
            f"False errors: {self.results['errors']['false_positives']}\n"
            f"Missed errors: {self.results['errors']['false_negatives']}\n"
            f"Percentage of retrieved errors that are revelant (precision): {self.results['errors']['precision']*100}%\n"
            f"Percentage of revelant errors that are retrieved (recall): {self.results['errors']['recall']*100}%\n"
            f"Harmonic mean of precision and recall for errors (f1_score): {self.results['errors']['recall']*100}%\n"
        )

    def get_table_row(self):
        """
        Get the results of the test suite as a table row.

        Returns:
            list: The results of the test suite as a table row.
        """
        return [
            self.name,
            str(self.results["nbr_executions"]),
            f"{self.results['hints']['true_positives']}/{self.results['hints']['nbr_lines']}",
            str(self.results["hints"]["false_positives"]),
            str(self.results["hints"]["false_negatives"]),
            str(self.results["hints"]["precision"]),
            str(self.results["hints"]["recall"]),
            str(self.results["hints"]["f1_score"]),
            f"{self.results['errors']['true_positives']}/{self.results['errors']['nbr_lines']}",
            str(self.results["errors"]["false_positives"]),
            str(self.results["errors"]["false_negatives"]),
            str(self.results["errors"]["precision"]),
            str(self.results["errors"]["recall"]),
            str(self.results["errors"]["f1_score"]),
        ]

    def analyze_results(self, json_result, gt_dict, type):
        """
        Analyze the results of the analysis and compare them with the ground truth.

        Parameters:
            json_result (dict): The JSON result of the analysis.
            gt_dict (dict): The ground truth dictionary.
            type (str): The type of analysis (hints or errors).
        """
        self.logger.debug(f"{self.NAME}: Analyzing results, session: {uuid.uuid4().hex}...")
        if type == "hints":
            type_lines = "hint_lines"
            gt_list = Logs.expand_range(gt_dict["hint_lines"])
        else:
            type_lines = "error_lines"
            gt_list = Logs.expand_range(gt_dict["error_lines"])

        for line in gt_list:
            if line in json_result[type_lines]:
                self.results[type]["true_positives"] += 1
            else:
                self.results[type]["false_negatives"] += 1
            self.results[type]["nbr_lines"] += 1

        for line in json_result[type_lines]:
            if line not in gt_list:
                self.results[type]["false_positives"] += 1

        if self.results[type]["true_positives"] == 0:
            self.results[type]["precision"] = 0
            self.results[type]["recall"] = 0
            self.results[type]["f1_score"] = 0
            if self.results[type]["false_positives"] == 0:
                self.results[type]["precision"] = -1
                self.results[type]["f1_score"] = -1
            if self.results[type]["false_negatives"] == 0:
                self.results[type]["recall"] = -1
                self.results[type]["f1_score"] = -1
        else:
            self.results[type]["precision"] = round(self.results[type]["true_positives"] / (self.results[type]["true_positives"] + self.results[type]["false_positives"]), 3)
            self.results[type]["recall"] = round(self.results[type]["true_positives"] / (self.results[type]["true_positives"] + self.results[type]["false_negatives"]), 3)
            self.results[type]["f1_score"] = round(
                2 * self.results[type]["recall"] * self.results[type]["precision"] / (self.results[type]["recall"] + self.results[type]["precision"]), 3
            )
