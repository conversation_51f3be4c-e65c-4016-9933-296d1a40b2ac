from testsuites.keyword_testsuite import KeywordTestSuite
from testsuites.neural_testsuite import NeuralTestSuite
from testsuites.regex_testsuite import RegexTestSuite
from utils.logger import Logger

TEST_SUITES = dict(
    KeywordTestSuite=KeywordTestSuite,
    RegexTestSuite=RegexTestSuite,
    # May take even a longer time than vectors...
    NeuralTestSuite=NeuralTestSuite,
)


class TestSuitesFactory:
    """The Test suites factory"""

    def __init__(self, logger=None, args=None):
        self.logger = logger if logger else Logger(name="TestSuites", logfile="test-suites.log", level=Logger.DEBUG)
        self.arg = args

    def create(self, test_suite_type):
        if test_suite_type in TEST_SUITES.keys():
            return TEST_SUITES[test_suite_type](logger=self.logger, args=self.arg)
        else:
            raise NotImplementedError(f'Test Suite of type "{test_suite_type}" is not yet implemented, possible types are {TEST_SUITES.keys()} ...')
