import argparse
import json
import os
import sys

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
import uuid
from pathlib import Path

import tabulate

# import main
from analyser.analyser import LogAnalyser
from testsuites.testsuites_factory import TestSuitesFactory, TEST_SUITES
from utils.logger import Logger
from utils.files import Files
from utils.logs import Logs
from utils.time_monitor import timed


class CleanTestSuite:
    """
    A class to represent a clean test suite.
    """

    def __init__(self, name, description):
        """
        Initialize the CleanTestSuite with a name and description.

        Parameters:
            name (str): The name of the test suite.
            description (str): The description of the test suite.
        """
        self.name = name
        self.description = description
        self.score = 0
        self.nbr_executions = 0

    @staticmethod
    def analyze(input_file, analyser_type):
        """
        Analyze the input file using the specified analyser type.

        Parameters:
            input_file (str): The path to the input file.
            analyser_type (str): The type of analyser to use.

        Returns:
            dict: The analysis results.
        """
        analyser = LogAnalyser(input=input_file, analysis_type=analyser_type, error_vocab=None, false_positive_vocab=None, hint_vocab=None, success_vocab=None)
        return analyser.analyze()

    def run(self, file, fullpath, gt):
        """
        Run the test suite on the given file and ground truth.

        Parameters:
            file (str): The file to analyze.
            fullpath (str): The full path to the file.
            gt (dict): The ground truth data.
        """
        if gt["exit_code"] == 0 and "error_lines" not in gt and "hint_lines" not in gt:
            self.nbr_executions += 1
            log_analyser_result = main.extract_rows(fullpath, 10)
            if log_analyser_result is None or log_analyser_result == "":
                self.score += 1


class BenchTest:
    """
    A class to run tests for the log-analyser detection methods vs ground-truth files and generate results.
    Limited to the first root cause, reason: multi-root cause analysis results are not implemented yet.
    """

    NAME = "BenchTest"

    OUTPUT_TYPES = ["json", "html", "rst", "orgtbl"]
    HEADERS = [
        "Test suite",
        "Nbr executions",
        "Found hints",
        "False hints",
        "Missed hints",
        "Precision hints",
        "Recall hints",
        "f1_score hints",
        "Found errors",
        "False errors",
        "Missed errors",
        "Precision errors",
        "Recall errors",
        "f1_score errors",
    ]
    VECTOR_BASED_METHODS = ["VectorsTestSuite", "NeuralTestSuite"]
    LOG_LEVEL = Logger.INFO

    def __init__(self):
        """
        Initialize the BenchTest with a unique identifier and logger.
        """
        self.id = uuid.uuid4()
        self.logger = Logger(name=f"CleanTestSuite-{self.id}", logfile="CleanTestSuite.log", level=self.LOG_LEVEL)
        self.lg = Logs(self.logger)
        self.files = Files(self.logger)

    @staticmethod
    def get_json_output(table, headers):
        """
        Generate JSON output from the table and headers.

        Parameters:
            table (list): The table data.
            headers (list): The table headers.

        Returns:
            str: The JSON formatted output.
        """
        results = []
        for row in table:
            json_result = {}
            for i, header in enumerate(headers):
                header = header.lower().replace(" ", "_")
                if "/" in row[i]:
                    found, total = row[i].split("/")
                    json_result[header] = {"found": found, "total": total}
                    continue
                json_result[header] = row[i]
            results.append(json_result)
        return json.dumps(results, indent=2)

    def validate_args(self, args):
        """
        Validate the command-line arguments.

        Parameters:
            args (argparse.Namespace): The parsed command line arguments.

        Raises:
            OSError: If the specified folder path is not a directory.
        """
        if not Path(args.folder).is_dir():
            error = f"{self.NAME}: Error: specified path '{args.folder}' is not a folder."
            self.logger.error(error)
            raise OSError(error)
        if args.verbose:
            self.logger.logger.setLevel(Logger.DEBUG)

    @timed
    def test(self, args):
        """
        Run the test for the BenchTest instance.

        Parameters:
            args (argparse.Namespace): The parsed command line arguments.

        Returns:
            str: The formatted output of the test results.
        """
        self.validate_args(args)
        self.logger.debug(f"{self.NAME}: Running test method for BenchTest instance: {self.id}")
        pairs = self.lg.get_pairs(args.folder)
        table = []
        if not os.getenv("NEURAL_SEARCH_API_KEY"):
            self.logger.warning(
                f"{self.NAME}: Environment variable NEURAL_SEARCH_API_KEY is not set. NeuralSearch method can't run without it. Please set up the api key in  NEURAL_SEARCH_API_KEY for xc-cp-qdrant-0 Qdrant DB Server instance."
            )
        for suite in TEST_SUITES.keys():

            if args.skip_embeddings_based and suite in self.VECTOR_BASED_METHODS:
                self.logger.debug(f"{self.NAME}: Skipping suite: {suite}, Reason: '-s' or '--skip_embeddings_based' option was specified ...")
                continue

            self.logger.debug(f"{self.NAME}: -- Test suite: {suite} ------------------------------------------")
            test_suite = TestSuitesFactory(logger=self.logger, args=args).create(suite)

            for [gt_file, log_file] in pairs:
                self.logger.debug(f"Processing pair: {gt_file}, {log_file}")
                gt = self.files.get_json(gt_file)

                if self.lg.is_valid_gt(gt):
                    self.logger.debug(f"--- Running: {suite} - for {log_file} ...")
                    if self.logger.logger.level == self.logger.DEBUG:
                        run_benchmarked = timed(test_suite.run)
                        run_benchmarked(log_file, gt)
                    else:
                        test_suite.run(log_file, gt)
            table.append(test_suite.get_table_row())
            # make sure we clean the trash leftovers from memory...
            del test_suite
        return self.format_output(table, args.output_type)

    def format_output(self, table, output_format):
        """
        Format the output based on the specified format.

        Parameters:
            table (list): The table data.
            output_format (str): The output format.

        Returns:
            str: The formatted output.
        """
        if output_format == self.OUTPUT_TYPES[0]:  # "json"
            output = self.get_json_output(table, self.HEADERS)
        else:
            output = tabulate.tabulate(
                table, headers=self.HEADERS, tablefmt=output_format, colalign=("left", "right", "right", "right", "right", "right", "right", "right", "right", "right", "right")
            )
        return output

    def output(self, args, content):
        """
        Output the content to a file or print to console.

        Parameters:
            args (argparse.Namespace): The parsed command line arguments.
            content (str): The content to output.
        """
        if args.output:
            self.logger.debug(f"{self.NAME}: --- Writing output of type '{args.output}' in the file '{args.output}' ...")
            self.files.write_file(content, args.output)
        else:
            print(content)


if __name__ == "__main__":
    from testsuites.main import main_fn

    main_fn()
