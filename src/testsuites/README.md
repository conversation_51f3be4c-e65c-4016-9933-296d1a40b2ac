# Test suites / Bench test

*The primary goal of this tool is to simplify the process of testing and evaluating the Log Analyzer.*

Test suites / bench test is a script that operates the Log Analyzer with all the analysis methods for testing and evaluation purpose

> **Warning**: AI based methods will not run without the required models. If internet
> access is available, the models will be downloaded automatically. If not, you will need to provide and configure those models manually. Please read the analyzer documentation
> above in regard to those methods. If you can't manage to install and configure the models and still want to run the BenchTest, just use the '--skip_embeddings_based' option to skip
> those methods in the tests.


![Bench test](/doc/Benchtest_flow.drawio.svg)

| Argument                                          | Description                                                                                                                                                                                                         |
|---------------------------------------------------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| `folder`                                          | Mandatory, folder containing logs & ground-truth files to be analyzed.                                                                                                                                              |
| `-output_type, --output_type`                     | Optional, Output type, one of (json, html, rst, orgtbl), default is 'orgtbl' text based suited for mono-font console display, 'rst' is text based useful for true-type font display e.g. for inline Gerrit comments |
| `-output, --output`                               | Optional, write output to file                                                                                                                                                                                      |
| `-verbose, --verbose`                             | Optional, verbose mode, will automatically trigger DEBUG logging into the log file also                                                                                                                             |
| `-skip_embeddings_based, --skip_embeddings_based` | Optional, skip the vectors based methods                                                                                                                                                                            |
| `-ini_config, --ini_config`                       | Optional, specify a file (with path) for the configuration, e.g. /configs/custom_config1/config.ini. Default (if not specified) is the 'config.ini' in the of the log-nalyzer (parent of analyser) folder.          |

> **Warning**: MAKE SURE YOU ARE IN THE `<repo_dir>/src` DIRECTORY OF THE PROJECT BEFORE RUNNING THE COMMANDS

### Examples of usage

Output the table result in HTML format to a HTML file, verbose mode

```commandline
python -m testsuites ./samples/ --output_type html --output test-suite-results.html --verbose -s
```

Output the table result in JSON format to a JSON file, verbose mode

```commandline
python -m testsuites ./samples/ --output_type json --output test-suite-results.json --verbose -s
```

Display results table in console with RST format, good for Gerrit comments, for instance, non-verbose

```commandline
python -m testsuites ./samples/ --output_type rst -s
```

Display results table with ORGTBL format, non-verbose

```commandline
python -m testsuites ./samples/ -s
```

### Update history

| Date       | Author                  | Reference          |
|------------|-------------------------|--------------------|
| 2025-01-24 | Mihai-Ciprian Chezan    | CHM1LUD            |
|------------|-------------------------|--------------------|
| 2025-05-28 | Alban Courpon           | COA1LUD            |
