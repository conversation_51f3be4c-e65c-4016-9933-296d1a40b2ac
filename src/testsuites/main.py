import argparse
import traceback

from testsuites.benchtest import BenchTest
from utils.default_config import DEFAULT_CONFIG_FILE


def main_fn():
    """ Entry point """
    parser = argparse.ArgumentParser(
        description="Run a test of all log-analyser hotspot detection methods. Prints or outputs to file the results.",
        formatter_class=argparse.RawTextHelpFormatter)
    parser.add_argument(
        "folder",
        help="Mandatory, folder containing logs & ground-truth files to be analyzed.")
    parser.add_argument(
        "-t", "--output_type", type=str, default='orgtbl',
        choices=BenchTest().OUTPUT_TYPES,
        help=f"Optional, Output type, one of ({', '.join(BenchTest().OUTPUT_TYPES)}), "
             f"default is 'orgtbl', for Gerrit inline comments you can use 'rst'")
    parser.add_argument(
        "-o", "--output", type=str, default=None, help=f"Optional, write output to file")
    parser.add_argument(
        '-v', '--verbose', help="Optional, verbose mode", default=False, action='store_true')
    parser.add_argument(
        '-s', '--skip_embeddings_based', help="Optional, skip the vectors based methods (vectors and neural)",
        default=False, action='store_true')
    parser.add_argument(
        '-i', '--ini_config',
        help="Optional, specify a file (with path) for the configuration, e.g. /configs/custom_config1/config.ini. Default (if not specified) is the 'config.ini' in the of the log-nalyzer (parent of analyser) folder.",
        default=DEFAULT_CONFIG_FILE)

    args = parser.parse_args()
    bench = BenchTest()
    print(".: BenchTest :....................................................... \n")
    print(
        "Be aware that the AI based methods ('vectors' and 'neural') will not run without the required models. If internet access is available, and no proxy restrictions, the models will be downloaded automatically. If not, you will need to provide and configure those models manually. Please read the analyser documentation. If you can't manage to install and configure the models and still want to run the BenchTest, just use the '-s' or '--skip_vectors' option to skip those methods in the tests.\n")
    try:
        print("=== Staring test suite ...")
        results = bench.test(args)
        bench.output(args, results)
    except Exception:
        print(traceback.format_exc())


if __name__ == '__main__':
    main_fn()
