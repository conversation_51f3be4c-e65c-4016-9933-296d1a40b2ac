import json
import unittest
from unittest.mock import patch, MagicMock

from visualiser import Visualiser


class TestAnomalyVisualizer(unittest.TestCase):
    def setUp(self):
        self.av = Visualiser()

    @patch('argparse.ArgumentParser.parse_args')
    def test_get_args(self, mock_args):
        mock_args.return_value = MagicMock()
        self.assertIsNotNone(self.av.get_args())

    def test_get_class_with_hint_line(self):
        results = {"hint_lines": [1], "error_lines": [], "success_lines": []}
        self.assertEqual(self.av.get_class(1, results), ' class="hint"')

    def test_get_class_with_error_line(self):
        results = {"hint_lines": [], "error_lines": [1], "success_lines": []}
        self.assertEqual(self.av.get_class(1, results), ' class="error"')

    def test_get_class_with_success_line(self):
        results = {"hint_lines": [], "error_lines": [], "success_lines": [1]}
        self.assertEqual(self.av.get_class(1, results), ' class="success"')

    def test_get_class_with_no_matching_line(self):
        results = {"hint_lines": [], "error_lines": [], "success_lines": []}
        self.assertEqual(self.av.get_class(1, results), '')

    def test_process_line(self):
        results = {"hint_lines": [1], "error_lines": [], "success_lines": []}
        self.assertEqual(self.av.process_line(1, "test", results), '<div class="hint"><div>1</div><div>test</div></div> \n')

    def test_validate_with_missing_input(self):
        args = MagicMock(input=None, results="results")
        with self.assertRaises(ValueError):
            self.av.validate(args)

    def test_validate_with_missing_results(self):
        args = MagicMock(input="input", results=None)
        with self.assertRaises(ValueError):
            self.av.validate(args)

    @patch('pathlib.Path.is_file')
    @patch('pathlib.Path.exists')
    @patch('main.Files.get_json')
    def test_get_results_with_file(self, mock_get_json, mock_exists, mock_is_file):
        mock_is_file.return_value = True
        mock_exists.return_value = True
        mock_get_json.return_value = {"hint_lines": [], "error_lines": [], "success_lines": []}
        args = MagicMock(results="results.json")
        self.assertEqual(self.av.get_results(args), {"hint_lines": [], "error_lines": [], "success_lines": []})

    @patch('json.loads')
    def test_get_results_with_json_string(self, mock_loads):
        mock_loads.return_value = {"hint_lines": [], "error_lines": [], "success_lines": []}
        args = MagicMock(results='{"hint_lines": [], "error_lines": [], "success_lines": []}')
        self.assertEqual(self.av.get_results(args), {"hint_lines": [], "error_lines": [], "success_lines": []})

    @patch('json.loads')
    def test_get_results_with_invalid_json_string(self, mock_loads):
        mock_loads.side_effect = json.JSONDecodeError("Invalid JSON", doc="", pos=0)
        args = MagicMock(results='invalid_json')
        with self.assertRaises(ValueError):
            self.av.get_results(args)


if __name__ == '__main__':
    unittest.main()
