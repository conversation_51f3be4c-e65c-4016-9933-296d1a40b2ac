import argparse

from visualiser.version import VERSION
from visualiser.visualiser import gen_visualisation


def get_args():
    """
    Parse and return command-line arguments.

    Returns:
        argparse.Namespace: Parsed command line arguments.
    """
    parser = argparse.ArgumentParser(
        description=f".: AnomalyVisualizer v.{VERSION} :. -- Generates a HTML visualization file from the anomaly detection results",
        epilog="Examples:\n\tOutput html directly to console:\n\t\tpython .\\visualiser.py .\\samples\\log-10002.txt .\\samples\\input_sample.json"
               "\n\tOutput to html file:\n\t\tpython .\\visualiser.py .\\samples\\log-10002.txt .\\samples\\input_sample.json -o .\\sample.html"
               "\n\tInline JSON with output to html file (note the slashed quotations):\n\t\tpython .\\visualiser.py .\\samples\\log-10002.txt '{\\\"hint_lines\\\":[9904,9905,9906,9907,9908,9916,9917,9918,9919,9920,9924,9925,9926,9927,9928,9929],\\\"error_lines\\\":[443,462,481,9988,10006,10007],\\\"success_lines\\\":[]}' -o .\\sample.html",
        formatter_class=argparse.RawTextHelpFormatter
    )
    parser.add_argument("input", help="Log file including path", type=str)
    parser.add_argument("results", help="JSON file, or JSON one-line string, containing the anomaly detection results", type=str)
    parser.add_argument("-output", "--output", help="HTML file containing the log files with highlights, if not specified the content is outputted to console", type=str)
    parser.add_argument("-llmresponse", "--llmresponse", help="The LLM response file", type=str)
    parser.add_argument('-verbose', '--verbose', help="Verbose mode", action='store_true', default=False)
    parser.add_argument('-compact', '--compact', help="Compact/skipp consecutive lines that do not have any error or hint.", action='store_true', default=True)
    parser.add_argument('-compact_from', '--compact_from',
                        help="Compact log lines with greater number of lines than this. Default is 2000. Note that files with less lines than this will not be compacted even if --compact is specified.",
                        type=int, default=2000)
    parser.add_argument('-compact_max_normal_lines', '--compact_max_normal_lines',
                        help="Max consecutive normal lines shown without being compacted. Everything above this number gets compacted. Default is 10",
                        type=int, default=10)
    parser.add_argument('--compact_context_lines', help="Number of normal lines shown above and below critical lines. Default is.5", type=int, default=5)
    return parser.parse_args()


def main_fn():
    args = get_args()
    gen_visualisation(
        input_file=args.input,
        results_file=args.results,
        output=args.output,
        llm_response_file=args.llmresponse,
        compact=args.compact,
        compact_from=args.compact_from,
        compact_max_normal_lines=args.compact_max_normal_lines,
        compact_context_lines=args.compact_context_lines,
        verbose=args.verbose,
        gzip=True)


if __name__ == '__main__':
    main_fn()
