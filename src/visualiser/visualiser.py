import base64
import gzip
import html
import json
import re
import traceback
import uuid
from datetime import datetime
from pathlib import Path
from typing import Optional

from minify_html import minify

from utils.files import Files
from utils.logger import Logger

NAME = "Visualiser"


class Visualiser:
    """
    Generates HTML visualization files from anomaly detection results and log files.
    It can also be used as a utility from the command line, run with -h to see the options.
    """

    NAME = NAME
    PARENT = Path(__file__).parent
    TEMPLATE = PARENT / "static/template.html"
    TEMPLATE_GZIP = PARENT / "static/template.gzip.html"
    TEMPLATE_GZIP_BODY = PARENT / "static/template.body.gzip.html"
    STYLES = PARENT / "static/styles.css"
    STYLES_EMBED = PARENT / "static/styles_embed.css"
    JS = PARENT / "static/scripts.js"
    DEFAULT_COMPACT_FROM = 2000
    DEFAULT_COMPACT_MAX_NORMAL_LINES = 10
    DEFAULT_COMPACT_CONTEXT_LINES = 5

    GENERIC_RESPONSE = (
        "As part of my role, I meticulously analyze log files and provide valuable insights. "
        "\n\n"
        "I can identify error lines, hint at relevant sections, and highlight success lines. "
        "I can locate the root cause within the logs, making troubleshooting more fast and efficient. "
        "I can describe the error name and provide a clear explanation of what went wrong. "
        "When issues arise, I’ll offer practical solutions to address them. "
        "Whether it’s missing dependencies or configuration glitches, count on me to guide you toward resolution."
        "\n\n"
        "I'm committed to making your log analysis experience easy, fast, seamless and insightful!"
    )

    HTML_COMMENT_TITLE = "<!-- .: KnutGTP :.  Analysis Visualization for: '{file}' -->"
    ROOT_CAUSE_CAPTION = "Root Cause Line:"
    LOG_LEVEL = Logger.INFO

    def __init__(
        self,
        input_file: str,
        results_file: str,
        output: Optional[str] = None,
        llm_response_file: Optional[str] = None,
        gzip: bool = True,
        verbose: bool = False,
        logger: Optional[Logger] = None,
    ) -> None:
        """
        Initialize Visualiser with logger and file handler.

        Args:
            input_file (str): Path to input log file.
            results_file (str): Path to results file.
            output (str, optional): Output file path.
            llm_response_file (str, optional): LLM response file path.
            gzip (bool, optional): Enable gzip compression.
            verbose (bool, optional): Enable verbose logging.
            logger (Logger, optional): Logger instance.
        """
        if not Path(input_file).exists():
            raise FileNotFoundError(f"Input file {input_file} not found")
        self.logger = (
            logger
            if logger
            else Logger(
                name=f"AnomalyVisualizer-{uuid.uuid4().hex}",
                logfile="AnomalyVisualizer.log",
                level=self.LOG_LEVEL,
            )
        )
        self.files = Files(self.logger)
        self.input = input_file
        self.results = results_file
        self.output = output
        self.llm_response_file = llm_response_file
        self.gzip = gzip
        self.verbose = verbose
        if self.verbose:
            self.logger.logger.setLevel(Logger.DEBUG)

    @staticmethod
    def get_quick_links_template() -> str:
        """
        Return the quick links template for the visualization.

        Returns:
            str: The quick links template.
        """
        return (
            '<div class="quicklink quicklinks-{}"><div class="heading">{}</div>'
            '<h3 class="title"><span class="material-symbols-outlined">{}</span></h3><p class="scroller">{}</p></div>'
        )

    @staticmethod
    def get_link_types() -> dict:
        """Return a fresh dictionary of link types for each visualization."""
        return {
            "rootcause": {
                "lines": [],
                "links": "",
                "label": "ROOT CAUSE",
                "icon": "crisis_alert",
            },
            "error": {"lines": [], "links": "", "label": "ERRORS", "icon": "error"},
            "hint": {"lines": [], "links": "", "label": "HINTS", "icon": "warning"},
            "success": {
                "lines": [],
                "links": "",
                "label": "SUCCESS",
                "icon": "verified",
            },
        }

    def get_llm_response(self, llm_response_file):
        """
        Retrieve the LLM response from the provided file or the generic response.

        Returns:
            str: The LLM response content.
        """
        if llm_response_file is not None and Path(llm_response_file).is_file():
            self.logger.debug(f"{self.NAME}: Loading LLM response from file: {llm_response_file} ...")
            return self.files.get_lines(llm_response_file, False)
        return self.GENERIC_RESPONSE

    @staticmethod
    def convert_to_html_paragraphs(multiline_string):
        """
        Convert a multiline string into HTML paragraphs.

        Parameters:
            multiline_string (str): The input multiline string.

        Returns:
            str: The HTML string with paragraphs.
        """
        lines = multiline_string.split("\n")
        lines = [line.strip() for line in lines if line.strip()]
        html_paragraphs = "\n".join(f"<p>{line}</p>" for line in lines)
        return html_paragraphs

    @staticmethod
    def get_class(line_number, results):
        """
        Determine the CSS class for a given line number based on the results.

        Parameters:
            line_number (int): The line number in the log file.
            results (dict): The anomaly detection results.

        Returns:
            str: The CSS class for the given line number.
        """
        if "root_causes" not in results:
            raise ValueError("Root causes ('root_causes' key) not found in results input JSON")
        class_name = ""
        for root_cause in results["root_causes"]:
            if "hint_lines" in root_cause and line_number in root_cause["hint_lines"]:
                class_name = "hint"
            if "error_lines" in root_cause and line_number in root_cause["error_lines"]:
                class_name = "error"
        if "root_cause_lines" in results and line_number in results["root_cause_lines"]:
            class_name = "rootcause"
        if "success_lines" in results and line_number in results["success_lines"]:
            class_name = "success"
        return f' class="{class_name}"' if class_name else ""

    def process_line(self, line_number, line, results):
        """
        Process a single line of the log file and return its HTML representation.

        Parameters:
            line_number (int): The line number in the log file.
            line (str): The content of the line.
            results (dict): The anomaly detection results.

        Returns:
            tuple: A tuple containing the CSS class and the HTML representation of the line.
        """
        class_name = self.get_class(line_number, results)
        line = html.escape(line.rstrip())
        return (
            class_name,
            f'<div{class_name}><div id="n{line_number}">{line_number}</div><div>{line}</div></div> \n',
        )

    def minimap_process_line(self, line_number, line, results, max_width):
        """
        Process a single line for the minimap and return its HTML representation.

        Parameters:
            line_number (int): The line number in the log file.
            line (str): The content of the line.
            results (dict): The anomaly detection results.
            max_width (int): The maximum width for the minimap.

        Returns:
            str: The HTML representation of the line for the minimap.
        """
        line_length = round((len(line) / max_width) * 100)
        class_name = self.get_class(line_number, results)
        width = "100%" if class_name else f"{line_length}%"
        return f'<div{class_name} style="width:{width}"></div> \n'

    def get_results(self):
        """
        Retrieve and parse the results from the provided JSON file or string.

        Returns:
            dict: The parsed anomaly detection results.

        Raises:
            ValueError: If the results JSON is not valid.
        """
        if isinstance(self.results, str):
            if Path(self.results).is_file() and Path(self.results).exists():
                return self.files.get_json(str(self.results))
            try:
                return json.loads(self.results)
            except json.JSONDecodeError as e:
                raise ValueError(f"Results JSON is not valid: {e}")
        else:
            raise TypeError(f"Results must be a string or file path, got {type(self.results)}")

    @staticmethod
    def get_optimal_width(lines):
        """
        Calculate the optimal width for the minimap based on the log lines.

        Parameters:
            lines (list): A list of lines from the log file.

        Returns:
            int: The optimal width for the minimap.
        """
        longest_line = len(max(lines, key=len))
        average_length = sum(len(line) for line in lines) / len(lines)
        return longest_line if longest_line <= average_length * 2 else longest_line / 2

    @staticmethod
    def add_quick_link(i, html_class, line_type):
        """
        Add a quick link for a specific line type if applicable.

        Parameters:
            i (int): The line number.
            html_class (str): The CSS class for the line.
            line_type (str): The type of the line (e.g., rootcause, error, hint, success).

        Returns:
            str: The HTML anchor tag for the quick link.
        """
        return f'<a href="#n{i}">{i}</a> ' if line_type in html_class else ""

    def create_quicklinks(self, link_types):
        """
        Create the quicklinks HTML section.

        Parameters:
            link_types (dict): A dictionary containing link types and their associated data.
                Each key is a link type (e.g., 'rootcause', 'error', 'hint', 'success') and each value is a dictionary with:
                    - 'links' (str): HTML anchor tags for the quick links.
                    - 'label' (str): The label for the quick link section.
                    - 'icon' (str): The icon for the quick link section.

        Returns:
            str: The HTML content for the quicklinks section.
        """
        quicklinks_html = ""
        for link_type, data in link_types.items():
            if data["lines"]:
                grouped_lines = self.group_consecutive_numbers(data["lines"])
                for group in grouped_lines:
                    if len(group) == 1:
                        data["links"] += f'<a href="#n{group[0]}">{group[0]}</a> '
                    else:
                        data["links"] += f'<a href="#n{group[0]}">{group[0]}-{group[-1]}</a> '
                template = self.get_quick_links_template()
                quicklinks_html += template.format(link_type, data["label"], data["icon"], data["links"])
        return quicklinks_html

    @staticmethod
    def add_spacing_divs(html_elements_list, count, top=True, bottom=True, template="<div></div>\n"):
        """
        Add a number of empty divs to the list of HTML elements.

        Parameters:
            html_elements_list (list): A list of HTML elements.
            count (int): The number of empty divs to add.
            template (str): The template for the empty div.
            top (bool): Whether to add the empty divs to the top of the list.
            bottom (bool): Whether to add the empty divs to the bottom of the list

        Returns:
            list: The updated list of HTML elements.
        """
        for _ in range(count):
            if bottom:
                html_elements_list.append(template)
            if top:
                html_elements_list.insert(0, template)
        return html_elements_list

    def add_spacings(self, body_html, minimap_html):
        """
        Add spacing divs to the body and minimap HTML lists.

        Parameters:
            body_html (list): A list of HTML elements for the body.
            minimap_html (list): A list of HTML elements for the minimap.
        """
        self.add_spacing_divs(body_html, 1)
        self.add_spacing_divs(body_html, 1, False, True)
        current_datetime = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.add_spacing_divs(
            body_html,
            1,
            True,
            False,
            f"<div class=analysis-file><div></div><div>{self.input} -- {current_datetime}</div></div>\n",
        )
        self.add_spacing_divs(minimap_html, 2, True, True, '<div style="width:0px"></div>\n')

    @staticmethod
    def group_consecutive_numbers(numbers):
        """
        Group consecutive numbers in a list.

        This function takes a list of numbers and groups consecutive numbers together.
        For example, given the input [1, 2, 3, 5, 6, 8], the output will be [[1, 2, 3], [5, 6], [8]].

        Parameters:
            numbers (list of int): A list of integers to be grouped.

        Returns:
            list of list: A list of lists, where each sublist contains consecutive numbers.
        """
        groups = []
        current_group = []
        for number in sorted(numbers):
            if not current_group or number == current_group[-1] + 1:
                current_group.append(number)
            else:
                groups.append(current_group)
                current_group = [number]
        if current_group:
            groups.append(current_group)
        return groups

    def is_special(self, line_number, results):
        """
        Determines if the line is special (rootcause, error, hint, success).

        Parameters:
            line_number (int): The line number (1-based).
            results (dict): The anomaly detection results.

        Returns:
            bool: True if the line is special, False otherwise.
        """
        class_name = self.get_class(line_number, results)
        return any(cls in class_name for cls in ["rootcause", "error", "hint", "success"])

    def append_lines(self, start, end, lines, results, body_html, minimap_html, optimal_width):
        """
        Append lines and their processed HTML to the body and minimap HTML lists.

        Parameters:
            start (int): Starting index (inclusive, 0-based).
            end (int): Ending index (exclusive, 0-based).
            lines (list): The list of log lines.
            results (dict): The anomaly detection results.
            body_html (list): The list of body HTML fragments.
            minimap_html (list): The list of minimap HTML fragments.
            optimal_width (int): The optimal width for minimap processing.
        """
        end = min(end, len(lines))  # Ensure end index is within bounds
        for i in range(start, end):
            line_number = i + 1
            html_class, html_line = self.process_line(line_number, lines[i], results)
            body_html.append(html_line)
            minimap_html.append(self.minimap_process_line(line_number, lines[i], results, optimal_width))

    def handle_collapse(
        self,
        start_index,
        end_index,
        lines,
        results,
        body_html,
        minimap_html,
        optimal_width,
        context_lines,
    ):
        """
        Collapse lines and add placeholders for skipped lines.

        Parameters:
            start_index (int): Starting index of the collapsed section (0-based).
            end_index (int): Ending index of the collapsed section (0-based).
            lines (list): The list of log lines.
            results (dict): The anomaly detection results.
            body_html (list): The list of body HTML fragments.
            minimap_html (list): The list of minimap HTML fragments.
            optimal_width (int): The optimal width for minimap processing.
            context_lines (int): The number of context lines to display around collapsed normal lines.
        """
        # Ensure indices are within bounds
        start_context_end = min(start_index + context_lines, len(lines))
        end_context_start = max(end_index - context_lines + 1, 0)
        self.append_lines(
            start_index,
            start_context_end,
            lines,
            results,
            body_html,
            minimap_html,
            optimal_width,
        )
        if end_context_start > start_context_end:
            body_html.append('<div class="normal collapsed">...</div>\n')
        self.append_lines(
            end_context_start,
            end_index + 1,
            lines,
            results,
            body_html,
            minimap_html,
            optimal_width,
        )
        # self.logger.debug(f"Collapsing lines {start_index + 1}-{end_index + 1}, added context lines {end_context_start + 1}-{end_index + 1} ...")

    def get_main_divs(
        self,
        lines,
        results,
        root_cause_lines,
        enable_collapse=False,
        max_normal_lines=10,
        context_lines=5,
    ):
        """
        Generate the main HTML divisions for the log visualization.
        """
        self.logger.debug(f"{self.NAME}: Generating quicklinks and log lines content ...")

        # Initialize helper variables
        optimal_width = self.get_optimal_width(lines)
        body_html = []
        minimap_html = []
        if root_cause_lines:
            results["root_cause_lines"] = root_cause_lines  # Inject root cause lines if provided

        # Start iterating through lines
        index = 0
        quicklink_info = self.get_link_types()

        while index < len(lines):
            current_line_number = index + 1  # Line numbers are 1-based
            current_line = lines[index]

            if enable_collapse and not self.is_special(current_line_number, results):
                # Look for collapsible sections of lines
                collapse_start = index
                collapse_end = collapse_start
                while collapse_end < len(lines) and not self.is_special(collapse_end + 1, results):
                    collapse_end += 1

                # Collapse lines only if they exceed the max allowed group size
                if (collapse_end - collapse_start + 1) > max_normal_lines:
                    self.add_lines_to_quicklinks(collapse_start, collapse_end, results, quicklink_info)
                    self.handle_collapse(
                        collapse_start,
                        collapse_end,
                        lines,
                        results,
                        body_html,
                        minimap_html,
                        optimal_width,
                        context_lines,
                    )
                    index = collapse_end + 1  # Skip processed lines
                    continue  # Skip to the next loop iteration

            # Process single line (non-collapsible or within limit)
            self.append_lines(index, index + 1, lines, results, body_html, minimap_html, optimal_width)

            # Add current line to the appropriate quicklinks
            html_class = self.get_class(current_line_number, results)
            self.add_line_to_quicklinks(current_line_number, html_class, quicklink_info)

            # Move to the next line
            index += 1

        # Create quicklinks and add spacing
        quicklinks_html = self.create_quicklinks(quicklink_info)
        self.add_spacings(body_html, minimap_html)

        # Return the finalized HTML components
        return "".join(body_html), "".join(minimap_html), quicklinks_html

    def add_lines_to_quicklinks(self, start_index, end_index, results, quicklink_info):
        """
        Add a range of lines to the quicklinks mapping.

        Parameters:
            start_index (int): The starting index of the range (inclusive, 0-based).
            end_index (int): The ending index of the range (inclusive, 0-based).
            results (dict): The anomaly detection results.
            quicklink_info (dict): A dictionary containing quicklink types and their associated data.
        """
        for line_index in range(start_index, end_index + 1):
            line_number = line_index + 1  # 1-based line number
            html_class = self.get_class(line_number, results)
            self.add_line_to_quicklinks(line_number, html_class, quicklink_info)

    @staticmethod
    def add_line_to_quicklinks(line_number, html_class, quicklink_info):
        """
        Map a single line number to the appropriate quicklink type.

        Parameters:
            line_number (int): The line number to be added to the quicklinks.
            html_class (str): The CSS class associated with the line.
            quicklink_info (dict): A dictionary containing quicklink types and their associated data.

        """
        for link_type, link_data in quicklink_info.items():
            if link_type in html_class:
                link_data["lines"].append(line_number)

    def generate_compressed_html(
        self,
        css_styles,
        css_styles_embed,
        content_body,
        minimap_content,
        quicklinks_content,
        js_scripts,
        llm_response,
        root_cause_line,
    ):
        """
        Generate a compressed HTML file with embedded styles and scripts.

        Parameters:
            css_styles (str): The CSS styles for the HTML file.
            css_styles_embed (str): The embedded CSS styles for the HTML file, e.g., inline fonts, svgs, etc.
            content_body (str): The main content body of the HTML file.
            minimap_content (str): The minimap content of the HTML file.
            quicklinks_content (str): The quicklinks content of the HTML file.
            js_scripts (str): The JavaScript scripts for the HTML file.
            llm_response (str): The LLM response content for the HTML file.
            root_cause_line (str): The root cause line content for the HTML file.

        Returns:
            str: The compressed HTML content.
        """
        decompression_script_template = "".join(self.files.get_lines(str(self.TEMPLATE_GZIP), False))
        html_content = self.generate_html(
            css_styles,
            css_styles_embed,
            content_body,
            minimap_content,
            quicklinks_content,
            js_scripts,
            llm_response,
            root_cause_line,
        )
        compressed_html_content = gzip.compress(html_content.encode("utf-8"))
        base64_compressed_html = base64.b64encode(compressed_html_content).decode("utf-8")
        return minify(
            decompression_script_template.format(title=self.input, compressed=base64_compressed_html),
            minify_css=True,
            minify_js=True,
        )

    def generate_html(
        self,
        css_styles,
        css_styles_embed,
        content_body,
        minimap_content,
        quicklinks_content,
        js_scripts,
        llm_response,
        root_cause_line,
    ):
        """
        Generate a standard HTML file with embedded styles and scripts.

        Parameters:
            css_styles (str): The CSS styles for the HTML file.
            css_styles_embed (str): The embedded CSS styles for the HTML file, e,g, inline fonts, svgs etc.
            content_body (str): The main content body of the HTML file.
            minimap_content (str): The minimap content of the HTML file.
            quicklinks_content (str): The quicklinks content of the HTML file.
            js_scripts (str): The JavaScript scripts for the HTML file.
            llm_response (str): The LLM response content for the HTML file.
            root_cause_line (str): The root cause line content for the HTML file.

        Returns:
            str: The HTML content.
        """
        if not llm_response:
            llm_response = self.GENERIC_RESPONSE
        html_template = "".join(self.files.get_lines(str(self.TEMPLATE), False))
        llm_response = self.convert_to_html_paragraphs(llm_response)
        self.logger.debug(f"{self.NAME}: Generating and compressing HTML, CSS and JavaScript of visualisation ...")
        return minify(
            html_template.format(
                page_title=self.input,
                styles=css_styles,
                styles_embed=css_styles_embed,
                scripts=js_scripts,
                content=content_body,
                minimap=minimap_content,
                quicklinks=quicklinks_content,
                root_cause_line=root_cause_line,
                llm_response=llm_response,
            ),
            minify_css=True,
            minify_js=True,
        )

    def process_llm_response(self):
        """
        Process the LLM response to extract HTML content and root cause line number.

        This method reads the LLM response from a file if provided, converts the response
        to HTML paragraphs, and extracts the root cause line number if present.

        Returns:
            tuple: A tuple containing:
                - llm_response_hml (str or None): The HTML representation of the LLM response.
                - root_cause_list (int or None): The root cause list, otherwise None.
        """
        llm_response_hml = None
        root_cause_list = []
        if self.llm_response_file:
            llm_response = self.get_llm_response(self.llm_response_file)
            if isinstance(llm_response, list):
                llm_response = "".join(llm_response)
            lines = [line for line in llm_response.split("\n") if line.strip()]
            if lines:
                root_causes = []
                for line in lines:
                    if self.ROOT_CAUSE_CAPTION in line:
                        root_causes = re.findall(r"\d+", line)
                        break
                root_cause_list = [int(num) for num in root_causes]
            llm_response_hml = self.convert_to_html_paragraphs(llm_response)
        return llm_response_hml, root_cause_list

    def generate_visualization(
        self,
        compact: bool = True,
        compact_from: int = 2000,
        compact_max_normal_lines: int = 10,
        compact_context_lines: int = 5,
    ):
        """
        Generate the HTML visualization for the log file based on the provided arguments.

        Parameters:
            compact (bool): Whether to enable collapsing of normal lines.
            compact_from (int): The number of lines after which to start compacting.
            compact_max_normal_lines (int): The maximum number of consecutive normal lines to display fully.
            compact_context_lines (int): The number of context lines to display around collapsed normal lines.

        Returns:
            str: The generated HTML content.
        """
        self.logger.debug(f"{self.NAME}: Generating visualization for: {self.input} ...")
        first_root_cause = 0
        log_lines = self.files.get_lines(self.input)
        anomaly_results = self.get_results()
        css_styles = "".join(self.files.get_lines(str(self.STYLES), False))
        css_styles_embed = "".join(self.files.get_lines(str(self.STYLES_EMBED), False))
        js_scripts = "".join(self.files.get_lines(str(self.JS), False))
        llm_response_hml, root_cause_lines = self.process_llm_response()
        if compact and len(log_lines) > compact_from:
            compact = True
        else:
            compact = False
        content_body, minimap_content, quicklinks_content = self.get_main_divs(
            log_lines,
            anomaly_results,
            root_cause_lines,
            enable_collapse=compact,
            max_normal_lines=compact_max_normal_lines,
            context_lines=compact_context_lines,
        )
        minimap_content = '<div class="three-dots-loader"><div></div><div></div><div></div></div>'

        if root_cause_lines:
            first_root_cause = root_cause_lines[0]
        if self.gzip:
            html_output = self.generate_compressed_html(
                css_styles,
                css_styles_embed,
                content_body,
                minimap_content,
                quicklinks_content,
                js_scripts,
                llm_response_hml,
                first_root_cause,
            )
        else:
            html_output = self.generate_html(
                css_styles,
                css_styles_embed,
                content_body,
                minimap_content,
                quicklinks_content,
                js_scripts,
                llm_response_hml,
                first_root_cause,
            )
        html_output = f"{self.HTML_COMMENT_TITLE.format(file=self.input)}\n{html_output}"
        return html_output


def gen_visualisation(
    input_file,
    results_file,
    output,
    compact=None,
    compact_from=None,
    compact_max_normal_lines=None,
    compact_context_lines=None,
    llm_response_file=None,
    gzip=True,
    verbose=False,
    logger=None,
):

    # Ensure default values and correct types for visualization arguments
    if compact is None:
        compact = True
    if compact_from is None:
        compact_from = Visualiser.DEFAULT_COMPACT_FROM
    if compact_max_normal_lines is None:
        compact_max_normal_lines = Visualiser.DEFAULT_COMPACT_MAX_NORMAL_LINES
    if compact_context_lines is None:
        compact_context_lines = Visualiser.DEFAULT_COMPACT_CONTEXT_LINES

    visualizer = Visualiser(
        input_file=input_file,
        results_file=results_file,
        output=output,
        llm_response_file=llm_response_file,
        gzip=gzip,
        verbose=verbose,
        logger=logger,
    )
    visualizer.logger.debug(f"{NAME}: --- Started generating visualisation ...")
    html_output = None
    try:
        html_output = visualizer.generate_visualization(compact, compact_from, compact_max_normal_lines, compact_context_lines)
        if not output:
            print(html_output)
        else:
            visualizer.files.write_file(html_output, str(output))
    except Exception as e:
        visualizer.logger.error(f"{NAME} An error occurred: {e}, {traceback.format_exc()}")
    visualizer.logger.info(f"{NAME}: <<< Generating visualisation: COMPLETED")
    return html_output
