<script type="application/javascript">
(async function (base64) {{
const byteCharacters = atob(base64);
const byteNumbers = new Array(byteCharacters.length);
for (let i = 0; i < byteCharacters.length; i++) {{byteNumbers[i] = byteCharacters.charCodeAt(i);}}
const byteArray = new Uint8Array(byteNumbers);
const blob = new Blob([byteArray], {{ type: "application/gzip" }});
const ds = new DecompressionStream("gzip");
const decompressedStream = blob.stream().pipeThrough(ds);
const blobData = await new Response(decompressedStream).blob();
const reader = new FileReader();
reader.onload = function () {{
const decompressedHTML = reader.result;
document.write(decompressedHTML);
document.close();
const scripts = document.querySelectorAll("script");
scripts.forEach(script => {{const newScript = document.createElement("script");
if (script.src) {{newScript.src = script.src;}} else {{newScript.textContent = script.textContent;}}document.head.appendChild(newScript);}});}};reader.readAsText(blobData);
}})('{compressed}');
</script>
