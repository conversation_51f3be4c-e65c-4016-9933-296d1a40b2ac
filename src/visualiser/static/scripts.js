/* We need to wait for all the DOM content to load to safely operate on elements... */
document.addEventListener('DOMContentLoaded', () => {

    /* --- Minimap, Scroll Indicator and line navigation logic --- */
    let retryCount = 0;
    const retryLimit = 10;
    const retryInterval = 1000;
    const intervalId = setInterval(() => {
        const minimapContainer = document.querySelector('.minimap-container');

        if (minimapContainer !== null) {
            clearInterval(intervalId);

            // Clear the content of the minimap container
            minimapContainer.innerHTML = '';

            // Create a canvas element for the minimap (it's faster and more accurate than previous minimap version)
            const canvas = document.createElement('canvas');
            canvas.classList.add('minimap-canvas');
            minimapContainer.appendChild(canvas);
            const ctx = canvas.getContext('2d');

            const updateCanvas = () => {
                const docHeight = document.documentElement.scrollHeight;
                const winHeight = window.innerHeight;
                const minimapHeight = minimapContainer.clientHeight;
                const minimapWidth = minimapContainer.clientWidth;

                canvas.width = minimapWidth;
                canvas.height = minimapHeight;

                // Clear the canvas
                ctx.clearRect(0, 0, minimapWidth, minimapHeight);

                // Draw the minimap content
                const elements = document.querySelectorAll('.content div');
                elements.forEach(element => {
                    const rect = element.getBoundingClientRect();
                    const top = (rect.top + window.scrollY) / docHeight * minimapHeight;
                    const height = rect.height / docHeight * minimapHeight;

                    if (element.classList.contains('error')) {
                        ctx.fillStyle = 'rgba(255, 0, 0, 1)'; // Bright red
                    } else if (element.classList.contains('hint')) {
                        ctx.fillStyle = 'rgba(0, 191, 255, 1)'; // Bright blue
                    } else if (element.classList.contains('success')) {
                        ctx.fillStyle = 'rgba(0, 255, 0, 1)'; // Bright green
                    } else if (element.classList.contains('rootcause')) {
                        ctx.fillStyle = 'rgba(255, 255, 0, 1)'; // Bright yellow
                    } else {
                        ctx.fillStyle = 'rgba(97, 94, 91, 0.1)'; // Default gray
                    }

                    ctx.fillRect(0, top, minimapWidth, height);
                });

                // Draw the scroll indicator
                const scrollPos = window.scrollY || document.documentElement.scrollTop;
                const indicatorHeight = winHeight / docHeight * minimapHeight;
                const indicatorPos = scrollPos / docHeight * minimapHeight;

                ctx.fillStyle = 'rgba(255, 204, 0, 0.6)'; // Brighter scroll indicator
                ctx.fillRect(0, indicatorPos, minimapWidth, indicatorHeight);
            };

            const onMinimapClick = (event) => {
                const minimapHeight = minimapContainer.clientHeight;
                const clickY = event.offsetY;
                const docHeight = document.documentElement.scrollHeight;
                const targetScroll = (clickY / minimapHeight) * docHeight;
                window.scrollTo(0, targetScroll);
            };

            let isDragging = false;

            const onMinimapMouseDown = (event) => {
                isDragging = true;
                onMinimapClick(event);
            };

            const onMinimapMouseMove = (event) => {
                if (isDragging) {
                    onMinimapClick(event);
                }
            };

            const onMinimapMouseUp = () => {
                isDragging = false;
            };

            updateCanvas();
            window.addEventListener('scroll', updateCanvas, { passive: true });
            window.addEventListener('resize', updateCanvas, { passive: true });
            canvas.addEventListener('mousedown', onMinimapMouseDown);
            canvas.addEventListener('mousemove', onMinimapMouseMove);
            canvas.addEventListener('mouseup', onMinimapMouseUp);
            canvas.addEventListener('mouseleave', onMinimapMouseUp);

        } else if (retryCount >= retryLimit) {
            clearInterval(intervalId);
            console.error("Document content and minimap container was not found, indication that the document was not loaded completely. Aborting...");
        } else {
            retryCount++;
            console.warn(`Document content not yet loaded, waiting and retrying ${retryCount} time ...`);
        }
    }, retryInterval);


    /* --- Tooltip logic --- */

    const tooltipHoverDelay = 300; /* Delay (in milliseconds) before showing the tooltip on hover */
    let hoverTimer; /* Timer for managing the hover delay */

    /* Close all tooltips by setting their display style to 'none' */
    const closeAllTooltips = () => {
        const allTooltips = document.querySelectorAll('.tooltip-container');
        allTooltips.forEach(tooltip => {
            tooltip.style.display = 'none';
        });
    };

    /* Show the tooltip associated with the hovered element after a delay */
    const showTooltip = (event) => {
        const rootCauseDiv = event.currentTarget;
        hoverTimer = setTimeout(() => {
            closeAllTooltips();
            const tooltipContainer = rootCauseDiv.querySelector('.tooltip-container');
            if (tooltipContainer) {
                tooltipContainer.style.display = 'block';
            }
        }, tooltipHoverDelay);
    };

    /* Show the tooltip associated with a specific element by its ID */
    const showTooltipByID = (id) => {
        const rootCauseDiv = document.querySelector(`#${id}`);
        if (rootCauseDiv) {
            closeAllTooltips();
            const tooltipContainer = rootCauseDiv.querySelector('.tooltip-container');
            if (tooltipContainer) {
                tooltipContainer.style.display = 'block';
            }
        }
        if (id === "MainRootCause") {
            window.scrollTo(0,rootCauseDiv.offsetTop);
        }
    };

    /* Cancel the hover timer to prevent showing the tooltip */
    const cancelHover = () => {
        clearTimeout(hoverTimer);
    };

    /* Hide the tooltip when the associated button is clicked */
    const hideTooltip = (event) => {
        const button = event.currentTarget;
        const tooltipContainer = button.closest('.tooltip-container');
        if (tooltipContainer) {
            tooltipContainer.style.display = 'none';
        }
    };

    const tooltips = document.querySelectorAll('.tooltip');
    tooltips.forEach(tooltip => {
        tooltip.addEventListener('mouseenter', showTooltip);
        tooltip.addEventListener('mouseleave', cancelHover);

        const okButton = tooltip.querySelector('.button');
        if (okButton) {
            okButton.addEventListener('click', hideTooltip);
        }
    });

    /* Add a click listener to a div element to show a specific tooltip by its ID */
    const addClickListenerToDiv = (divId, tooltipId) => {
        const div = document.querySelector(`#${divId}`);
        if (div) {
            div.addEventListener('click', () => {
                showTooltipByID(tooltipId);
            });
        }
    };

    /* Check if any root cause tooltips exist and set the ID of the first one to 'MainRootCause' */
    const existsRootCause = () => {
        const rootCauseDivs = document.querySelectorAll('div.rootcause.tooltip');
        if (rootCauseDivs.length > 0) {
            const firstRootCauseDiv = rootCauseDivs[0];
            firstRootCauseDiv.id = 'MainRootCause';
            return true;
        }
        return false;
    };

    if (existsRootCause()) {
        addClickListenerToDiv('AnalysisResult', 'MainRootCause');
    } else {
        addClickListenerToDiv('AnalysisResult', 'DefaultRootCause');
    }
    addClickListenerToDiv('ChatWithMe', 'KnutGPTChat');

    /* Close button */
    const monitorCloseButton = () => {
        document.querySelectorAll('.close-button').forEach(button => {
            button.addEventListener('click', function() {
                let thirdParent = this.parentElement;
                if (thirdParent) {
                    thirdParent.style.display = 'none';
                }
            });
        });
    };
    monitorCloseButton();

    /* Root cause line auto navigation  */
    const handleRootCauseLine = () => {
        const rootCauseLineInput = document.getElementById('RootCauseLine');
        if (rootCauseLineInput) {
            const rootCauseLineValue = parseInt(rootCauseLineInput.value, 10);
            if (rootCauseLineValue !== 0) {
                const tooltip = document.querySelector('#DefaultRootCause .tooltip');
                if (tooltip) {
                    const feedbackDiv = tooltip.querySelector('.feedback.hidden');
                    if (feedbackDiv) {
                        feedbackDiv.classList.remove('hidden');
                    }
                }
                const targetLine = document.getElementById(`n${rootCauseLineValue}`);
                if (targetLine) {
                    targetLine.scrollIntoView();
                }
                const defaultTooltip = document.getElementById('DefaultRootCauseTooltip');
                if (defaultTooltip) {
                    const tooltipContainer = defaultTooltip.closest('.tooltip').querySelector('.tooltip-container');
                    if (tooltipContainer) {
                        tooltipContainer.style.display = 'block';
                    }
                }
            }
        }
    };
    handleRootCauseLine();
});
