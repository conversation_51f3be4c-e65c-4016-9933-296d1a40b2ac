<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>{page_title}</title>
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; style-src 'self' 'unsafe-inline'; script-src 'self' 'unsafe-inline'; font-src 'self' data:; img-src 'self' data:;">
    <!-- Stylesheets embedded in the html file  -->
    <style>{styles}{styles_embed}</style>
</head>
<body>
<div class="content">
    <!-- Default Root Cause div -->
    <div id="DefaultRootCause" class="invisible">
        <input type="hidden" id="RootCauseLine" value="{root_cause_line}"/>
        <div class="tooltip">
            <div id="DefaultRootCauseTooltip"></div>
            <div></div>
            <div class="tooltip-container">
                <div class="close-button"><span class="material-symbols-outlined">close</span></div>
                <div class="scroller tooltiptext">{llm_response}</div>
                <div class="button">Ok</div>
                <div class="feedback hidden">
                    <span class="material-symbols-outlined">thumb_up</span>
                    <span class="material-symbols-outlined">thumb_down</span>
                </div>
                <a href="https://knutgpt.bosch.com/" class="knutgpt_url_link" target="_blank">knutgpt.bosch.com</a>

            </div>
        </div>
    </div>
    <!-- KnutGPT Chat div, maybe a future feature ... -->
    <div id="KnutGPTChat" class="invisible">
        <div class="tooltip">
            <div id="KnutGPTChatooltip"></div>
            <div></div>
            <div class="tooltip-container">
                <div class="close-button"><span class="material-symbols-outlined">close</span></div>
                <div class="scroller tooltiptext">
                    <p>In the near future, we anticipate the introduction of a live chat feature. Imagine being able to engage in real-time conversations with KnutGPT AI companion!
                        Once this feature is implemented and released, you’ll have the opportunity to try it out.
                    </p>
                    <p>What you can expect from this exciting possible addition?
                    </p>
                    <p>Technical Assistance when you need help with debugging or further lines analysis when you deep dive into debugging of the analysis file. Simply copy-paste
                        relevant lines from the logs analysis, and I’ll do my best to assist you. My expertise lies primarily in technical matters, so feel free to challenge me
                        with any coding or troubleshooting questions. While my focus is technical, we’re not limited to just that. We can discuss a wide range of topics, whether
                        it’s technology, hobbies, latest movie releases, or you just want me to cheer you up by telling you a joke. Feel free to steer the conversation wherever
                        you’d like but keep in mind I'm here for technical discussions and assistance!
                    </p>
                    <p>Keep an eye on the official KnutGPT page and announcements for updates. As this feature evolves, we’ll share more details and provide instructions on how to
                        access it. But remember, this feature is a possibility on the horizon, so stay tuned!
                    </p>
                </div>
                <div class="button">Exiting, I'm looking forward to this!</div>
                <div class="feedback hidden">
                    <span class="material-symbols-outlined">thumb_up</span>
                    <span class="material-symbols-outlined">thumb_down</span>
                </div>
                <a href="https://knutgpt.bosch.com/" class="knutgpt_url_link" target="_blank">knutgpt.bosch.com</a>
            </div>
        </div>
    </div>
    <!-- Content -->
    {content}
</div>
<div class="minimap-container">{minimap}</div>
<div class="quicklinks-container">{quicklinks}</div>
<!-- Knut Bot Icon -->
<div class="knut-bot-container">
    <div class="knut-tools" id="AnalysisResult"><span class="material-symbols-outlined">troubleshoot</span></div>
    <div class="knut-tools" id="ChatWithMe"><span class="material-symbols-outlined">chat</span></div>
    <div id="Knut"></div>
</div>
<!-- Javascript ... -->
<script>{scripts}</script>
</body>
</html>
