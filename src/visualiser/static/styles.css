:root {
    --system-ui-font: "-apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', Ubuntu, Arial, sans-serif";
    --mono-font: "SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace";
    --icons-font: 'Material Symbols Outlined';
    --background-color: #2b2d30;
    --text-color: #ccc;

    --highlight-background: #000;
    --highlight-color: #ffcc00;
    --border-color: rgb(0, 0, 0);

    --error-background: #892e2e;
    --error-color: #ffffff;
    --rootcause-background: #eeff00;
    --rootcause-color: #ff0000;
    --hint-background: #1b5365;
    --hint-color: #ddf3f7;
    --success-background: #336533;
    --success-color: #cde6af;

    --quicklink-background: rgba(0, 0, 0, 0.8);
    --quicklink-hover-background: #2b2d30;
    --quicklink-hover-color: #ffea83;

    --tooltip-background: #fff7e6;
    --tooltip-color: #2b2d30;
    --tooltip-border: #000;
    --tooltip-shadow: #2b2d30;
    --icon-hover-color: #0063c5;

    --scroll-indicator-background: rgba(255, 255, 0, 0.5);
    --minimap-background: rgba(0, 0, 0, 0.5);

    --knut-tools-background: rgba(43, 45, 48, 0.5);
    --knut-tools-hover-background: rgba(73, 67, 66, 0.9);

    --margin-small: 0.3em;
    --margin-medium: 1.5em;
    --padding-small: 0.5em;
    --padding-medium: 1em;
    --padding-large: 3em;
    --transition-duration: 0.1s;

    --quicklinks-rootcause-background: rgba(173, 173, 24, 0.95);
    --quicklinks-rootcause-color: #fff3bd;
    --quicklinks-error-background: rgba(135, 19, 19, 0.95);
    --quicklinks-error-color: #e1acac;
    --quicklinks-hint-background: rgba(37, 105, 131, 0.95);
    --quicklinks-hint-color: #32c9ff;
    --quicklinks-success-background: rgba(53, 115, 53, 0.95);
    --quicklinks-success-color: #43d343;
}

* {
    margin: 0;
    padding: 0;
    border: 0;
    font-size: 1em;
}

::selection {
    background-color: var(--highlight-background);
    color: var(--highlight-color);
}

::-webkit-selection {
    background-color: var(--highlight-background);
    color: var(--highlight-color);
}

::-moz-selection {
    background-color: var(--highlight-background);
    color: var(--highlight-color);
}

body {
    border: 0;
    border-top: 3px solid var(--border-color);
    border-bottom: 3px solid var(--border-color);
    background: var(--background-color);
    color: var(--text-color);
    font-family: var(--mono-font), monospace;
    width: 100%;
    height: 100%;
    overflow: hidden;
    overflow-y: auto;
}

.invisible {
    position: fixed;
    top: -1000em;
    z-index: 10;
}

.unselectable {
    user-select: none;
}

.content {
    width: auto;
    height: auto;
    overflow: scroll;
    margin-left: 8em;
}

.content div {
    width: auto;
    display: flex;
    clear: both;
    margin-top: 1px;
    background: transparent;
    color: var(--text-color);
    white-space: nowrap;
    min-height: 1em;
}

.content div div:first-child {
    min-width: 5em;
    display: inline-block;
    text-align: right;
    padding-right: 10px;
}

.content div div:nth-child(2) {
    display: inline-block;
    word-wrap: break-word;
    text-wrap: nowrap;
    text-align: left;
}

.minimap-container div.error,
.content div.error,
.content div.error div:first-child,
.content div.error div:nth-child(2) {
    background: var(--error-background);
    color: var(--error-color);
}

.minimap-container div.rootcause,
.content div.rootcause,
.content div.rootcause div:first-child,
.content div.rootcause div:nth-child(2) {
    background: var(--rootcause-background);
    color: var(--rootcause-color);
}

.minimap-container div.hint,
.content div.hint,
.content div.hint div:first-child,
.content div.hint div:nth-child(2) {
    background: var(--hint-background);
    color: var(--hint-color);
}

.minimap-container div.success,
.content div.success,
.content div.success div:first-child,
.content div.success div:nth-child(2) {
    background: var(--success-background);
    color: var(--success-color);
}

.minimap-container {
    background-color: var(--minimap-background);
    position: fixed;
    top: 0;
    left: 0;
    width: 8em;
    display: flex;
    flex-direction: column;
    height: 100vh;
    border-right: 2px solid var(--border-color);
    overflow: hidden;
    margin: 0;
    padding: 0;
}

.minimap-container div {
    background-color: #615e5b;
    flex-grow: 0.1;
    flex-basis: 0;
    align-items: center;
    justify-content: center;
    border: 0;
}

.minimap-container .error::before,
.minimap-container div.error {
    background: #e40000;
}

.minimap-container .hint::before,
.minimap-container div.hint {
    background: #0095c5;
}

.minimap-container .success::before,
.minimap-container div.success {
    background: #009300;
}

.minimap-container .rootcause::before,
.minimap-container div.rootcause {
    background: var(--highlight-color);
}

.minimap-container .error::before,
.minimap-container .hint::before,
.minimap-container .success::before,
.minimap-container .rootcause::before {
    content: '';
    position: absolute;
    display: inline-block;
    left: 0;
    right: 0;
    width: 100%;
    min-height: 1px !important;
    z-index: 1;
}

.quicklinks-container {
    position: fixed;
    top: var(--margin-medium);
    right: var(--margin-medium);
    width: 18em;
    display: flex;
    flex-direction: column;
    height: auto;
    font-family: var(--system-ui-font), system-ui;
}

.quicklinks-container .title {
    opacity: 0.5;
    position: absolute;
    left: -1.5em;
    margin-top: -0.5em;
}

.quicklinks-container .title .material-symbols-outlined {
    font-size: 1.5em;
    cursor: pointer;
}

.quicklinks-container a,
.quicklinks-container a:visited {
    text-decoration: none;
    padding: 0 0.3em;
}

.quicklinks-container a:hover {
    background-color: var(--quicklink-hover-background);
    color: var(--quicklink-hover-color);
    text-decoration: none;
}

.quicklink {
    margin: var(--margin-medium) var(--margin-small) var(--margin-small);
    padding: var(--padding-small);
}

.quicklink:hover .heading {
    opacity: 1;
    transition: all var(--transition-duration) ease-in-out;
}

.quicklink .heading {
    position: absolute;
    margin: -1.8em 0 0 -0.5em;
    font-weight: 600;
    background: var(--quicklink-background);
    padding: 0 0.5em;
    transition: all var(--transition-duration) ease-in-out;
    opacity: 0;
}

.quicklink p {
    max-height: 12em;
    overflow-y: auto;
}


.quicklinks-rootcause {
    background: var(--quicklinks-rootcause-background);
}

.quicklinks-rootcause .heading {
    background: var(--quicklinks-rootcause-background);
    color: var(--quicklinks-rootcause-color);
}

.quicklinks-error .heading {
    background: var(--quicklinks-error-background);
    color: var(--quicklinks-error-color);
}

.quicklinks-hint .heading {
    background: var(--quicklinks-hint-background);
    color: var(--quicklinks-hint-color);
}

.quicklinks-success .heading {
    background: var(--quicklinks-success-background);
    color: var(--quicklinks-success-color);
}

.quicklinks-rootcause .title {
    color: #ffff2b;
}

.quicklinks-rootcause a {
    color: var(--quicklinks-rootcause-color);
}

.quicklinks-error {
    background: var(--quicklinks-error-background);
}

.quicklinks-error .title {
    color: #ff0000;
}

.quicklinks-error a {
    color: var(--quicklinks-error-color);
}

.quicklinks-hint {
    background: var(--quicklinks-hint-background);
}

.quicklinks-hint .title {
    color: var(--quicklinks-hint-color);
}

.quicklinks-hint a {
    color: #8addff;
}

.quicklinks-success {
    background: var(--quicklinks-success-background);
}

.quicklinks-success .title {
    color: var(--quicklinks-success-color);
}

.quicklinks-success a {
    color: #95ed95;
}

.minimap-container .scroll-indicator {
    position: absolute;
    min-height: 10px;
    width: 100%;
    cursor: grab !important;
    transition: none !important;
    background-color: var(--scroll-indicator-background) !important;
    border: 0 !important;
    z-index: 2;
}

.content div.tooltip.rootcause {
    border-bottom: 1px dotted black;
}

.hidden {
    visibility: hidden;
    display: initial;
}

.content div.tooltip .tooltip-container {
    z-index: 10;
    border: 1px solid var(--tooltip-border);
    background: var(--tooltip-background);
    font-family: var(--system-ui-font), system-ui;
    box-shadow: 5px 5px 15px var(--tooltip-shadow);
    padding: 3em 2em 2em;
    display: none;
    position: fixed;
    bottom: 1.2em;
    left: 9em;
    right: 20em;
    min-width: 12em;
}

.content div.tooltip .tooltip-container .tooltiptext {
    background-color: var(--tooltip-background);
    color: var(--tooltip-color);
    text-align: left;
    white-space: normal;
    width: 100%;
    margin: 0;
    padding: 0;
    max-height: 20em;
    overflow: auto;
    min-width: 10em;
}

.tooltiptext p {
    margin-bottom: 1em;
}

.content div.tooltip .tooltip-container:after {
    content: "";
    border-top: 3.5em solid var(--tooltip-background);
    border-left: 0em solid #0000;
    border-right: 12.2em solid #0000;
    width: 0;
    height: 0;
    margin: 0;
    position: absolute;
    bottom: .5em;
    left: auto;
    right: -12em;
}

.content div.tooltip .tooltip-container a.knutgpt_url_link {
    position: absolute;
    text-align: left;
    top: 1.2em;
    left: 2.5em;
    color: #bfbebe;
    font-size: 0.8em;
    text-decoration: none;
    float: right;
    transition: all .1s ease-out;
}

.content div.tooltip .tooltip-container a.knutgpt_url_link:hover {
    color: #0063c5;
    letter-spacing: .1em;
    transition: all .1s ease-out;
}

.content div.tooltip .tooltip-container .button {
    background: var(--hint-background);
    padding: var(--padding-medium) var(--padding-large);
    float: left;
    margin: 0;
    border-radius: 5em;
    color: #fff;
    font-weight: 600;
    transition: background-color 0.3s ease;
    cursor: pointer;
    user-select: none;
}

.content div.tooltip .tooltip-container .button:hover {
    background: #0063c5;
}

.content div.tooltip .tooltip-container .feedback {
    position: absolute;
    bottom: 2em;
    right: 2em;
}

.hidden {
    display: none;
}

.content div.tooltip .tooltip-container .material-symbols-outlined {
    cursor: pointer;
    font-size: 1.2em;
    padding: 0.1em;
    transition: all 0.3s ease;
}

.content div.tooltip .tooltip-container .material-symbols-outlined:hover {
    transform: scale(1.2);
    color: var(--icon-hover-color);
}


.content div.tooltip .tooltip-container .close-button {
    position: absolute;
    top: .3em;
    right: 0em;
    font-size: 1.5em;
}


#MainRootCause {
    color: var(--quicklinks-rootcause-color);
}

#MainRootCause #n0 .material-symbols-outlined {
    font-size: 1em;
}

#ChatWithMe {
    left: 0.3em;
    top: 4em;
}

#AnalysisResult {
    left: 0.8em;
    top: 1em;
}

.knut-tools:hover::before {
    content: "Analysis Result";
    position: fixed;
    right: 1em;
    bottom: .5em;
    font-family: var(--system-ui-font), system-ui;
    font-size: 0.8em;
    color: #fff;
    padding: .2em .5em;
    background: var(--knut-tools-background);
    border-radius: 0.5em;
    text-align: center;
    min-width: 8em;
}

#AnalysisResult:hover::before {
    content: "Analysis Result";
}

#ChatWithMe:hover::before {
    content: "Chat with me";
}

.knut-bot-container {
    background: transparent;
    position: fixed;
    right: 0;
    bottom: 0;
    padding: 2em 2em 1em 3em;
    border-radius: 100em;
    z-index: 20;
}

.knut-bot-container:hover .knut-tools {
    opacity: 1;
}

.knut-tools {
    position: absolute;
    cursor: pointer;
    border-radius: 10em;
    padding: var(--padding-small);
    background: var(--knut-tools-background);
    height: 1.5em;
    width: 1.5em;
    transition: all var(--transition-duration) ease-in-out;
    user-select: none;
    z-index: 10;
    opacity: 0.5;
}

.knut-tools .material-symbols-outlined {
    font-size: 1.5em;
    color: #fff;
    transition: all var(--transition-duration) ease-in-out;
}

.knut-tools:hover {
    transition: all var(--transition-duration) ease-in-out;
}

.knut-tools:hover .material-symbols-outlined {
    transform: scale(1.2);
    transition: all var(--transition-duration) ease-in-out;
}

.material-symbols, .material-symbols-outlined {
    font-family: var(--icons-font);
    font-weight: normal;
    font-style: normal;
    font-size: 24px;
    line-height: 1;
    letter-spacing: normal;
    text-transform: none;
    display: inline-block;
    white-space: nowrap;
    word-wrap: normal;
    direction: ltr;
    -webkit-font-smoothing: antialiased;
}

.content div.analysis-file {
    font-family: var(--system-ui-font), system-ui;
    background-color: #000;
    font-weight: bold;
    width: auto;
}

.scroller {
    scrollbar-gutter: stable both-edges;
    overflow-y: auto;
}

.scroller::-webkit-scrollbar {
    visibility: hidden;
    width: 10px;
    height: 12px;
}

.scroller:hover::-webkit-scrollbar {
    visibility: initial;
}

.scroller::-webkit-scrollbar-thumb {
    background-color: #2b2d30;
    border-radius: 6px;
    visibility: hidden;
}

.scroller:hover::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.7);
    visibility: initial;
}

.scroller::-webkit-scrollbar-track {
    opacity: 0;
    visibility: hidden;
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 6px;
}

.scroller:hover::-webkit-scrollbar-track {
    opacity: 1;
    visibility: initial;
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 6px;
}
