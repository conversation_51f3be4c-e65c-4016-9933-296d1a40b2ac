# Visualiser

*The primary goal of this tool is to generate a visual representation of log files.*

Visualiser is a library designed to generate a visual representation of log files. It can be used as a standalone command-line utility or integrated into other Python projects as a
library. The primary goal of this tool is to simplify the process of analyzing log files by highlighting anomalies and events such as root-causes, errors, hints or success.

Visualiser generates self-contained HTML files with embedded CSS and JavaScript, making the output portable and dependency-free. Simply input a log file, and the tool will generate
an HTML file that can be opened in any modern web browser for easy navigation and analysis of log lines.

## Features

- Visual representation of log files
- Highlighting of anomalies and events and generated quick links for the issues
- Generation of self-contained, portable HTML files
- Command-line utility and library usage

## Installation

- Python 3.11.7+ recommended.

This tool relies only on Python standard library modules. No additional dependencies are required. Simply clone the repository and run the main script.

## Usage

The main script can be run with the following command:

```console
visualiser.cmd <input> <results>  <options...> <-output,--output> <output.html>
```

Where:

- `input`: Path to the log file
- `results`: Path to the JSON file containing the anomaly detection results, or a one-line JSON string
- `-output, --output`: Path to the output HTML file. If not specified, the content is written to the console
- `-llmresponse, --llmresponse`, (optional) Path to the LLM response text file
- `-compact, --compact`, (optional) Compact/skipp consecutive lines that do not have any error or hint. If not specified, default is True, meaning all files with more than 2000 (or
  --compact_from value) lines will be compacted.
-compact_from value) lines will be compacted.
- `-compact_from, --compact_from`, (optional, fine-tuning) Compact log lines with greater number of lines than this. **Default: 2000.** Note that files with fewer lines than this
  will not be compacted even if --compact is specified.
- `-compact_max_normal_lines, --compact_max_normal_lines`, (optional, fine-tuning) Max consecutive normal lines shown without being compacted. Everything above this number gets
  compacted. **Default: 10**
- `-compact_context_lines, --compact_context_lines`, (optional, fine-tuning) Number of normal lines shown above and below critical lines. **Default: 5**
The input JSON file should be in the following format:

```json
{
  "file": "consoleText.txt",
  "exit_code": "None",
  "root_causes": [
    {
      "error_lines": [
        738,
        809,
        815,
        817
      ],
      "hint_lines": [
        784,
        785
      ]
    }
  ],
  "success_lines": [
    819
  ]
}

```

## Examples

Navigate to the `src` directory of the project folder before running the examples using `cd <repo_dir>/src`. 
If you run the examples from a different folder, you will need to adjust the paths accordingly.

For linux just use `sh ./run/linux/visualiser.sh` instead of `.\run\windows\visualiser.cmd`

```warning
MAKE SURE YOU ARE IN THE <repo_dir>/src DIRECTORY OF THE PROJECT BEFORE RUNNING THE COMMANDS
```

### Basic

Examples of how to use the log Visualiser with provided sample files:

```bash
 .\run\windows\visualiser.cmd ./samples/logfile_hotspot_chunk.txt ./samples/logfile_hotspot_chunk.json --verbose --output ./results_visualisation_example_1.html
```
```bash
 sh ./run/linux/visualiser.sh ./samples/logfile_hotspot_chunk.txt ./samples/logfile_hotspot_chunk.json --verbose --output ./results_visualisation_example_1.html
```

Additional basic examples of how to use the log Visualiser with provided sample files:

```bash
 .\run\windows\visualiser.cmd ./samples/consoleText.txt ./samples/consoleText.json --verbose --output ./results_visualisation_example_2.html
```
```bash
 sh ./run/linux/visualiser.sh ./samples/consoleText.txt ./samples/consoleText.json --verbose --output ./results_visualisation_example_2.html
```

### Include LLM response file for root causes

This will analyze the LLM response and include the root causes reported by the LLM in the visualisation.


```bash
 .\run\windows\visualiser.cmd ./samples/log-10002.txt ./samples/log-10002.json --llmresponse ./samples/llmresponse.txt --verbose --output ./results_visualisation_example_3.html
```
```bash
 sh ./run/linux/visualiser.sh ./samples/log-10002.txt ./samples/log-10002.json --llmresponse ./samples/llmresponse.txt --verbose --output ./results_visualisation_example_3.html
```

### Compact (hide-fold)

Compact normal non-critical lines with default values, starting for log files bigger than 2000 lines and will show 5 lines above and below.

```bash
 .\run\windows\visualiser.cmd  ./samples/consoleText.txt ./samples/consoleText.json --llmresponse ./samples/llmresponse2.txt --compact --verbose --output ./results_visualisation_example_4.html
```
```bash
 sh ./run/linux/visualiser.sh  ./samples/consoleText.txt ./samples/consoleText.json --llmresponse ./samples/llmresponse2.txt --compact --verbose --output ./results_visualisation_example_4.html
```
Compact normal non-critical lines with default values (**default: True**), starting for log files bigger than 2000 (**default: 2000**) lines and will show 5 lines (**default: 5**) above and below critical lines.

```bash
 .\run\windows\visualiser.cmd  ./samples/consoleText.txt ./samples/consoleText.json --llmresponse ./samples/llmresponse2.txt --compact --compact_from 200 --compact_context_lines 6 --verbose --output ./results_visualisation_example_5.html
```
```bash
 sh ./run/linux/visualiser.sh  ./samples/consoleText.txt ./samples/consoleText.json --llmresponse ./samples/llmresponse2.txt --compact --compact_from 200 --compact_context_lines 6 --verbose --output ./results_visualisation_example_5.html
```

### Console
Print the generated HTML directly to the console without saving it to a file:


```bash
 .\run\windows\visualiser.cmd ./samples/log-10002.txt ./samples/log-10002.json
```
```bash
 sh ./run/linux/visualiser.sh ./samples/log-10002.txt ./samples/log-10002.json
```

### Inline JSON

Inline JSON (note that `"` should be escaped as `\"` to meet shell requirements) with output to an HTML file:

```bash
 .\run\windows\visualiser.cmd ./samples/consoleText.txt '"{\"file\": \"consoleText.txt\", \"exit_code\": \"None\", \"root_causes\": [{\"error_lines\": [738, 809, 815, 817], \"hint_lines\": [784, 785]}], \"success_lines\": [819]}"' --verbose --output ./results_visualisation_example_6.html
```
```bash
 sh ./run/linux/visualiser.sh ./samples/consoleText.txt '"{\"file\": \"consoleText.txt\", \"exit_code\": \"None\", \"root_causes\": [{\"error_lines\": [738, 809, 815, 817], \"hint_lines\": [784, 785]}], \"success_lines\": [819]}"' --verbose --output ./results_visualisation_example_6.html
```

## Limitations

This tool has been tested with Mozilla Firefox and Microsoft Edge. Although it should function in any modern web browser (e.g., Google Chrome), additional testing may be required.

### Update history

| Date       | Author                  | Reference          |
|------------|-------------------------|--------------------|
| 2025-01-24 | Mihai-Ciprian Chezan    | CHM1LUD            |
