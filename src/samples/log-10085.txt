[2021-09-01T05:03:10.809Z] Triggered by G<PERSON><PERSON>: https://rbcm-gerrit.de.bosch.com/c/projects/hydra/config/autosar/stdcore/saic-dms-is31/+/338415
[2021-09-01T05:03:11.128Z] Checking out git ssh://rbcm-gerrit.de.bosch.com:29418/clusterbase/tools/jenkins-groovy-scripts into /var/jenkins_home/workspace/Git_platform_hydra_autosar_on-review@script to read hydra-review-autosar.groovy
[2021-09-01T05:03:11.130Z] The recommended git tool is: NONE
[2021-09-01T05:03:11.130Z] Warning: CredentialId "None" could not be found.
[2021-09-01T05:03:11.139Z]  > git rev-parse --resolve-git-dir /var/jenkins_home/workspace/Git_platform_hydra_autosar_on-review@script/.git # timeout=10
[2021-09-01T05:03:11.150Z] Fetching changes from the remote Git repository
[2021-09-01T05:03:11.150Z]  > git config remote.origin.url ssh://rbcm-gerrit.de.bosch.com:29418/clusterbase/tools/jenkins-groovy-scripts # timeout=10
[2021-09-01T05:03:11.164Z] Fetching upstream changes from ssh://rbcm-gerrit.de.bosch.com:29418/clusterbase/tools/jenkins-groovy-scripts
[2021-09-01T05:03:11.164Z]  > git --version # timeout=10
[2021-09-01T05:03:11.169Z]  > git --version # 'git version 2.20.1'
[2021-09-01T05:03:11.169Z]  > git fetch --tags --force --progress -- ssh://rbcm-gerrit.de.bosch.com:29418/clusterbase/tools/jenkins-groovy-scripts +refs/heads/*:refs/remotes/origin/* # timeout=20
[2021-09-01T05:03:11.381Z]  > git rev-parse fb6ca02cda5b46727c83a319b99a0e9969a51ece^{commit} # timeout=10
[2021-09-01T05:03:11.390Z] Checking out Revision fb6ca02cda5b46727c83a319b99a0e9969a51ece (detached)
[2021-09-01T05:03:11.390Z]  > git config core.sparsecheckout # timeout=10
[2021-09-01T05:03:11.395Z]  > git checkout -f fb6ca02cda5b46727c83a319b99a0e9969a51ece # timeout=20
[2021-09-01T05:03:11.408Z] Commit message: "Revert "On-review running in parallel""
[2021-09-01T05:03:11.408Z]  > git rev-list --no-walk fb6ca02cda5b46727c83a319b99a0e9969a51ece # timeout=10
[2021-09-01T05:03:11.514Z] Running in Durability level: MAX_SURVIVABILITY
[2021-09-01T05:03:11.551Z] Loading library build_utilities@master
[2021-09-01T05:03:11.551Z] Attempting to resolve master from remote references...
[2021-09-01T05:03:11.551Z]  > git --version # timeout=10
[2021-09-01T05:03:11.555Z]  > git --version # 'git version 2.20.1'
[2021-09-01T05:03:11.555Z] using GIT_ASKPASS to set credentials Git/Gerrit HTTP password, get from https://rbcm-gerrit.de.bosch.com/#/settings/http-password, updated on 2020-10-24
[2021-09-01T05:03:11.556Z]  > git ls-remote -h -- cm_gerrit:common/ebs2/jenkins_shared_library # timeout=10
[2021-09-01T05:03:11.675Z] Found match: refs/heads/master revision 4bad26b5b1874176fed9804c01a4d49e7da27109
[2021-09-01T05:03:11.687Z] Selected Git installation does not exist. Using Default
[2021-09-01T05:03:11.687Z] The recommended git tool is: NONE
[2021-09-01T05:03:11.687Z] using credential cca2lr_gerrit
[2021-09-01T05:03:11.701Z]  > git rev-parse --resolve-git-dir /var/jenkins_home/workspace/Git_platform_hydra_autosar_on-review@libs/build_utilities/.git # timeout=10
[2021-09-01T05:03:11.709Z] Fetching changes from the remote Git repository
[2021-09-01T05:03:11.709Z]  > git config remote.origin.url cm_gerrit:common/ebs2/jenkins_shared_library # timeout=10
[2021-09-01T05:03:11.714Z] Fetching without tags
[2021-09-01T05:03:11.714Z] Fetching upstream changes from cm_gerrit:common/ebs2/jenkins_shared_library
[2021-09-01T05:03:11.714Z]  > git --version # timeout=10
[2021-09-01T05:03:11.719Z]  > git --version # 'git version 2.20.1'
[2021-09-01T05:03:11.719Z] using GIT_ASKPASS to set credentials Git/Gerrit HTTP password, get from https://rbcm-gerrit.de.bosch.com/#/settings/http-password, updated on 2020-10-24
[2021-09-01T05:03:11.720Z]  > git fetch --no-tags --force --progress -- cm_gerrit:common/ebs2/jenkins_shared_library +refs/heads/*:refs/remotes/origin/* # timeout=10
[2021-09-01T05:03:11.870Z] Checking out Revision 4bad26b5b1874176fed9804c01a4d49e7da27109 (master)
[2021-09-01T05:03:11.870Z]  > git config core.sparsecheckout # timeout=10
[2021-09-01T05:03:11.896Z]  > git checkout -f 4bad26b5b1874176fed9804c01a4d49e7da27109 # timeout=10
[2021-09-01T05:03:11.926Z] Commit message: "Capture and log STDOUT+STDERR of typeperf process"
[2021-09-01T05:03:11.926Z]  > git rev-list --no-walk 4bad26b5b1874176fed9804c01a4d49e7da27109 # timeout=10
[2021-09-01T05:03:11.972Z] Loading library CmSharedLib@e5c2544a89197c56e4781dbcd813c967f6a95ea9
[2021-09-01T05:03:11.972Z] Attempting to resolve e5c2544a89197c56e4781dbcd813c967f6a95ea9 from remote references...
[2021-09-01T05:03:11.972Z]  > git --version # timeout=10
[2021-09-01T05:03:11.979Z]  > git --version # 'git version 2.20.1'
[2021-09-01T05:03:11.979Z] using GIT_ASKPASS to set credentials 
[2021-09-01T05:03:11.980Z]  > git ls-remote -h -- cm_gerrit:tools/android/jenkinssharedlib # timeout=10
[2021-09-01T05:03:12.171Z] Could not find e5c2544a89197c56e4781dbcd813c967f6a95ea9 in remote references. Pulling heads to local for deep search...
[2021-09-01T05:03:13.665Z]  > git rev-parse --resolve-git-dir /var/jenkins_home/caches/git-a718211dd47325254dbec456b983fcea/.git # timeout=10
[2021-09-01T05:03:13.671Z] Setting origin to cm_gerrit:tools/android/jenkinssharedlib
[2021-09-01T05:03:13.671Z]  > git config remote.origin.url cm_gerrit:tools/android/jenkinssharedlib # timeout=10
[2021-09-01T05:03:13.676Z] Fetching origin...
[2021-09-01T05:03:13.676Z] Fetching upstream changes from origin
[2021-09-01T05:03:13.676Z]  > git --version # timeout=10
[2021-09-01T05:03:13.681Z]  > git --version # 'git version 2.20.1'
[2021-09-01T05:03:13.681Z]  > git config --get remote.origin.url # timeout=10
[2021-09-01T05:03:13.687Z] using GIT_ASKPASS to set credentials 
[2021-09-01T05:03:13.687Z]  > git fetch --tags --force --progress -- origin +refs/heads/*:refs/remotes/origin/* # timeout=10
[2021-09-01T05:03:13.922Z]  > git rev-parse e5c2544a89197c56e4781dbcd813c967f6a95ea9^{commit} # timeout=10
[2021-09-01T05:03:13.927Z]  > git branch -a -v --no-abbrev --contains e5c2544a89197c56e4781dbcd813c967f6a95ea9 # timeout=10
[2021-09-01T05:03:13.935Z] Selected match: ccs2-biaas revision e5c2544a89197c56e4781dbcd813c967f6a95ea9
[2021-09-01T05:03:13.936Z] Selected Git installation does not exist. Using Default
[2021-09-01T05:03:13.936Z] The recommended git tool is: NONE
[2021-09-01T05:03:13.936Z] using credential fe8ff69c-5b69-4e49-8bab-4ba2f73c9d5a
[2021-09-01T05:03:13.947Z]  > git rev-parse --resolve-git-dir /var/jenkins_home/workspace/Git_platform_hydra_autosar_on-review@libs/CmSharedLib/.git # timeout=10
[2021-09-01T05:03:13.953Z] Fetching changes from the remote Git repository
[2021-09-01T05:03:13.953Z]  > git config remote.origin.url cm_gerrit:tools/android/jenkinssharedlib # timeout=10
[2021-09-01T05:03:13.959Z] Fetching without tags
[2021-09-01T05:03:13.959Z] Fetching upstream changes from cm_gerrit:tools/android/jenkinssharedlib
[2021-09-01T05:03:13.959Z]  > git --version # timeout=10
[2021-09-01T05:03:13.964Z]  > git --version # 'git version 2.20.1'
[2021-09-01T05:03:13.964Z] using GIT_ASKPASS to set credentials 
[2021-09-01T05:03:13.971Z]  > git fetch --no-tags --force --progress -- cm_gerrit:tools/android/jenkinssharedlib +refs/heads/*:refs/remotes/origin/* # timeout=10
[2021-09-01T05:03:14.228Z] Checking out Revision e5c2544a89197c56e4781dbcd813c967f6a95ea9 (ccs2-biaas)
[2021-09-01T05:03:14.228Z]  > git config core.sparsecheckout # timeout=10
[2021-09-01T05:03:14.233Z]  > git checkout -f e5c2544a89197c56e4781dbcd813c967f6a95ea9 # timeout=10
[2021-09-01T05:03:14.243Z] Commit message: "BUGFIX in rbflashdevice"
[2021-09-01T05:03:14.244Z]  > git rev-list --no-walk e5c2544a89197c56e4781dbcd813c967f6a95ea9 # timeout=10
[2021-09-01T05:03:14.837Z] [8mha:////4GvJbOio0+tOQMxwYjmUKk2e8HOKoDGhCaVlSUYD/YvXAAAAoh+LCAAAAAAAAP9tjTEOwjAQBM8BClpKHuFItIiK1krDC0x8GCfWnbEdkooX8TX+gCESFVvtrLSa5wtWKcKBo5UdUu8otU4GP9jS5Mixv3geZcdn2TIl9igbHBs2eJyx4YwwR1SwULBGaj0nRzbDRnX6rmuvydanHMu2V1A5c4MHCFXMWcf8hSnC9jqYxPTz/BXAFEIGsfuclm8zQVqFvQAAAA==[0m[Pipeline] Start of Pipeline
[2021-09-01T05:03:15.080Z] [8mha:////4EjvjNX+qtJPpbP3wdF6X5l7tjqaVkwxN/L1n8gcF89GAAAApR+LCAAAAAAAAP9tjTEOwjAUQ3+KOrAycohUghExsUZZOEFIQkgb/d8mKe3EibgadyBQiQlLlmxL1nu+oE4RjhQdby12HpP2vA+jK4lPFLtroIm3dOGaMFGwXNpJkrGnpUrKFhaxClYC1hZ1oOTRZdiIVt1VExS65pxj2Q4CKm8GeAAThZxVzN8yR9jeRpMIf5y/AJj7DGxXvP/86jduZBmjwAAAAA==[0m[Pipeline] node
[2021-09-01T05:03:30.089Z] Still waiting to schedule task
[2021-09-01T05:03:30.089Z] ‘[8mha:////4ITiKy5LGlcwBt4VMThlZ+hVFfrqmHWahMqMdLYAtX1UAAAAsR+LCAAAAAAAAP9b85aBtbiIQTGjNKU4P08vOT+vOD8nVc83PyU1x6OyILUoJzMv2y+/JJUBAhiZGBgqihhk0NSjKDWzXb3RdlLBUSYGJk8GtpzUvPSSDB8G5tKinBIGIZ+sxLJE/ZzEvHT94JKizLx0a6BxUmjGOUNodHsLgAypEgZV/eT83ILSktQifdeK1GRzA7P4YE/dKFc/dwOzcANDg3jzeFMTfQDo21nv2gAAAA==[0mExec706_SI-ZENG06W010_7_54’ is offline
[2021-09-01T05:08:43.686Z] Running on [8mha:////4LwFjsiprPg7g9G+6MsZtA6S/fAbNnXpUrRLrzWL3IQOAAAAsh+LCAAAAAAAAP9b85aBtbiIQTGjNKU4P08vOT+vOD8nVc83PyU1x6OyILUoJzMv2y+/JJUBAhiZGBgqihhk0NSjKDWzXb3RdlLBUSYGJk8GtpzUvPSSDB8G5tKinBIGIZ+sxLJE/ZzEvHT94JKizLx0a6BxUmjGOUNodHsLgAzpEgY1/eT83ILSktQifdeK1GRzA7P4YE/dKFc/dwOzcANDg3iTeENjE30AtkHAANsAAAA=[0mExec706_SI-ZENG06W010_4_134 in C:\workspace\workspace\Git_platform_hydra_autosar_on-review
[2021-09-01T05:08:43.695Z] [8mha:////4PvHl9am0M8rRRU36Ek8Oz3TTsX2mXw01hylxCFU1VzMAAAApR+LCAAAAAAAAP9tjTEOwjAUQ3+KOrAycoh0gA0xsUZZOEFIQkgb/d8mKe3EibgadyBQiQlLlmxL1nu+oE4RjhQdby12HpP2vA+jK4lPFLtroIm3dOGaMFGwXNpJkrGnpUrKFhaxClYC1hZ1oOTRZdiIVt1VExS65pxj2Q4CKm8GeAAThZxVzN8yR9jeRpMIf5y/AJj7DGxXvP/86jfoP95RwAAAAA==[0m[Pipeline] {
[2021-09-01T05:08:43.745Z] [8mha:////4B/88Iqs2b4bn4fnrIeDoWFR4OF1B8LKp/N4eV1jqzZ6AAAApR+LCAAAAAAAAP9tjTEOwjAUQ3+KOrAycoh0gQkxsUZZOEFIQkgb/d8mKe3EibgadyBQiQlLlmxL1nu+oE4RjhQdby12HpP2vA+jK4lPFLtroIm3dOGaMFGwXNpJkrGnpUrKFhaxClYC1hZ1oOTRZdiIVt1VExS65pxj2Q4CKm8GeAAThZxVzN8yR9jeRpMIf5y/AJj7DGxXvP/86jc09154wAAAAA==[0m[Pipeline] ws
[2021-09-01T05:08:43.755Z] Running in c:/b/review_autosar
[2021-09-01T05:08:43.764Z] [8mha:////4IhRwwIc1jQOyTVBt3R1zNAmKxguAF4nIYX4DRcqbjdSAAAApR+LCAAAAAAAAP9tjTEOwjAUQ3+KOrAycoh0ggUxsUZZOEFIQkgb/d8mKe3EibgadyBQiQlLlmxL1nu+oE4RjhQdby12HpP2vA+jK4lPFLtroIm3dOGaMFGwXNpJkrGnpUrKFhaxClYC1hZ1oOTRZdiIVt1VExS65pxj2Q4CKm8GeAAThZxVzN8yR9jeRpMIf5y/AJj7DGxXvP/86jek7ggRwAAAAA==[0m[Pipeline] {
[2021-09-01T05:08:43.814Z] [8mha:////4I8/rDIKNCkZFAXOSdrzTUCmWv1/+OmmevDDhPVV+LQKAAAApR+LCAAAAAAAAP9tjTEOwjAUQ3+KOrAycoh0gwExsUZZOEFIQkgb/d8mKe3EibgadyBQiQlLlmxL1nu+oE4RjhQdby12HpP2vA+jK4lPFLtroIm3dOGaMFGwXNpJkrGnpUrKFhaxClYC1hZ1oOTRZdiIVt1VExS65pxj2Q4CKm8GeAAThZxVzN8yR9jeRpMIf5y/AJj7DGxXvP/86jcChmMxwAAAAA==[0m[Pipeline] timestamps
[2021-09-01T05:08:43.814Z] The timestamps step is unnecessary when timestamps are enabled for all Pipeline builds.
[2021-09-01T05:08:43.824Z] [8mha:////4NsQUP7m8PU9jPyIZc/g85jRlSHVIQrteuI4RdzIkpmDAAAApR+LCAAAAAAAAP9tjTEOwjAUQ3+KOrAycoh0BAkxsUZZOEFIQkgb/d8mKe3EibgadyBQiQlLlmxL1nu+oE4RjhQdby12HpP2vA+jK4lPFLtroIm3dOGaMFGwXNpJkrGnpUrKFhaxClYC1hZ1oOTRZdiIVt1VExS65pxj2Q4CKm8GeAAThZxVzN8yR9jeRpMIf5y/AJj7DGxXvP/86jfpX/cvwAAAAA==[0m[Pipeline] {
[2021-09-01T05:08:43.871Z] [8mha:////4EV7zLxdwmHJUIJy/0iZO0LdX5RfAiU3iNNpJ2D++ajTAAAApR+LCAAAAAAAAP9tjTEOwjAUQ3+KOrAycoh0BSEm1igLJwhJCGmj/9skpZ04EVfjDgQqMWHJkm3Jes8X1CnCkaLjrcXOY9Ke92F0JfGJYncNNPGWLlwTJgqWSztJMva0VEnZwiJWwUrA2qIOlDy6DBvRqrtqgkLXnHMs20FA5c0AD2CikLOK+VvmCNvbaBLhj/MXAHOfge2K959f/QbB16AVwAAAAA==[0m[Pipeline] timeout
[2021-09-01T05:08:43.871Z] Timeout set to expire in 6 hr 0 min
[2021-09-01T05:08:43.881Z] [8mha:////4AcEGGibeN6AhLpzzI0GJ/9LuLzrVui8IerI5VcqsydsAAAAph+LCAAAAAAAAP9tjTEOwjAQBM9BKWgpeYQDJUJUtJYbXmBiY5xYd8G+kFS8iK/xBwKRqNhqZ6XVPF9Q5gQHSl42DtuAuQ6yi72fmhwotZdIg2zoLGvCTNFJ7QZN1h1n1MQO5ogCFgqWDutIOaBnWKnG3E0VDfrqxGna9gqKYG/wAKEmM5vEXxgTrK+9zYQ/z18BjB2D2DEU283nWL4Bsam+msEAAAA=[0m[Pipeline] {
[2021-09-01T05:08:43.929Z] [8mha:////4OAkCGkjG8wLX/ewR9Of0BF9yO+r7gS4NUv7Wp1AR5oCAAAApR+LCAAAAAAAAP9tjTEOwjAUQ3+DOrAycoiUzoiJNerCCUITQtLo/zZJSSdOxNW4Ay2VmPBg2Zas93pDGQOcKBjuNHYWY2t570czJ54pdDdPmTu68pYwkte80bkhpc9rbShpWFUw2AjYamw9RYsmwU44+ZCVl2iqSwrzdhTArBrgCYWYyUmG9C1TgP19VJHwx/kLgKlPwOrDYvXyLD8BobDcwgAAAA==[0m[Pipeline] stage
[2021-09-01T05:08:43.939Z] [8mha:////4E7fkPMwRROJRQAN3FOtcaeJRJPEa+bNu/CmQrF+VCmTAAAAph+LCAAAAAAAAP9tjTEOwjAUQ3+LOrAycohUZUVMrFEWThCaEJJG/7dJSjpxIq7GHWipxIQHy7ZkvdcbqhjgRMEwp7GzGFvLej+aObFMobt5yszRlbWEkbxmQmdBSp/XKihpWFWUsOGw1dh6ihZNgh138iFrL9HUlxTm7cihtGqAJxR8JicZ0rdMAfb3UUXCH+cvAKY+Qdk0ix2WZ/UBbwyqm8IAAAA=[0m[Pipeline] { (clean workspace)
[2021-09-01T05:08:43.990Z] Stage "clean workspace" skipped due to when conditional
[2021-09-01T05:08:44.008Z] [8mha:////4CFtrNiwl49yAboaZg7cHfuu+X/SqBJ9DvjBy4oAE94PAAAAox+LCAAAAAAAAP9tjTEOwjAQBC9BFLSUPOIioERUaS03vMDExjix7oztkFS8iK/xBwKRqNhitbPNPF+wTBGOHC22hjpHqXEYfG+nhQPH7uJ5wJbP2DAl9galGSRrU88oORuYU5SwELAy1HhOjmyGtWjVXVVeka1OOU7fQUDp9A0eUIjJnFXMXxgjbK69Tkw/z18BjCFkKLf7T+3eO7Rwfr4AAAA=[0m[Pipeline] }
[2021-09-01T05:08:44.044Z] [8mha:////4EaFs9DnnryMqKVicT2nQ3IZwXCXJZXARwKvj+lrTGwAAAAApB+LCAAAAAAAAP9tjbEOwiAURV9rHFwd/QiaJm7GyZWw+AXYIkLJexSodPKL/DX/QbSJk3e4uecu5/mCdQxwpKCZVTgYjJ1h3k26LJYpDFdHmVm6sI4wklNMqCyoV6cFBSUFS6oaVhw2CjtH0aBOsOVW3mXjJOrmnEL5Dhxq04/wgIoXc5IhfWEOsLtNfST8ef4KYPY+Qd3uP9W+ATnVfPq+AAAA[0m[Pipeline] // stage
[2021-09-01T05:08:44.081Z] [8mha:////4KPCZ0M0yRb/p/weKvguXa3Oat3akgnpxWya7VROMJ6rAAAApx+LCAAAAAAAAP9tjTEOwjAUQ3+LOrAycogUBibE1DXKwglCE0La6P/yk9JOnIircQdaKjHhwbItWe/1hiIynIidaCy2HmPtRRd6NyUxELfXQINo6CJqwkjBCmUHRcZWS1WULCzKclhJWFusA0WPLsFGNvqhy6DRlefE03aUkHtzhydkciInzelbRobtrTeR8Mf5C4CxS5Dvd7Md5mfxARfjIUfCAAAA[0m[Pipeline] stage
[2021-09-01T05:08:44.091Z] [8mha:////4NBmC+eKwnCVdLf3rZ1zLUPl1l1Tc78zBMsgqG2uol4+AAAApx+LCAAAAAAAAP9tjTEOwjAUQ3+LOrAycohUDLAgpq5RFk4QmhDSRv+Xn5R24kRcjTvQUokJD5ZtyXqvNxSR4UTsRGOx9RhrL7rQuymJgbi9BhpEQxdRE0YKVig7KDK2WqqiZGFRlsNKwtpiHSh6dAk2stEPXQaNrjwnnrajhNybOzwhkxM5aU7fMjJsb72JhD/OXwCMXYJ8t5/tMD+LD/s94rDCAAAA[0m[Pipeline] { (Prepare)
[2021-09-01T05:08:44.137Z] [8mha:////4Pq57J+BB7TM3zYyKtHTiM2bOjSo5fljvcFsrnDS5DR8AAAApx+LCAAAAAAAAP9tjTEOwjAUQ3+LOrAycohULDAgpq5RFk4QmhDSRv+Xn5R24kRcjTvQUokJD5ZtyXqvNxSR4UTsRGOx9RhrL7rQuymJgbi9BhpEQxdRE0YKVig7KDK2WqqiZGFRlsNKwtpiHSh6dAk2stEPXQaNrjwnnrajhNybOzwhkxM5aU7fMjJsb72JhD/OXwCMXYJ8t5/tMD+LD1+IXOLCAAAA[0m[Pipeline] script
[2021-09-01T05:08:44.146Z] [8mha:////4Eos3bYTy2ky2+hPY5f6Gi+4p1IoHBt+F0qn+csYb/VkAAAApx+LCAAAAAAAAP9tjTEOwjAUQ3+LOrAycohUTCAhpq5RFk4QmhDSRv+Xn5R24kRcjTvQUokJD5ZtyXqvNxSR4UTsRGOx9RhrL7rQuymJgbi9BhpEQxdRE0YKVig7KDK2WqqiZGFRlsNKwtpiHSh6dAk2stEPXQaNrjwnnrajhNybOzwhkxM5aU7fMjJsb72JhD/OXwCMXYJ8t5/tMD+LD0rlhNLCAAAA[0m[Pipeline] {
[2021-09-01T05:08:44.184Z] [8mha:////4G2qaQy5prVwGaBRbt/+hd7EPOJki0PV5THV6YAngOZ4AAAApx+LCAAAAAAAAP9tjTEOwjAUQ3+LOrAycohUbCDE1DXKwglCE0La6P/yk9JOnIircQdaKjHhwbItWe/1hiIynIidaCy2HmPtRRd6NyUxELfXQINo6CJqwkjBCmUHRcZWS1WULCzKclhJWFusA0WPLsFGNvqhy6DRlefE03aUkHtzhydkciInzelbRobtrTeR8Mf5C4CxS5Dv9rMd5mfxASnCIIrCAAAA[0m[Pipeline] dir
[2021-09-01T05:08:44.185Z] Running in c:/b/review_autosar
[2021-09-01T05:08:44.193Z] [8mha:////4FFWdk2vf6M82gHcXz+QEj/UVwnwxQcSjsJsb7pM2/sZAAAApx+LCAAAAAAAAP9tjTEOwjAUQ3+LOrAycogU2BDqxBp14QShCSFp9H9JUtKJE3E17kCgEhMeLNuS9Z4vqIKHhrxmVmFvMHSGDW7UObFEvr84SszSmXWEgZxirUotSXWca0tRwayihAWHpcLOUTCoI6y4FXdRO4G6PkWftwOH0sgbPKDgmRyFj98yeVhfRxkIf5y/AJiGCOV2n223+TyrN7xWSV3CAAAA[0m[Pipeline] {
[2021-09-01T05:08:44.223Z] [8mha:////4K89W0Pz4J6rNq+H2ZhWP5bL2IQY5xMcH0X7EQId9TILAAAAox+LCAAAAAAAAP9tjTEOwjAQBC9BFLSUPOICtIgqreWGF5jYGCfWnbEdkooX8TX+QCASFVusdraZ5wuWKcKRo8XWUOcoNQ6D7+20cODYXTwP2PIZG6bE3qA0g2Rt6hklZwNzihIWAlaGGs/Jkc2wFq26q8orstUpx+k7CCidvsEDCjGZs4r5C2OEzbXXienn+SuAMWQo99tP7cIbaOP4NL4AAAA=[0m[Pipeline] fileExists
[2021-09-01T05:08:44.268Z] [8mha:////4H8RDBY3Sv213ARVFt8sFZOZrt71uQNXJ8WFgwtkQSLSAAAAox+LCAAAAAAAAP9tjTEOwjAQBC9BFLSUPOKC0iIqWisNLzCJMXasO8d2cCpexNf4A4FIVGyx2tlmni9YxwBHDhqtot5QbA16N+p5YebQXx1ntHzBlimyU9io3HCnTgs2nBQsKUpYCdgoah1HQzrBVlh5l5WTpKtzCvN3EFCaboAHFGI2JxnSF6YAu9vYRaaf568AJp+grPefqv0bq7DVH74AAAA=[0m[Pipeline] bat
[2021-09-01T05:08:45.443Z] [8mha:////4ORCtPc+SA2M3XycjRx5tuXlYh+r4sYmkiz0AWHzKDAMAAAAox+LCAAAAAAAAP9tjTEOwjAQBC9BFLSUPOKCoERUaS03vMDExjix7oztkFS8iK/xBwKRqNhitbPNPF+wTBGOHC22hjpHqXEYfG+nhQPH7uJ5wJbP2DAl9galGSRrU88oORuYU5SwELAy1HhOjmyGtWjVXVVeka1OOU7fQUDp9A0eUIjJnFXMXxgjbK69Tkw/z18BjCFDudt+ah/e6oHOBr4AAAA=[0m[Pipeline] bat
[2021-09-01T05:08:45.783Z] 
[2021-09-01T05:08:45.783Z] c:\b\review_autosar>mkdir result_dir 
[2021-09-01T05:08:45.794Z] [8mha:////4PqsAqBA2bpcaHeSwAH3e6hZvTGFz445Y65+qgy+Yt0OAAAApB+LCAAAAAAAAP9tjTEOwjAQBC9BFLSUPOKCEB2iSmu54QUmNsaJdWdsh6TiRXyNPxCIRMUWq51t5vmCZYpw5GixNdQ5So3D4Hs7LRw4dhfPA7Z8xoYpsTcozSBZm3pGydnAnKKEhYCVocZzcmQzrEWr7qryimx1ynH6DgJKp2/wgEJM5qxi/sIYYXPtdWL6ef4KYAwhQ7nbf2r7BvGsvpe+AAAA[0m[Pipeline] }
[2021-09-01T05:08:45.816Z] [8mha:////4BTFB435yetP62n1XTxP6YQDoE42UmPwZaDavRFdeCsEAAAAph+LCAAAAAAAAP9tjTEOwjAQBC9BFLSUPOIikCgQSkVrueEFJjHGiXUXbAen4kV8jT9gEYmKLVY728zrDcvgoWZvsNPUWwqNxcGNJi9M7Pur44QdX7BhCuw0Sp0kt/o0o+SoYU5RwkLASlPjOFgyEdaiUw9VOUWmOkefv6OA0rZ3eEIhsjkqH78wedjcxjYw/Tx/BTANQ4Ryt8+1PXwAM5QTDr4AAAA=[0m[Pipeline] // dir
[2021-09-01T05:08:45.834Z] [8mha:////4CbaESerRqdMX1v62skSdxxL7SxTU25fJU/KgLbGWx4AAAAAph+LCAAAAAAAAP9tjTEOwjAQBC9BFLSUPOIiKBASSkVrueEFJjHGiXUXbAen4kV8jT9gEYmKLVY728zrDcvgoWZvsNPUWwqNxcGNJi9M7Pur44QdX7BhCuw0Sp0kt/o0o+SoYU5RwkLASlPjOFgyEdaiUw9VOUWmOkefv6OA0rZ3eEIhsjkqH78wedjcxjYw/Tx/BTANQ4Ryt8+1PXwAC9aA/74AAAA=[0m[Pipeline] }
[2021-09-01T05:08:45.863Z] [8mha:////4FpRRN4uGgY5RgAwjxLzFv1W78TQiRZ8CfuZHP9lpsakAAAApR+LCAAAAAAAAP9tjTEOwjAQBC9BFLSUPOIiaFIgqrRWGl5gYmOcWHfGdkgqXsTX+AMWkajYYrWzzbzesI4BThwM9poGS7Gz6N1o8sKJw3B1PGHPF+yYIjuNrZ5aVrpZsOWkYUlRwkrARlPnOFoyCbailw9ZOUmmOqeQv6OA0qo7PKEQ2ZxkSF+YA+xuo4pMP89fAczeJygPda59/QE/GGOkvgAAAA==[0m[Pipeline] // script
[2021-09-01T05:08:45.880Z] [8mha:////4AdDQBhM4fxChPYxXAR712v0ILXpghZCoSFLFt4uxASSAAAAph+LCAAAAAAAAP9tjTEOwjAQBC9BFLSUPOIiKBASSkVrueEFJjHGiXUXbAen4kV8jT9gEYmKLVY728zrDcvgoWZvsNPUWwqNxcGNJi9M7Pur44QdX7BhCuw0Sp0kt/o0o+SoYU5RwkLASlPjOFgyEdaiUw9VOUWmOkefv6OA0rZ3eEIhsjkqH78wedjcxjYw/Tx/BTANQ4Ryd8i13X8AfJoyIr4AAAA=[0m[Pipeline] }
[2021-09-01T05:08:45.908Z] [8mha:////4JZW8WwZQe0nNATsjkdAu7BHlaTSpNZj7x9WUxWhwsB6AAAAph+LCAAAAAAAAP9tjTEOwjAQBC9BFLSUPOIikCgQSkVrueEFJjHGiXUXbAen4kV8jT9gEYmKLVY728zrDcvgoWZvsNPUWwqNxcGNJi9M7Pur44QdX7BhCuw0Sp0kt/o0o+SoYU5RwkLASlPjOFgyEdaiUw9VOUWmOkefv6OA0rZ3eEIhsjkqH78wedjcxjYw/Tx/BTANQ4Ryd8i13X8AYxhncL4AAAA=[0m[Pipeline] // stage
[2021-09-01T05:08:45.944Z] [8mha:////4I6zqLMRmkh6f0RLzWP2zNM5y7nVJoCf5m87+NST83foAAAApx+LCAAAAAAAAP9tjTEOwjAUQ3+LOrAycoi0iBExdY26cILQhJA0+r8kKenEibgadyBQiQkPlm3Jes8XVMHDkbxmVuFgMPSGjW7SObFEfrg4SszSmfWEgZxinUodSdUutaOoYFFRworDWmHvKBjUETbciruonUBdn6LP24FDaeQNHlDwTI7Cx2+ZPWyvkwyEP85fAMxjhHLXZNs3n2f1Bo1jc4DCAAAA[0m[Pipeline] stage
[2021-09-01T05:08:45.954Z] [8mha:////4E9kHhth0Un1M/DVWEM2qh/rgfciaUIRJBq1A67O2zu8AAAAph+LCAAAAAAAAP9tjTEOwjAUQ3+LOrAycogUxIiYukZZOEFoQkgb/V9+UtqJE3E17kBLJSY8WLYl673eUESGE7ETjcXWY6y96ELvpiQG4vYaaBANXURNGClYoeygyNhqqYqShUVZDisJa4t1oOjRJdjIRj90GTS68px42o4Scm/u8IRMTuSkOX3LyLC99SYS/jh/ATB2CfLDbrb9/Cw+ty4Up8IAAAA=[0m[Pipeline] { (Archive env variables)
[2021-09-01T05:08:46.000Z] [8mha:////4J7ZUwuN0uzgXN/MycDrci5CSwH5zO42ZZZjxa4ISU2KAAAAph+LCAAAAAAAAP9tjTEOwjAUQ3+LOrAycohUwIiYukZZOEFoQkgb/V9+UtqJE3E17kBLJSY8WLYl673eUESGE7ETjcXWY6y96ELvpiQG4vYaaBANXURNGClYoeygyNhqqYqShUVZDisJa4t1oOjRJdjIRj90GTS68px42o4Scm/u8IRMTuSkOX3LyLC99SYS/jh/ATB2CfLDbrb9/Cw+2YMO4MIAAAA=[0m[Pipeline] script
[2021-09-01T05:08:46.009Z] [8mha:////4DP99sf+JnDDPHIhLEYTli7dzJvelSoDCc7BCu+IWUf1AAAAph+LCAAAAAAAAP9tjTEOwjAUQ3+DOrAycohUoiNiYo26cILQhJA0+r9NUtKJE3E17kBLJSY8WLYl673eUMYAJwqGO42dxdha3vvRzIlnCt3NU+aOrrwljOQ1b3RuSOnzWhtKGlYVDDYCthpbT9GiSbATTj5k5SWa6pLCvB0FMKsGeEIhZnKSIX3LFGB/H1Uk/HH+AmDqE7D6sFi9PMsPfTawssIAAAA=[0m[Pipeline] {
[2021-09-01T05:08:46.046Z] [8mha:////4JaejfevRFGiTVnQkCnaIaJ8DxxdNuQ7QbpWpRsVAKFvAAAAox+LCAAAAAAAAP9tjTESwiAURH/iWNhaeghSxM6xsmVoPAEmiBDmfwJEUnkir+YdRDNj5RY7+7Z5zxesY4AjBc2swsFg7AzzbtJlsUxhuDrKzNKFdYSRnGJCZUG9Oi0oKClYUtWw4rBR2DmKBnWCLbfyLhsnUTfnFMp34FCbfoQHVLyYkwzpC3OA3W3qI+HP81cAs09Qt+2n9v4NGWcPtb4AAAA=[0m[Pipeline] bat
[2021-09-01T05:08:46.389Z] [8mha:////4G/C+MJt/E102JOtP3mRVoGERpyEa/KueBX8piyCjeidAAAAox+LCAAAAAAAAP9tjTESwiAURH/iWNhaeghSZKwcK1uGxhNggghh/idAJJUn8mreQTQzVm6xs2+b93zBOgY4UtDMKhwMxs4w7yZdFssUhqujzCxdWEcYySkmVBbUq9OCgpKCJVUNKw4bhZ2jaFAn2HIr77JxEnVzTqF8Bw616Ud4QMWLOcmQvjAH2N2mPhL+PH8FMPsEddt+au/fWFYUrL4AAAA=[0m[Pipeline] bat
[2021-09-01T05:08:46.748Z] 
[2021-09-01T05:08:46.749Z] c:\b\review_autosar>set  1>env.txt 
[2021-09-01T05:08:46.777Z] [8mha:////4JbqVYzKdEaXlbh8VF+fLryIDyWzEPp+MSurAlX+VO/dAAAApB+LCAAAAAAAAP9tjTESwiAURH/iWNhaeghSZMbGsbJlaDwBJogQ5n8CRFJ5Iq/mHUQzY+UWO/u2ec8XrGOAIwXNrMLBYOwM827SZbFMYbg6yszShXWEkZxiQmVBvTotKCgpWFLVsOKwUdg5igZ1gi238i4bJ1E35xTKd+BQm36EB1S8mJMM6QtzgN1t6iPhz/NXALNPULftp/b+DZsFOYe+AAAA[0m[Pipeline] bat
[2021-09-01T05:08:47.124Z] [8mha:////4NhTXw+jwhvZlI+jgWAvVfk5TEC0KTsGV/ZDdrqkM7Z0AAAApB+LCAAAAAAAAP9tjTESwiAURH/iWNhaeghSpLBwrGwZGk+ACSKE+Z8AkVSeyKt5B9HMWLnFzr5t3vMF6xjgSEEzq3AwGDvDvJt0WSxTGK6OMrN0YR1hJKeYUFlQr04LCkoKllQ1rDhsFHaOokGdYMutvMvGSdTNOYXyHTjUph/hARUv5iRD+sIcYHeb+kj48/wVwOwT1G37qb1/A9o0Ip6+AAAA[0m[Pipeline] bat
[2021-09-01T05:08:47.446Z] 
[2021-09-01T05:08:47.446Z] c:\b\review_autosar>xcopy /y /R env.txt "c:/b/review_autosar/result_dir" 
[2021-09-01T05:08:47.446Z] C:env.txt
[2021-09-01T05:08:47.446Z] 1 File(s) copied
[2021-09-01T05:08:47.472Z] [8mha:////4Gs16H4hcyvhYRXUP/zsPjSiMiYWsyiOeFc0R7alWiFMAAAApB+LCAAAAAAAAP9tjTESwiAURH/iWNhaeghSpHHGsbJlaDwBJogQ5n8CRFJ5Iq/mHUQzY+UWO/u2ec8XrGOAIwXNrMLBYOwM827SZbFMYbg6yszShXWEkZxiQmVBvTotKCgpWFLVsOKwUdg5igZ1gi238i4bJ1E35xTKd+BQm36EB1S8mJMM6QtzgN1t6iPhz/NXALNPULftp/b+DRUouhm+AAAA[0m[Pipeline] archiveArtifacts
[2021-09-01T05:08:47.483Z] Archiving artifacts
[2021-09-01T05:08:48.267Z] [8mha:////4Ma2uLeGdDHwn+jkRowxFria+Fa25hKB18kP6dIcEhQXAAAAox+LCAAAAAAAAP9tjTESwiAURH/iWNhaeghSpHIcK1uGxhNggghh/idAJJUn8mreQTQzVm6xs2+b93zBOgY4UtDMKhwMxs4w7yZdFssUhqujzCxdWEcYySkmVBbUq9OCgpKCJVUNKw4bhZ2jaFAn2HIr77JxEnVzTqF8Bw616Ud4QMWLOcmQvjAH2N2mPhL+PH8FMHufoG73n2rfYNRFDb4AAAA=[0m[Pipeline] }
[2021-09-01T05:08:48.295Z] [8mha:////4PzDTFRCYzJMnl2WRGys4NHVb49HyDhtMqRqxi5/ZheIAAAApB+LCAAAAAAAAP9tjTEOwjAQBC9BFLSUPOKCgA6lorXc8AKTGOPEugu2g1PxIr7GH7CIRMUWq51t5vWGZfBQszfYaeothcbi4EaTFyb2/dVxwo4v2DAFdhqlTpJbfZpRctQwpyhhIWClqXEcLJkIa9Gph6qcIlOdo8/fUUBp2zs8oRDZHJWPX5g8bG5jG5h+nr8CmIYhQnnY5trvPrCL6pi+AAAA[0m[Pipeline] // script
[2021-09-01T05:08:48.314Z] [8mha:////4Gxi86OuWJr1JQM0ESLQH1XMUn/SJaWGTfmiPwvUcF5bAAAApB+LCAAAAAAAAP9tjTEOwjAQBC9BFLSUPOKiCDpEldZKwwtMbIwT687YDknFi/gaf8AiEhVbrHa2mdcb1jHAiYPBXtNgKXYWvRtNXjhxGK6OJ+z5gh1TZKex1VPLSjcLtpw0LClKWAnYaOocR0smwVb08iErJ8lU5xTydxRQWnWHJxQim5MM6QtzgN1tVJHp5/krgNn7BOWhzrWvP68Jv8q+AAAA[0m[Pipeline] }
[2021-09-01T05:08:48.335Z] [8mha:////4E5jFiDddJdrYB3T+I1flyROPyMGWE09ZMWTa9r5o83HAAAApB+LCAAAAAAAAP9tjTEOwjAQBC9BFLSUPOKCgA6lorXc8AKTGOPEugu2g1PxIr7GH7CIRMUWq51t5vWGZfBQszfYaeothcbi4EaTFyb2/dVxwo4v2DAFdhqlTpJbfZpRctQwpyhhIWClqXEcLJkIa9Gph6qcIlOdo8/fUUBp2zs8oRDZHJWPX5g8bG5jG5h+nr8CmIYhQnnY5dpvP5dLLDu+AAAA[0m[Pipeline] // stage
[2021-09-01T05:08:48.372Z] [8mha:////4MA+HbVTnUrp7vxs6Lj332oJMdk7mAJHkzhLmEpjSQk4AAAApx+LCAAAAAAAAP9tjTEOwjAUQ3+LOrAycogUBBvqxBp14QShCSFp9H9JUtKJE3E17kCgEhMeLNuS9Z4vqIKHhrxmVmFvMHSGDW7UObFEvr84SszSmXWEgZxirUotSXWca0tRwayihAWHpcLOUTCoI6y4FXdRO4G6PkWftwOH0sgbPKDgmRyFj98yeVhfRxkIf5y/AJiGCOV2k22/+zyrN60vQMPCAAAA[0m[Pipeline] stage
[2021-09-01T05:08:48.382Z] [8mha:////4Cv0mtqkzvoL7t+GmaqDYojUrSRC9Y9xIZRQ1mB5rvvIAAAAph+LCAAAAAAAAP9tjTEOwjAUQ3+DOrAycoh0oBtiYo26cILQhJA0+r9NUtKJE3E17kBLJSY8WLYl673eUMYAJwqGO42dxdha3vvRzIlnCt3NU+aOrrwljOQ1b3RuSOnzWhtKGlYVDDYCthpbT9GiSbATTj5k5SWa6pLCvB0FMKsGeEIhZnKSIX3LFGB/H1Uk/HH+AmDqE7D6sFi9PMsPBA4E38IAAAA=[0m[Pipeline] { (do preBuild actions)
[2021-09-01T05:08:48.469Z] [8mha:////4MjerC0YLPAK35Mda/9YQY0ddICSeWgKjVAFQzOUqmG2AAAAox+LCAAAAAAAAP9tjTESwiAURH/iWNhaegjSxMqxsmVoPAEmiBDmfwJEUnkir+YdRDNj5RY7+7Z5zxesY4AjBc2swsFg7AzzbtJlsUxhuDrKzNKFdYSRnGJCZUG9Oi0oKClYUtWw4rBR2DmKBnWCLbfyLhsnUTfnFMp34FCbfoQHVLyYkwzpC3OA3W3qI+HP81cAs09Qt+2n9v4NSnc5Zr4AAAA=[0m[Pipeline] echo
[2021-09-01T05:08:48.478Z] Jenkins slave name:        Exec706_SI-ZENG06W010_4_134
[2021-09-01T05:08:48.496Z] [8mha:////4BtI3l94GOLPQ7yqHBQ7+eWkgsUiciBQEhEpo0+3tx5cAAAAox+LCAAAAAAAAP9tjTESwiAURH/iWNhaegjSZGwcK1uGxhNggghh/idAJJUn8mreQTQzVm6xs2+b93zBOgY4UtDMKhwMxs4w7yZdFssUhqujzCxdWEcYySkmVBbUq9OCgpKCJVUNKw4bhZ2jaFAn2HIr77JxEnVzTqF8Bw616Ud4QMWLOcmQvjAH2N2mPhL+PH8FMPsEddt+au/fiSQUTb4AAAA=[0m[Pipeline] echo
[2021-09-01T05:08:48.506Z] Jenkins slave description: Swarm agent from **********: Cores: 6 Memory(GB): 32 Snapshot: snap_20210825-163520-706300
[2021-09-01T05:08:48.524Z] [8mha:////4DZKNZ7gGDkvBzi6PgOWHD7I+yFz2jubehUaNxBiiGjGAAAApB+LCAAAAAAAAP9tjTESwiAURH/iWNhaegjSZMbCsbJlaDwBJogQ5n8CRFJ5Iq/mHUQzY+UWO/u2ec8XrGOAIwXNrMLBYOwM827SZbFMYbg6yszShXWEkZxiQmVBvTotKCgpWFLVsOKwUdg5igZ1gi238i4bJ1E35xTKd+BQm36EB1S8mJMM6QtzgN1t6iPhz/NXALNPULftp/b+DcgVD1S+AAAA[0m[Pipeline] echo
[2021-09-01T05:08:48.533Z] Jenkins slave up since:    Wed Sep 01 07:08:40 CEST 2021
[2021-09-01T05:08:48.551Z] [8mha:////4Pn46nGMmaYtBAJI5cCuy/6BMiRuYa/QqM2hK5wkFtZdAAAApB+LCAAAAAAAAP9tjTESwiAURH/iWNhaegjSpHDGsbJlaDwBJogQ5n8CRFJ5Iq/mHUQzY+UWO/u2ec8XrGOAIwXNrMLBYOwM827SZbFMYbg6yszShXWEkZxiQmVBvTotKCgpWFLVsOKwUdg5igZ1gi238i4bJ1E35xTKd+BQm36EB1S8mJMM6QtzgN1t6iPhz/NXALNPULftp/b+DQcJl9O+AAAA[0m[Pipeline] echo
[2021-09-01T05:08:48.560Z] Jenkins slave log:
[2021-09-01T05:08:48.579Z] [8mha:////4L2zWrS3nV1APCvmF/SYUpSoFAfOjaUSwljplubKlZyUAAAAox+LCAAAAAAAAP9tjTESwiAURH/iWNhaegjSpHEcK1uGxhNggghh/idAJJUn8mreQTQzVm6xs2+b93zBOgY4UtDMKhwMxs4w7yZdFssUhqujzCxdWEcYySkmVBbUq9OCgpKCJVUNKw4bhZ2jaFAn2HIr77JxEnVzTqF8Bw616Ud4QMWLOcmQvjAH2N2mPhL+PH8FMPsEddt+au/fRjiMyr4AAAA=[0m[Pipeline] echo
[2021-09-01T05:08:48.588Z] ##### Start of log #####
[2021-09-01T05:08:48.606Z] [8mha:////4BiF6QVO/ovgAZcNXZpELjqDwsGf0tLxsp795R37v8CKAAAApB+LCAAAAAAAAP9tjTEOwjAQBC9BFLSUPMKhCBWiSmul4QUmPowT687YDknFi/gaf8AiEhVbrHa2mdcb1jHAiYMRPdJgKXZWeDeavMTEYbg6nkTPF9ExRXYoWpxa1tgs2HJCWFKUsJKwQeocR0smwVb26qEqp8hU5xTyd5RQWn2HJxQym5MK6QtzgN1t1JHp5/krgNknKOs612HvPzjpjBq+AAAA[0m[Pipeline] echo
[2021-09-01T05:08:48.615Z] Inbound agent connected from **********/**********:58846
[2021-09-01T05:08:48.615Z] Remoting version: 4.9
[2021-09-01T05:08:48.615Z] This is a Windows agent
[2021-09-01T05:08:48.615Z] Agent successfully connected and online
[2021-09-01T05:08:48.615Z] 
[2021-09-01T05:08:48.633Z] [8mha:////4G7brpMSOTpuN2ip+1BXePFWE+Sx/80BORmwxy8tw7BhAAAApR+LCAAAAAAAAP9tjTEOwjAQBC9BFLSUPMIRUqgQVVorDS8w8WGcWHfGdkgqXsTX+AMWkajYYrWzzbzesI4BThyM6JEGS7GzwrvR5CUmDsPV8SR6voiOKbJD0eLUssZmwZYTwpKihJWEDVLnOFoyCbayVw9VOUWmOqeQv6OE0uo7PKGQ2ZxUSF+YA+xuo45MP89fAcw+QVnXuQ57/wF52JcDvgAAAA==[0m[Pipeline] echo
[2021-09-01T05:08:48.643Z] ##### End of log #####
[2021-09-01T05:08:48.689Z] [8mha:////4CMzYQaBGxJizmhavAr52K7x1FbFHh04OO++9e0WTHygAAAApR+LCAAAAAAAAP9tjTEOwjAQBC9BFLSUPMKRUKgQVVorDS8w8WGcWHfGdkgqXsTX+AMWkajYYrWzzbzesI4BThyM6JEGS7GzwrvR5CUmDsPV8SR6voiOKbJD0eLUssZmwZYTwpKihJWEDVLnOFoyCbayVw9VOUWmOqeQv6OE0uo7PKGQ2ZxUSF+YA+xuo45MP89fAcw+QVnXuQ57/wG6i7oovgAAAA==[0m[Pipeline] echo
[2021-09-01T05:08:48.698Z] Environment variables of Jenkins slave inside "bat" environment:
[2021-09-01T05:08:48.717Z] [8mha:////4NZcD9SuRpDlUm+tFPj5LtJRnXlMHWeqPSFfDOd/uzBLAAAApB+LCAAAAAAAAP9tjTEOwjAQBC9BFLSUPMIpCBWiSmul4QUmPowT687YDknFi/gaf8AiEhVbrHa2mdcb1jHAiYMRPdJgKXZWeDeavMTEYbg6nkTPF9ExRXYoWpxa1tgs2HJCWFKUsJKwQeocR0smwVb26qEqp8hU5xTyd5RQWn2HJxQym5MK6QtzgN1t1JHp5/krgNknKOs612HvP/u6oTG+AAAA[0m[Pipeline] bat
[2021-09-01T05:08:49.035Z] 
[2021-09-01T05:08:49.035Z] c:\b\review_autosar>set
[2021-09-01T05:08:49.035Z] AGENT_NAME=buildslave_si-zeng06w010 && swarm
[2021-09-01T05:08:49.035Z] ALLUSERSPROFILE=C:\ProgramData
[2021-09-01T05:08:49.035Z] APPDATA=C:\Users\<USER>\AppData\Roaming
[2021-09-01T05:08:49.035Z] BIAAS_BUILD_VM_SNAPSHOT_ID=Update environment variables
[2021-09-01T05:08:49.035Z] BIAAS_ENV=https://rb-biaas-06.de.bosch.com/biaas-ng-webapp
[2021-09-01T05:08:49.035Z] BRANCH=platform-develop-qnx
[2021-09-01T05:08:49.035Z] BUILD_DISPLAY_NAME=#10085
[2021-09-01T05:08:49.035Z] BUILD_ID=10085
[2021-09-01T05:08:49.035Z] BUILD_NUMBER=10085
[2021-09-01T05:08:49.035Z] BUILD_PRODUCT=fca
[2021-09-01T05:08:49.035Z] BUILD_TAG=jenkins-Git_platform_hydra_autosar_on-review-10085
[2021-09-01T05:08:49.035Z] BUILD_TYPE=DEBUG
[2021-09-01T05:08:49.035Z] BUILD_URL=https://rb-jmaas.de.bosch.com/cm_build/job/Git_platform_hydra_autosar_on-review/10085/
[2021-09-01T05:08:49.035Z] CI=true
[2021-09-01T05:08:49.035Z] CommonProgramFiles=C:\Program Files\Common Files
[2021-09-01T05:08:49.035Z] CommonProgramFiles(x86)=C:\Program Files (x86)\Common Files
[2021-09-01T05:08:49.035Z] CommonProgramW6432=C:\Program Files\Common Files
[2021-09-01T05:08:49.035Z] COMPUTERNAME=SI-ZENG06W010
[2021-09-01T05:08:49.035Z] ComSpec=C:\WINDOWS\system32\cmd.exe
[2021-09-01T05:08:49.035Z] CONAN_USER_HOME=C:/instr-cluster-tools/conan
[2021-09-01T05:08:49.035Z] CUSTOM_WS=c:/b/review_autosar
[2021-09-01T05:08:49.035Z] DriverData=C:\Windows\System32\Drivers\DriverData
[2021-09-01T05:08:49.035Z] EXECUTOR_NUMBER=0
[2021-09-01T05:08:49.035Z] GERRIT_BRANCH=master
[2021-09-01T05:08:49.035Z] GERRIT_CHANGE_COMMIT_MESSAGE=Wzc2MDU3MV1BZGQgY29uZmlndXJhdGlvbiBjaGFuZ2VzIGZvciBQRk1Qcm94eQoKLSBSZWdpc3RlciBQRk1Qcm94eSBhcyBhIHN1cGVydmlzZWQgRW50aXR5IHRvIFdkZ00KLSBBZGRlZCBhIGNhbGxiYWNrIGZ1bmN0aW9uIGZvciBmYWlsdXJlIGluIHN1cGVydmlzaW9uCi0gU3VwZXJ2aXNpb24gY3ljbGUgaXMgc2V0IGFzIDIwIGJhc2VkIG9uICgyMDAvMTApbXMKLSBPdGhlciBNYXggLE1pbiBNYXJnaW4gYXJlIHdpdGggYXBwcm94LiB2YWx1ZXMKLSBNZW1tYXAgYWRkZWQgaW4gdGhlIGNzdiBmaWxlCgpSVEMyLTc2MDU3MQoKQ2hhbmdlLUlkOiBJZTEwNDJhOGJjMDAwYWQ3ZDY5ZWJhNzcyYzNjYWIzMGVjMzFkZGFkNQo=
[2021-09-01T05:08:49.035Z] GERRIT_CHANGE_ID=Ie1042a8bc000ad7d69eba772c3cab30ec31ddad5
[2021-09-01T05:08:49.035Z] GERRIT_CHANGE_NUMBER=338415
[2021-09-01T05:08:49.035Z] GERRIT_CHANGE_OWNER=\"Ajithkumar Vijayan (MS/ECK5-XC)\" <<EMAIL>>
[2021-09-01T05:08:49.035Z] GERRIT_CHANGE_OWNER_EMAIL=<EMAIL>
[2021-09-01T05:08:49.035Z] GERRIT_CHANGE_OWNER_NAME=Ajithkumar Vijayan (MS/ECK5-XC)
[2021-09-01T05:08:49.035Z] GERRIT_CHANGE_PRIVATE_STATE=false
[2021-09-01T05:08:49.035Z] GERRIT_CHANGE_SUBJECT=[760571]Add configuration changes for PFMProxy
[2021-09-01T05:08:49.035Z] GERRIT_CHANGE_URL=https://rbcm-gerrit.de.bosch.com/c/projects/hydra/config/autosar/stdcore/saic-dms-is31/+/338415
[2021-09-01T05:08:49.035Z] GERRIT_CHANGE_WIP_STATE=false
[2021-09-01T05:08:49.035Z] GERRIT_EVENT_ACCOUNT=\"Ajithkumar Vijayan (MS/ECK5-XC)\" <<EMAIL>>
[2021-09-01T05:08:49.035Z] GERRIT_EVENT_ACCOUNT_EMAIL=<EMAIL>
[2021-09-01T05:08:49.035Z] GERRIT_EVENT_ACCOUNT_NAME=Ajithkumar Vijayan (MS/ECK5-XC)
[2021-09-01T05:08:49.036Z] GERRIT_EVENT_HASH=-**********
[2021-09-01T05:08:49.036Z] GERRIT_EVENT_TYPE=patchset-created
[2021-09-01T05:08:49.036Z] GERRIT_HOST=rbcm-gerrit.de.bosch.com
[2021-09-01T05:08:49.036Z] GERRIT_NAME=rbcm-gerrit.de.bosch.com:29418
[2021-09-01T05:08:49.036Z] GERRIT_PATCHSET_NUMBER=4
[2021-09-01T05:08:49.036Z] GERRIT_PATCHSET_REVISION=afb4bbb4bd182a1a37eff78c2f0bcb316a7e34fe
[2021-09-01T05:08:49.036Z] GERRIT_PATCHSET_UPLOADER=\"Ajithkumar Vijayan (MS/ECK5-XC)\" <<EMAIL>>
[2021-09-01T05:08:49.036Z] GERRIT_PATCHSET_UPLOADER_EMAIL=<EMAIL>
[2021-09-01T05:08:49.036Z] GERRIT_PATCHSET_UPLOADER_NAME=Ajithkumar Vijayan (MS/ECK5-XC)
[2021-09-01T05:08:49.036Z] GERRIT_PORT=29418
[2021-09-01T05:08:49.036Z] GERRIT_PROJECT=projects/hydra/config/autosar/stdcore/saic-dms-is31
[2021-09-01T05:08:49.036Z] GERRIT_REFSPEC=refs/changes/15/338415/4
[2021-09-01T05:08:49.036Z] GERRIT_SCHEME=ssh
[2021-09-01T05:08:49.036Z] GERRIT_TOPIC=PFMProxy_Conf
[2021-09-01T05:08:49.036Z] GERRIT_VERSION=3.3.2
[2021-09-01T05:08:49.036Z] GHS_LMHOST=@rb-lic-ghs2.de.bosch.com,Rb-lic-ghs-cm-is.de.bosch.com
[2021-09-01T05:08:49.036Z] GHS_LMWHICH=ghs
[2021-09-01T05:08:49.036Z] GPOTestVar=0
[2021-09-01T05:08:49.036Z] HOME=C:\Users\<USER>\Program Files\RedHat\java-1.8.0-openjdk\
[2021-09-01T05:08:49.036Z] JENKINS_HOME=/var/jenkins_home
[2021-09-01T05:08:49.036Z] JENKINS_NODE_COOKIE=8771ad82-0f0b-47b6-91f7-452232f332d9
[2021-09-01T05:08:49.036Z] JENKINS_SERVER_COOKIE=durable-116a633f10f628b257c2dd9a535e5e54
[2021-09-01T05:08:49.036Z] JENKINS_URL=https://rb-jmaas.de.bosch.com/cm_build/
[2021-09-01T05:08:49.036Z] JFROG_CLI_LOG_LEVEL=ERROR
[2021-09-01T05:08:49.036Z] JOB_BASE_NAME=Git_platform_hydra_autosar_on-review
[2021-09-01T05:08:49.036Z] JOB_DISPLAY_URL=https://rb-jmaas.de.bosch.com/cm_build/job/Git_platform_hydra_autosar_on-review/display/redirect
[2021-09-01T05:08:49.036Z] JOB_NAME=Git_platform_hydra_autosar_on-review
[2021-09-01T05:08:49.036Z] JOB_URL=https://rb-jmaas.de.bosch.com/cm_build/job/Git_platform_hydra_autosar_on-review/
[2021-09-01T05:08:49.036Z] library.build_utilities.version=master
[2021-09-01T05:08:49.036Z] library.CmSharedLib.version=e5c2544a89197c56e4781dbcd813c967f6a95ea9
[2021-09-01T05:08:49.036Z] LOCALAPPDATA=C:\Users\<USER>\AppData\Local
[2021-09-01T05:08:49.036Z] MANIFEST=hydra.xml
[2021-09-01T05:08:49.036Z] MOZ_LEGACY_PROFILES=1
[2021-09-01T05:08:49.036Z] NEXTHINK=C:\Program Files\Nexthink\Collector
[2021-09-01T05:08:49.036Z] NODE_LABELS=Exec706_SI-ZENG06W010_4_134 buildslave_si-zeng06w010 swarm windows
[2021-09-01T05:08:49.036Z] NODE_NAME=Exec706_SI-ZENG06W010_4_134
[2021-09-01T05:08:49.036Z] NUMBER_OF_PROCESSORS=6
[2021-09-01T05:08:49.036Z] OneDrive=C:\Users\<USER>\OneDrive
[2021-09-01T05:08:49.036Z] OS=Windows_NT
[2021-09-01T05:08:49.036Z] Path=C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files (x86)\Toolbase Client;C:\WINDOWS\system32\config\systemprofile\.dnx\bin;C:\Program Files\Microsoft DNX\Dnvm\;C:\Program Files (x86)\Windows Kits\8.1\Windows Performance Toolkit\;C:\Program Files\Microsoft SQL Server\120\Tools\Binn\;C:\Program Files\Microsoft SQL Server\130\Tools\Binn\;C:\Tools\git-repo;C:\Tools\Python3;C:\Program Files\7-Zip;C:\Program Files\Git\bin;C:\cygwin\bin;C:\Program Files\Git\cmd;C:\Program Files\Anaconda3\condabin;C:\Program Files\RedHat\java-1.8.0-openjdk\missioncontrol\;C:\Program Files\RedHat\java-1.8.0-openjdk\webstart\;C:\Program Files\RedHat\java-1.8.0-openjdk\bin;C:\Program Files\RedHat\java-1.8.0-openjdk\jre\bin;C:\Program Files (x86)\RedHat\java-1.8.0-openjdk\webstart\;C:\Program Files (x86)\RedHat\java-1.8.0-openjdk\bin;C:\Program Files (x86)\RedHat\java-1.8.0-openjdk\jre\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps
[2021-09-01T05:08:49.036Z] PATHEXT=.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.CPL
[2021-09-01T05:08:49.036Z] pathToArtifactoryLib=C:/instr-cluster-tools
[2021-09-01T05:08:49.036Z] PROCESSOR_ARCHITECTURE=AMD64
[2021-09-01T05:08:49.036Z] PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 85 Stepping 4, GenuineIntel
[2021-09-01T05:08:49.036Z] PROCESSOR_LEVEL=6
[2021-09-01T05:08:49.036Z] PROCESSOR_REVISION=5504
[2021-09-01T05:08:49.036Z] ProgramData=C:\ProgramData
[2021-09-01T05:08:49.036Z] ProgramFiles=C:\Program Files
[2021-09-01T05:08:49.036Z] ProgramFiles(x86)=C:\Program Files (x86)
[2021-09-01T05:08:49.036Z] ProgramW6432=C:\Program Files
[2021-09-01T05:08:49.036Z] PROMPT=$P$G
[2021-09-01T05:08:49.036Z] PSModulePath=C:\Users\<USER>\Documents\WindowsPowerShell\Modules;C:\Program Files\WindowsPowerShell\Modules;C:\WINDOWS\system32\WindowsPowerShell\v1.0\Modules
[2021-09-01T05:08:49.036Z] PUBLIC=C:\Users\<USER>\PRQA\QAC-8.1.1-R\bin
[2021-09-01T05:08:49.036Z] QACCFG=C:\PRQA\QAC-8.1.1-R\bin\qac.cfg
[2021-09-01T05:08:49.036Z] QACHELPFILES=C:\PRQA\QAC-8.1.1-R\m2cm
[2021-09-01T05:08:49.036Z] QACOUTPATH=C:\QAC_outfiles
[2021-09-01T05:08:49.036Z] QACPATH=C:\PRQA\QAC-8.1.1-R
[2021-09-01T05:08:49.036Z] QACPPBIN=C:\PRQA\QACPP-3.0.1-R\bin
[2021-09-01T05:08:49.036Z] QACPPCFG=C:\PRQA\QACPP-3.0.1-R\bin\qacpp.cfg
[2021-09-01T05:08:49.036Z] QACPPHELPFILES=C:\PRQA\QACPP-3.0.1-R\mcpp
[2021-09-01T05:08:49.036Z] QACPPOUTPATH=C:\QAC_outfiles
[2021-09-01T05:08:49.036Z] QACPPPATH=C:\PRQA\QACPP-3.0.1-R
[2021-09-01T05:08:49.036Z] QNXLM_LICENSE_FILE=<EMAIL>
[2021-09-01T05:08:49.036Z] REDHAT_JAVA_HOME=C:\Program Files\RedHat\java-1.8.0-openjdk\
[2021-09-01T05:08:49.036Z] RUN_ARTIFACTS_DISPLAY_URL=https://rb-jmaas.de.bosch.com/cm_build/job/Git_platform_hydra_autosar_on-review/10085/display/redirect?page=artifacts
[2021-09-01T05:08:49.036Z] RUN_CHANGES_DISPLAY_URL=https://rb-jmaas.de.bosch.com/cm_build/job/Git_platform_hydra_autosar_on-review/10085/display/redirect?page=changes
[2021-09-01T05:08:49.036Z] RUN_DISPLAY_URL=https://rb-jmaas.de.bosch.com/cm_build/job/Git_platform_hydra_autosar_on-review/10085/display/redirect
[2021-09-01T05:08:49.036Z] RUN_GSS_UNITTEST=yes
[2021-09-01T05:08:49.036Z] RUN_KSS_UNITTEST=yes
[2021-09-01T05:08:49.036Z] RUN_TESTS_DISPLAY_URL=https://rb-jmaas.de.bosch.com/cm_build/job/Git_platform_hydra_autosar_on-review/10085/display/redirect?page=tests
[2021-09-01T05:08:49.036Z] SAPKM_USER_TEMP=C:\Users\<USER>\AppData\Local
[2021-09-01T05:08:49.036Z] STAGE_NAME=do preBuild actions
[2021-09-01T05:08:49.036Z] SystemDrive=C:
[2021-09-01T05:08:49.036Z] SystemRoot=C:\WINDOWS
[2021-09-01T05:08:49.036Z] TBCPROFILE_DIR=C:\toolbase\_profile
[2021-09-01T05:08:49.036Z] TBC_HOME=C:\Program Files (x86)\Toolbase Client
[2021-09-01T05:08:49.036Z] TB_HOMEDIR=C:\toolbase
[2021-09-01T05:08:49.036Z] TEMP=C:\Users\<USER>\AppData\Local\Temp
[2021-09-01T05:08:49.036Z] TMP=C:\Users\<USER>\AppData\Local\Temp
[2021-09-01T05:08:49.036Z] TOOL_GIT_BRANCH=master
[2021-09-01T05:08:49.036Z] TOOL_GIT_PATH=ssh://rbcm-gerrit.de.bosch.com:29418/clusterbase/tools/jenkins-helper-tools
[2021-09-01T05:08:49.036Z] UATDATA=C:\WINDOWS\CCM\UATData\D9F8C395-CAB8-491d-B8AC-179A1FE1BE77
[2021-09-01T05:08:49.036Z] USER=cma1lr
[2021-09-01T05:08:49.036Z] USERDNSDOMAIN=DE.BOSCH.COM
[2021-09-01T05:08:49.036Z] USERDOMAIN=DE
[2021-09-01T05:08:49.036Z] USERNAME=CMA1LR
[2021-09-01T05:08:49.036Z] USERPROFILE=C:\Users\<USER>\Program Files (x86)\Microsoft Visual Studio 14.0\Common7\Tools\
[2021-09-01T05:08:49.036Z] windir=C:\WINDOWS
[2021-09-01T05:08:49.036Z] WORKSPACE=c:/b/review_autosar
[2021-09-01T05:08:49.036Z] WORKSPACE_TMP=c:/b/review_autosar@tmp
[2021-09-01T05:08:49.080Z] [8mha:////4C68LBHYfDUBzBpx2A+W69Ku2KslPOi0YreBf3cV7W4OAAAApB+LCAAAAAAAAP9tjTEOwjAQBC9BFLSUPMJpQoWoaC03vMAkxtix7ox9Ial4EV/jD1hEomKL1c4283rDOic4UrLCGxwc5s6JGEZblpgoDddAk/B0ER1hpmCEMpOi3pwWVMQGllQ1rCRsDHaBskPLsJVeP3QTNNrmzKl8Bwm16+/whEoWM+vEX5gT7G5jnwl/nr8CmCND3bal9m38ADws4H6+AAAA[0m[Pipeline] pwd
[2021-09-01T05:08:49.107Z] [8mha:////4I33GMW57vOBWCKtjqMO2vadCSqCFYIwxurwqWXxrKREAAAApB+LCAAAAAAAAP9tjTEOwjAQBC9BFLSUPMJpkgpR0VppeIGJjXFi3Tn2haTiRXyNP2ARiYotVjvbzOsN2xThRNGK3uDgMHVOBD/ZvMRMcbh5mkVPV9ERJvJGtGZuSZvzii2xgTVFCRsJO4Odp+TQMuxlrx6q8gptdeGYv6OE0ukRnlDIbGYV+QtLhMN90onw5/krgCUwlHWdq2nCB30d+2e+AAAA[0m[Pipeline] echo
[2021-09-01T05:08:49.117Z] Installing rtcItemChecker.py ...
[2021-09-01T05:08:49.135Z] [8mha:////4ARdzijgJie/WKJ1HRDU2T/FZG3Yr5nYygJjZTjTEqCFAAAApB+LCAAAAAAAAP9tjTEOwjAQBC9BFLSUPOLSBBpEldZKwwtMbIwT687YDknFi/gaf8AiEhVbrHa2mdcb1jHAiYPBXtNgKXYWvRtNXjhxGK6OJ+z5gh1TZKex1VPLSjcLtpw0LClKWAnYaOocR0smwVb08iErJ8lU5xTydxRQWnWHJxQim5MM6QtzgN1tVJHp5/krgNknKOs61/7gP75O1ky+AAAA[0m[Pipeline] libraryResource
[2021-09-01T05:08:49.163Z] [8mha:////4CMKemnjfmXAGWe22ACGYB587+34TIa1CTbowa0/5bwxAAAApR+LCAAAAAAAAP9tjTEOwjAQBC9BFLSUPOLSBFEgqrRWGl5gYmOcWHfGdkgqXsTX+AMWkajYYrWzzbzesI4BThwM9poGS7Gz6N1o8sKJw3B1PGHPF+yYIjuNrZ5aVrpZsOWkYUlRwkrARlPnOFoyCbailw9ZOUmmOqeQv6OA0qo7PKEQ2ZxkSF+YA+xuo4pMP89fAcw+QVnXufYH/wH/f81VvgAAAA==[0m[Pipeline] writeFile
[2021-09-01T05:08:49.221Z] [8mha:////4Asw3DsRCPHZsZpCSC+mpw4lynHMqFKgBNFSdnWfGt5oAAAApR+LCAAAAAAAAP9tjTEOwjAQBC9BFLSUPOLSBAkJUaW10vACExvjxLoztkNS8SK+xh+wiETFFqudbeb1hnUMcOJgsNc0WIqdRe9GkxdOHIar4wl7vmDHFNlpbPXUstLNgi0nDUuKElYCNpo6x9GSSbAVvXzIykky1TmF/B0FlFbd4QmFyOYkQ/rCHGB3G1Vk+nn+CmD2Ccq6zrU/+A8wY1XSvgAAAA==[0m[Pipeline] bat
[2021-09-01T05:08:49.537Z] 
[2021-09-01T05:08:49.537Z] c:\b\review_autosar>C:\Tools\Python3\python.exe c:/b/review_autosar@tmp\rtcItemChecker.py 
[2021-09-01T05:08:49.799Z] ********************** YOUR COMMIT MSG **********************
[2021-09-01T05:08:49.799Z] [760571]Add configuration changes for PFMProxy
[2021-09-01T05:08:49.799Z] 
[2021-09-01T05:08:49.799Z] - Register PFMProxy as a supervised Entity to WdgM
[2021-09-01T05:08:49.799Z] - Added a callback function for failure in supervision
[2021-09-01T05:08:49.799Z] - Supervision cycle is set as 20 based on (200/10)ms
[2021-09-01T05:08:49.799Z] - Other Max ,Min Margin are with approx. values
[2021-09-01T05:08:49.799Z] - Memmap added in the csv file
[2021-09-01T05:08:49.799Z] 
[2021-09-01T05:08:49.799Z] RTC2-760571
[2021-09-01T05:08:49.799Z] 
[2021-09-01T05:08:49.799Z] Change-Id: Ie1042a8bc000ad7d69eba772c3cab30ec31ddad5
[2021-09-01T05:08:49.799Z] 
[2021-09-01T05:08:49.799Z] *************************************************************
[2021-09-01T05:08:49.799Z] RTC-Items: ['RTC2-760571']
[2021-09-01T05:08:49.799Z] SUCCESS: Commit message contains RTC-Item.
[2021-09-01T05:08:49.822Z] [8mha:////4M4xpvtI6EruDS9NLRJ8tpegBfTN2ntWi4z/bAAJTCJLAAAApR+LCAAAAAAAAP9tjTEOwjAQBC9BFLSUPOLShAIhqrRWGl5gYmOcWHfGdkgqXsTX+AMWkajYYrWzzbzesI4BThwM9poGS7Gz6N1o8sKJw3B1PGHPF+yYIjuNrZ5aVrpZsOWkYUlRwkrARlPnOFoyCbailw9ZOUmmOqeQv6OA0qo7PKEQ2ZxkSF+YA+xuo4pMP89fAcw+QVnXufYH/wFxUk7LvgAAAA==[0m[Pipeline] echo
[2021-09-01T05:08:49.840Z] ... RTC Item Check succeeded.
[2021-09-01T05:08:49.868Z] [8mha:////4I3csyAKSFN/4Sgjx/mvzcJR/AIVu0WllRkpxGWlfsJlAAAApR+LCAAAAAAAAP9tjTEOwjAQBC9BFLSUPMKhiGgQVVorDS8w8WGcWHfGdkgqXsTX+AMWkajYYrWzzbzesI4BThyM6JEGS7GzwrvR5CUmDsPV8SR6voiOKbJD0eLUssZmwZYTwpKihJWEDVLnOFoyCbayVw9VOUWmOqeQv6OE0uo7PKGQ2ZxUSF+YA+xuo45MP89fAcw+QVnXuQ57/wFhV8oYvgAAAA==[0m[Pipeline] echo
[2021-09-01T05:08:49.877Z] Starting CPU Monitoring ...
[2021-09-01T05:08:49.941Z] [8mha:////4CkKVJtN+e/JgjkVX5kUqm9JjpgT8ssxzKyW33CCIW1gAAAApR+LCAAAAAAAAP9tjTEOwjAQBC9BFLSUPMIRUkSDqNJaaXiBiQ/jxLoztkNS8SK+xh+wiETFFqudbeb1hnUMcOJgRI80WIqdFd6NJi8xcRiujifR80V0TJEdihanljU2C7acEJYUJawkbJA6x9GSSbCVvXqoyiky1TmF/B0llFbf4QmFzOakQvrCHGB3G3Vk+nn+CmD2Ccq6znXY+w8gZtEBvgAAAA==[0m[Pipeline] echo
[2021-09-01T05:08:49.952Z] Installing cpuBuildMonitoring.ps1 ...
[2021-09-01T05:08:49.972Z] [8mha:////4KDfLAtqlhXNqiNKQzSp2XDxx1lyEeMdH0FqNx3PpFoIAAAApR+LCAAAAAAAAP9tjTEOwjAQBC9BFLSUPMKRUESDqNJaaXiBiQ/jxLoztkNS8SK+xh+wiETFFqudbeb1hnUMcOJgRI80WIqdFd6NJi8xcRiujifR80V0TJEdihanljU2C7acEJYUJawkbJA6x9GSSbCVvXqoyiky1TmF/B0llFbf4QmFzOakQvrCHGB3G3Vk+nn+CmD2Ccq6znXY+w/jNfwqvgAAAA==[0m[Pipeline] libraryResource
[2021-09-01T05:08:49.999Z] [8mha:////4PHrtkhrmhfpGyckrbMXadNqcyoz94jXhvd0ZUVUlMccAAAApR+LCAAAAAAAAP9tjTEOwjAQBC9BFLSUPMIpiGgQVVorDS8w8WGcWHfGdkgqXsTX+AMWkajYYrWzzbzesI4BThyM6JEGS7GzwrvR5CUmDsPV8SR6voiOKbJD0eLUssZmwZYTwpKihJWEDVLnOFoyCbayVw9VOUWmOqeQv6OE0uo7PKGQ2ZxUSF+YA+xuo45MP89fAcw+QVnXuQ57/wGiBOczvgAAAA==[0m[Pipeline] writeFile
[2021-09-01T05:08:50.040Z] [8mha:////4MjBh0niLDikTYyk8SaCbFihg+T4F4QMIggit6Yu7Ah1AAAApB+LCAAAAAAAAP9tjTEOwjAQBC9BFLSUPMJpIhpERWu54QUmMcaOdWfsC0nFi/gaf8AiEhVbrHa2mdcb1jnBkZIV3uDgMHdOxDDassREabgGmoSni+gIMwUjlJkU9ea0oCI2sKSqYSVhY7ALlB1ahq30+qGboNE2Z07lO0ioXX+HJ1SymFkn/sKcYHcb+0z48/wVwBwZ6rYttW/jB2WSpny+AAAA[0m[Pipeline] bat
[2021-09-01T05:08:50.362Z] 
[2021-09-01T05:08:50.362Z] c:\b\review_autosar>PowerShell -File C:\Tools\CpuBuildMonitoring\cpuBuildMonitoring.ps1                                                     -Mode start                                                     -PerfLogs C:\temp\monitor 
[2021-09-01T05:08:50.936Z] 
[2021-09-01T05:08:50.936Z] 
[2021-09-01T05:08:50.936Z]     Directory: C:\temp
[2021-09-01T05:08:50.936Z] 
[2021-09-01T05:08:50.936Z] 
[2021-09-01T05:08:50.936Z] Mode                LastWriteTime         Length Name                                                                  
[2021-09-01T05:08:50.936Z] ----                -------------         ------ ----                                                                  
[2021-09-01T05:08:50.936Z] d-----       01.09.2021     07:08                monitor                                                               
[2021-09-01T05:08:50.936Z] d-----       01.09.2021     07:08                monitor                                                               
[2021-09-01T05:08:50.936Z] d-----       01.09.2021     07:08                monitor                                                               
[2021-09-01T05:08:50.936Z] d-----       01.09.2021     07:08                monitor                                                               
[2021-09-01T05:08:53.485Z] 
[2021-09-01T05:08:53.485Z] 
[2021-09-01T05:08:53.485Z]     Directory: C:\temp\monitor
[2021-09-01T05:08:53.485Z] 
[2021-09-01T05:08:53.485Z] 
[2021-09-01T05:08:53.485Z] Mode                LastWriteTime         Length Name                                                                  
[2021-09-01T05:08:53.485Z] ----                -------------         ------ ----                                                                  
[2021-09-01T05:08:53.485Z] -a----       01.09.2021     07:08              0 Git_platform_hydra_autosar_on-review-10085.txt                        
[2021-09-01T05:08:53.485Z] 
[2021-09-01T05:08:53.485Z] 
[2021-09-01T05:08:53.513Z] [8mha:////4PWSficG/LJeLjU9EUiqYcj0eMp4Arns7kvyI0MTWVbMAAAApB+LCAAAAAAAAP9tjTEOwjAQBC9BFLSUPOLSBBpEldZKwwtMbIwT687YDknFi/gaf8AiEhVbrHa2mdcb1jHAiYPBXtNgKXYWvRtNXjhxGK6OJ+z5gh1TZKex1VPLSjcLtpw0LClKWAnYaOocR0smwVb08iErJ8lU5xTydxRQWnWHJxQim5MM6QtzgN1tVJHp5/krgNknKOs612HvPySjvWW+AAAA[0m[Pipeline] echo
[2021-09-01T05:08:53.522Z] ... CPU Monitoring started!
[2021-09-01T05:08:53.550Z] [8mha:////4LW3RwSZz8ISacTBAOXYuLthM//kC6x3r4/ARqLfSewxAAAApB+LCAAAAAAAAP9tjTEOwjAQBC9BFLSUPMJpojSIitZKwwtMbIwT686xLyQVL+Jr/AGLSFRssdrZZl5v2KYIJ4pW9AYHh6lzIvjJ5iVmisPN0yx6uoqOMJE3ojVzS9qcV2yJDawpSthI2BnsPCWHlmEve/VQlVdoqwvH/B0llE6P8IRCZjOryF9YIhzuk06EP89fASyBoazrXE0TPufwkE6+AAAA[0m[Pipeline] echo
[2021-09-01T05:08:53.559Z] Verifying if Jenkins job name 'Git_platform_hydra_autosar_on-review' can be mapped to one build output share top level folder...
[2021-09-01T05:08:53.627Z] [8mha:////4EKHJHrWtwsyvfcdimyNzmx07jxDwkkzmKHp19AncKjCAAAApR+LCAAAAAAAAP9tjTEOwjAQBC9BFLSUPOLSRFAgqrRWGl5gYmOcWHfGdkgqXsTX+AMWkajYYrWzzbzesI4BThwM9poGS7Gz6N1o8sKJw3B1PGHPF+yYIjuNrZ5aVrpZsOWkYUlRwkrARlPnOFoyCbailw9ZOUmmOqeQv6OA0qo7PKEQ2ZxkSF+YA+xuo4pMP89fAcw+QVnXufYH/wGmwYtXvgAAAA==[0m[Pipeline] echo
[2021-09-01T05:08:53.636Z] Retrieving sub directories of directory "\\BOSCH.COM\DfsRB\DfsDE\DIV\CM\CI2\Software\BuildOutput\003" ...
[2021-09-01T05:08:53.655Z] [8mha:////4JiD6RT40h0EMSi1ko8xBnXQY5NtQd14M4/e9ATwYw5gAAAApR+LCAAAAAAAAP9tjTEOwjAQBC9BFLSUPOLSRAgJUaW10vACExvjxLoztkNS8SK+xh+wiETFFqudbeb1hnUMcOJgsNc0WIqdRe9GkxdOHIar4wl7vmDHFNlpbPXUstLNgi0nDUuKElYCNpo6x9GSSbAVvXzIykky1TmF/B0FlFbd4QmFyOYkQ/rCHGB3G1Vk+nn+CmD2Ccq6zrU/+A9p3RPQvgAAAA==[0m[Pipeline] bat
[2021-09-01T05:11:00.753Z] [8mha:////4P08waDQK28B7u61DFqKpJdruieuJDX5e1mvvqtMRotbAAAApR+LCAAAAAAAAP9tjTEOwjAQBC9BFLSUPOLSREgIUaW10vACExvjxLoztkNS8SK+xh+wiETFFqudbeb1hnUMcOJgsNc0WIqdRe9GkxdOHIar4wl7vmDHFNlpbPXUstLNgi0nDUuKElYCNpo6x9GSSbAVvXzIykky1TmF/B0FlFbd4QmFyOYkQ/rCHGB3G1Vk+nn+CmD2Ccq6zrU/+A8o7AjJvgAAAA==[0m[Pipeline] echo
[2021-09-01T05:11:00.764Z] ... done. Sub directories are: [.DeleteD.by.SyS, 3D_PoC_display, area51, CameraPlatform, CameraPlatform_Reference, CSWU_FCA_cluster, CSWU_SAIC_DMS, DaimlerBR214_display, DaimlerMMA_display, DisplayProductBuildKit, EBM_SW, FCA_cluster, FCA_DCSD_display, IMS_IMApp, pjims_OMS, Platform_Cerberus, platform_hydra, playground, PoC_3D_Display, SAIC_DMS, SoftwareEcoSystem_AgosenseSymphony, TFT_cluster, To_Be_Deleted]
[2021-09-01T05:11:00.794Z] [8mha:////4BSipG84PnpxtgzNJxb7THlD/iufteBQjwJS6vEuMf05AAAApR+LCAAAAAAAAP9tjTEOwjAQBC9BFLSUPMKhiESBqNJaaXiBiQ/jxLoztkNS8SK+xh+wiETFFqudbeb1hnUMcOJgRI80WIqdFd6NJi8xcRiujifR80V0TJEdihanljU2C7acEJYUJawkbJA6x9GSSbCVvXqoyiky1TmF/B0llFbf4QmFzOakQvrCHGB3G3Vk+nn+CmD2Ccq6znXY+w9WPQgZvgAAAA==[0m[Pipeline] echo
[2021-09-01T05:11:00.813Z] ... verification successful.
[2021-09-01T05:11:00.818Z] [8mha:////4L4d00Lk1px4UG6GPQpA08DYUz7Soo/I8F/G6epzyLIXAAAAph+LCAAAAAAAAP9tjTEOwjAQBC9BFLSUPMIRUiQKRJXWSsMLTHwYJ9adsR2SihfxNf6ARSQqtljtbDOvN6xjgBMHI3qkwVLsrPBuNHmJicNwdTyJni+iY4rsULQ4tayxWbDlhLCkKGElYYPUOY6WTIKt7NVDVU6Rqc4p5O8oobT6Dk8oZDYnFdIX5gC726gj08/zVwCz9wnKwz5XXX8AenF8xL4AAAA=[0m[Pipeline] }
[2021-09-01T05:11:00.848Z] [8mha:////4AlMZwvOG3PZyaoGyR3DKRvAyJ21Dwg708f6+1SCZnFlAAAAph+LCAAAAAAAAP9tjTEOwjAQBC9BFLSUPOIiARIFSkVrueEFJjHGiXUXbAen4kV8jT9gEYmKLVY728zrDcvgoWZvsNPUWwqNxcGNJi9M7Pur44QdX7BhCuw0Sp0kt/o0o+SoYU5RwkLASlPjOFgyEdaiUw9VOUWmOkefv6OA0rZ3eEIhsjkqH78wedjcxjYw/Tx/BTANQ4TysM21330Ad5aM3L4AAAA=[0m[Pipeline] // stage
[2021-09-01T05:11:00.928Z] [8mha:////4PKogLyrMT/HlxKpMCUr63zaM53TtiVzZbOBQ5dbCVfmAAAAqB+LCAAAAAAAAP9tjTEOwjAUQ3+LOrAycogUxMCAOrFGXThBaEJIGv1fkpR04kRcjTsQqMSEB8u2ZL3nC6rgoSGvmVXYGwydYYMbdU4ske8vjhKzdGYdYSCnWKtSS1Id59pSVDCrKGHBYamwcxQM6ggrbsVd1E6grk/R5+3AoTTyBg8oeCZH4eO3TB7W11EGwh/nLwCmIUK53WTb7z7P6g0wNajywgAAAA==[0m[Pipeline] stage
[2021-09-01T05:11:00.938Z] [8mha:////4NyaqFZVCTUefYy7lk9EaZzgkBwCEhoumHId8GUdjtj5AAAAqB+LCAAAAAAAAP9tjTEOwjAUQ3+LOrAycoh0AIkBMXWNsnCC0ISQNvq//KS0EyfiatyBlkpMeLBsS9Z7vaGIDCdiJxqLrcdYe9GF3k1JDMTtNdAgGrqImjBSsELZQZGx1VIVJQuLshxWEtYW60DRo0uwkY1+6DJodOU58bQdJeTe3OEJmZzISXP6lpFhe+tNJPxx/gJg7BLkh91s+/lZfABpxnKZwgAAAA==[0m[Pipeline] { (Download tool)
[2021-09-01T05:11:01.014Z] [8mha:////4M7U+bjpMp47BtpJvbTSMlCWgnTlbSJ9eQP3pW4opZfyAAAApx+LCAAAAAAAAP9tjTEOwjAUQ3+LOrAycoh0ATEgpq5RFk4QmhDSRv+Xn5R24kRcjTvQUokJD5ZtyXqvNxSR4UTsRGOx9RhrL7rQuymJgbi9BhpEQxdRE0YKVig7KDK2WqqiZGFRlsNKwtpiHSh6dAk2stEPXQaNrjwnnrajhNybOzwhkxM5aU7fMjJsb72JhD/OXwCMXYL8sJttPz+LD08AFXvCAAAA[0m[Pipeline] dir
[2021-09-01T05:11:01.015Z] Running in c:/b/review_autosar/jenkins-helper-tools
[2021-09-01T05:11:01.024Z] [8mha:////4GKifQdGNDcKQyqdo4cUajDyq/jec5/en1iZFS5zbhYcAAAApx+LCAAAAAAAAP9tjTEOwjAUQ3+LOrAycoh0AgbE1DXKwglCE0La6P/yk9JOnIircQdaKjHhwbItWe/1hiIynIidaCy2HmPtRRd6NyUxELfXQINo6CJqwkjBCmUHRcZWS1WULCzKclhJWFusA0WPLsFGNvqhy6DRlefE03aUkHtzhydkciInzelbRobtrTeR8Mf5C4CxS5AfdrPt52fxASGtDzzCAAAA[0m[Pipeline] {
[2021-09-01T05:11:01.063Z] [8mha:////4CzgCuHNTExJsLaMXq7xrOyjnjXtMvSjTYGdbriJTrj9AAAAox+LCAAAAAAAAP9tjTEOwjAQBC9BFLSUPOLSkQJR0VppeIFJjLFj3Tm2g1PxIr7GHwhEomKL1c4283zBOgY4ctBoFfWGYmvQu1HPCzOH/uo4o+ULtkyRncJG5YY7dVqw4aRgSVHCSsBGUes4GtIJtsLKu6ycJF2dU5i/g4DSdAM8oBCzOcmQvjAF2N3GLjL9PH8FMPkEZb3/VO3fW71ZT74AAAA=[0m[Pipeline] git
[2021-09-01T05:11:01.080Z] The recommended git tool is: NONE
[2021-09-01T05:11:03.125Z] No credentials specified
[2021-09-01T05:11:03.130Z] Cloning the remote Git repository
[2021-09-01T05:11:02.745Z] Cloning repository ssh://rbcm-gerrit.de.bosch.com:29418/clusterbase/tools/jenkins-helper-tools
[2021-09-01T05:11:02.813Z]  > git init c:\b\review_autosar\jenkins-helper-tools # timeout=10
[2021-09-01T05:11:03.067Z] Fetching upstream changes from ssh://rbcm-gerrit.de.bosch.com:29418/clusterbase/tools/jenkins-helper-tools
[2021-09-01T05:11:03.068Z]  > git --version # timeout=10
[2021-09-01T05:11:03.168Z]  > git --version # 'git version 2.31.1.windows.1'
[2021-09-01T05:11:03.169Z]  > git fetch --tags --force --progress -- ssh://rbcm-gerrit.de.bosch.com:29418/clusterbase/tools/jenkins-helper-tools +refs/heads/*:refs/remotes/origin/* # timeout=10
[2021-09-01T05:11:13.372Z] Avoid second fetch
[2021-09-01T05:11:13.492Z] Checking out Revision ea5d6ed5fd0aaefa763c9187887494f0a3c0f9d0 (refs/remotes/origin/master)
[2021-09-01T05:11:12.771Z]  > git config remote.origin.url ssh://rbcm-gerrit.de.bosch.com:29418/clusterbase/tools/jenkins-helper-tools # timeout=10
[2021-09-01T05:11:12.865Z]  > git config --add remote.origin.fetch +refs/heads/*:refs/remotes/origin/* # timeout=10
[2021-09-01T05:11:12.969Z]  > git rev-parse "refs/remotes/origin/master^{commit}" # timeout=10
[2021-09-01T05:11:13.094Z]  > git config core.sparsecheckout # timeout=10
[2021-09-01T05:11:13.189Z]  > git checkout -f ea5d6ed5fd0aaefa763c9187887494f0a3c0f9d0 # timeout=10
[2021-09-01T05:11:14.237Z]  > git branch -a -v --no-abbrev # timeout=10
[2021-09-01T05:11:14.338Z]  > git checkout -b master ea5d6ed5fd0aaefa763c9187887494f0a3c0f9d0 # timeout=10
[2021-09-01T05:11:16.205Z] Commit message: "Fix for finding parent id"
[2021-09-01T05:11:15.803Z]  > git rev-list --no-walk ea5d6ed5fd0aaefa763c9187887494f0a3c0f9d0 # timeout=10
[2021-09-01T05:11:16.465Z] [8mha:////4P8c7koNBDplyVIlyhRzU4Ba6+xXzyFY9tNL/sYvbrvWAAAApB+LCAAAAAAAAP9tjTEOwjAQBC9BFLSUPOLSARKiSmu54QUmNsaJdWdsh6TiRXyNPxCIRMUWq51t5vmCZYpw5GixNdQ5So3D4Hs7LRw4dhfPA7Z8xoYpsTcozSBZm3pGydnAnKKEhYCVocZzcmQzrEWr7qryimx1ynH6DgJKp2/wgEJM5qxi/sIYYXPtdWL6ef4KYAwhQ7nbf2r7Bl0SUCa+AAAA[0m[Pipeline] }
[2021-09-01T05:11:16.487Z] [8mha:////4It6g+/B5+xTCNDZyfVjX2ZZh7J9Rml5eFrOpakoLxpPAAAApB+LCAAAAAAAAP9tjTEOwjAQBC9BFLSUPOJSIYQQVVrLDS8wsTFOrDtjOyQVL+Jr/IFAJCq2WO1sM88XLFOEI0eLraHOUWocBt/baeHAsbt4HrDlMzZMib1BaQbJ2tQzSs4G5hQlLASsDDWekyObYS1adVeVV2SrU47TdxBQOn2DBxRiMmcV8xfGCJtrrxPTz/NXAGMIGcrd/lPbN0KQBXS+AAAA[0m[Pipeline] // dir
[2021-09-01T05:11:16.505Z] [8mha:////4CWiUK9Xam6jw6qeOYfWLkyZBG8B8hwzmAkBL3LWVH4EAAAAph+LCAAAAAAAAP9tjTEOwjAQBC9BFLSUPOJCgQQSSkVrueEFJjHGiXUXbAen4kV8jT9gEYmKLVY728zrDcvgoWZvsNPUWwqNxcGNJi9M7Pur44QdX7BhCuw0Sp0kt/o0o+SoYU5RwkLASlPjOFgyEdaiUw9VOUWmOkefv6OA0rZ3eEIhsjkqH78wedjcxjYw/Tx/BTANQ4TysM21330A9YFF0r4AAAA=[0m[Pipeline] }
[2021-09-01T05:11:16.533Z] [8mha:////4I63I6So/YwCjaG9CUe+i5pLgKiKCzXZW2tZjLiMDEYtAAAAph+LCAAAAAAAAP9tjTEOwjAQBC9BFLSUPOIiRAESSkVrueEFJjHGiXUXbAen4kV8jT9gEYmKLVY728zrDcvgoWZvsNPUWwqNxcGNJi9M7Pur44QdX7BhCuw0Sp0kt/o0o+SoYU5RwkLASlPjOFgyEdaiUw9VOUWmOkefv6OA0rZ3eEIhsjkqH78wedjcxjYw/Tx/BTANQ4TysM21330A88d9h74AAAA=[0m[Pipeline] // stage
[2021-09-01T05:11:16.569Z] [8mha:////4DyxOxc6Q4LK4qWRrTyucx3tPlfa+0M7b+cAaJ/m9OApAAAAqB+LCAAAAAAAAP9tjTEOwjAUQ3+LOrAycogUmJBQJ9aoCycITQhJo/9LkpJOnIircQcClZjwYNmWrPd8QRU8NOQ1swp7g6EzbHCjzokl8v3FUWKWzqwjDOQUa1VqSarjXFuKCmYVJSw4LBV2joJBHWHFrbiL2gnU9Sn6vB04lEbe4AEFz+QofPyWycP6OspA+OP8BcA0RCi3m2z73edZvQF8lrzPwgAAAA==[0m[Pipeline] stage
[2021-09-01T05:11:16.578Z] [8mha:////4DDLaIddbjUvWecw+iCuQgrjZupIPdAeWMFwJR8FoaZPAAAAqB+LCAAAAAAAAP9tjTEOwjAUQ3+LOrAycohUggUJMXWNsnCC0ISQNvq//KS0EyfiatyBlkpMeLBsS9Z7vaGIDCdiJxqLrcdYe9GF3k1JDMTtNdAgGrqImjBSsELZQZGx1VIVJQuLshxWEtYW60DRo0uwkY1+6DJodOU58bQdJeTe3OEJmZzISXP6lpFhe+tNJPxx/gJg7BLkh91s+/lZfACJxiXBwgAAAA==[0m[Pipeline] { (Check for No BoC)
[2021-09-01T05:11:16.621Z] [8mha:////4M+wve7jkGbMFMsuD/nu9nWN+UnBY3VgdaLee6JdfTrQAAAAqB+LCAAAAAAAAP9tjTEOwjAUQ3+LOrAycoh0gAEJMXWNsnCC0ISQNvq//KS0EyfiatyBlkpMeLBsS9Z7vaGIDCdiJxqLrcdYe9GF3k1JDMTtNdAgGrqImjBSsELZQZGx1VIVJQuLshxWEtYW60DRo0uwkY1+6DJodOU58bQdJeTe3OEJmZzISXP6lpFhe+tNJPxx/gJg7BLkh91s+/lZfADxKa4dwgAAAA==[0m[Pipeline] script
[2021-09-01T05:11:16.630Z] [8mha:////4Lul3qFehuox2e7D9WadpyO4MC41cHIalsPUn8AE0pyYAAAAqB+LCAAAAAAAAP9tjTEOwjAUQ3+LOrAycoh0AQkJMXWNsnCC0ISQNvq//KS0EyfiatyBlkpMeLBsS9Z7vaGIDCdiJxqLrcdYe9GF3k1JDMTtNdAgGrqImjBSsELZQZGx1VIVJQuLshxWEtYW60DRo0uwkY1+6DJodOU58bQdJeTe3OEJmZzISXP6lpFhe+tNJPxx/gJg7BLkh91s+/lZfADX78n/wgAAAA==[0m[Pipeline] {
[2021-09-01T05:11:16.669Z] [8mha:////4HzMsLK99y1qYs4y/gO4I/VWYJLhuKuZl2muo7vZCipQAAAApx+LCAAAAAAAAP9tjTEOwjAUQ3+LOrAycoh0AiEhpq5RFk4QmhDSRv+Xn5R24kRcjTvQUokJD5ZtyXqvNxSR4UTsRGOx9RhrL7rQuymJgbi9BhpEQxdRE0YKVig7KDK2WqqiZGFRlsNKwtpiHSh6dAk2stEPXQaNrjwnnrajhNybOzwhkxM5aU7fMjJsb72JhD/OXwCMXYL8sJttPz+LD7lC07jCAAAA[0m[Pipeline] dir
[2021-09-01T05:11:16.669Z] Running in c:/b/review_autosar
[2021-09-01T05:11:16.677Z] [8mha:////4CP+Dpmi48TkYVWA3n6koVnFQcXz99YbWoRxbrBcHv5JAAAApx+LCAAAAAAAAP9tjTEOwjAUQ3+LOrAycoh0AyTE1DXKwglCE0La6P/yk9JOnIircQdaKjHhwbItWe/1hiIynIidaCy2HmPtRRd6NyUxELfXQINo6CJqwkjBCmUHRcZWS1WULCzKclhJWFusA0WPLsFGNvqhy6DRlefE03aUkHtzhydkciInzelbRobtrTeR8Mf5C4CxS5AfdrPt52fxAR33berCAAAA[0m[Pipeline] {
[2021-09-01T05:11:16.778Z] [8mha:////4NRXr2zgF1imUOvOrJEXkD/Nwh8vgn5EL5/ftX0Bx4euAAAAox+LCAAAAAAAAP9tjTEOwjAQBC9BFLSUPOJSEglR0VppeIFJjLFj3Tm2g1PxIr7GHwhEomKL1c4283zBOgY4ctBoFfWGYmvQu1HPCzOH/uo4o+ULtkyRncJG5YY7dVqw4aRgSVHCSsBGUes4GtIJtsLKu6ycJF2dU5i/g4DSdAM8oBCzOcmQvjAF2N3GLjL9PH8FMPkEZb3/VO3fsf/KvL4AAAA=[0m[Pipeline] sh
[2021-09-01T05:11:17.442Z] + echo '[760571]Add configuration changes for PFMProxy
[2021-09-01T05:11:17.442Z] 
[2021-09-01T05:11:17.442Z] - Register PFMProxy as a supervised Entity to WdgM
[2021-09-01T05:11:17.442Z] - Added a callback function for failure in supervision
[2021-09-01T05:11:17.442Z] - Supervision cycle is set as 20 based on (200/10)ms
[2021-09-01T05:11:17.442Z] - Other Max ,Min Margin are with approx. values
[2021-09-01T05:11:17.442Z] - Memmap added in the csv file
[2021-09-01T05:11:17.442Z] 
[2021-09-01T05:11:17.442Z] RTC2-760571
[2021-09-01T05:11:17.442Z] 
[2021-09-01T05:11:17.442Z] Change-Id: Ie1042a8bc000ad7d69eba772c3cab30ec31ddad5
[2021-09-01T05:11:17.442Z] '
[2021-09-01T05:11:17.442Z] + tr -d ' '
[2021-09-01T05:11:17.465Z] [8mha:////4BZahI7PhXTxY8e882HKjFk2pemYqqIuNSyhUFPQUMgpAAAAox+LCAAAAAAAAP9tjTEOwjAQBC9BFLSUPOJSAkJUaS03vMDExjix7oztkFS8iK/xBwKRqNhitbPNPF+wTBGOHC22hjpHqXEYfG+nhQPH7uJ5wJbP2DAl9galGSRrU88oORuYU5SwELAy1HhOjmyGtWjVXVVeka1OOU7fQUDp9A0eUIjJnFXMXxgjbK69Tkw/z18BjCFDudt+ah/e8M7Rpb4AAAA=[0m[Pipeline] echo
[2021-09-01T05:11:17.474Z] NoBoC flag not found, continue building...
[2021-09-01T05:11:17.483Z] [8mha:////4J/71H0KaTuFIMq47fBR5V2nX+dNuufz9qXV1wTDL28UAAAApB+LCAAAAAAAAP9tjTEOwjAQBC9BFLSUPOJCBwilorXc8AKTGOPEugu2g1PxIr7GH7CIRMUWq51t5vWGZfBQszfYaeothcbi4EaTFyb2/dVxwo4v2DAFdhqlTpJbfZpRctQwpyhhIWClqXEcLJkIa9Gph6qcIlOdo8/fUUBp2zs8oRDZHJWPX5g8bG5jG5h+nr8CmIYhQnnY5trvPjTHo2q+AAAA[0m[Pipeline] }
[2021-09-01T05:11:17.503Z] [8mha:////4GAPktebKhf3X48VjVgTvx/NeG/8kpF8HN1r3GWPEr2IAAAApR+LCAAAAAAAAP9tjTEOwjAQBC9BFLSUPOIiGgRCqWgtN7zAJMY4se6C7eBUvIiv8QcsIlGxxWpnm3m9YRk81OwNdpp6S6GxOLjR5IWJfX91nLDjCzZMgZ1GqZPkVp9mlBw1zClKWAhYaWocB0smwlp06qEqp8hU5+jzdxRQ2vYOTyhENkfl4xcmD5vb2Aamn+evAKZhiFAetrn2uw8HJPjWvgAAAA==[0m[Pipeline] // dir
[2021-09-01T05:11:17.521Z] [8mha:////4LwnfHWtsjBCMstc3L3s0MyTQA5eTSkPVLkvk9fc9cFWAAAAph+LCAAAAAAAAP9tjTEOwjAQBC9BFLSUPOIiISGBUCpayw0vMIkxTqy7YDs4FS/ia/wBi0hUbLHa2WZeb1gGDzV7g52m3lJoLA5uNHlhYt9fHSfs+IINU2CnUeokudWnGSVHDXOKEhYCVpoax8GSibAWnXqoyiky1Tn6/B0FlLa9wxMKkc1R+fiFycPmNraB6ef5K4BpGCKUh22u/e4DEwdlyb4AAAA=[0m[Pipeline] }
[2021-09-01T05:11:17.541Z] [8mha:////4JK3MSD+yS1qW9X2wz6ODjMUlgOQJ4LAY9zqg4psDDhxAAAApR+LCAAAAAAAAP9tjTEOwjAQBC9BFLSUPOJSQAFCqWgtN7zAJMY4se6C7eBUvIiv8QcsIlGxxWpnm3m9YRk81OwNdpp6S6GxOLjR5IWJfX91nLDjCzZMgZ1GqZPkVp9mlBw1zClKWAhYaWocB0smwlp06qEqp8hU5+jzdxRQ2vYOTyhENkfl4xcmD5vb2Aamn+evAKZhiFAetrn2uw8g5D51vgAAAA==[0m[Pipeline] // script
[2021-09-01T05:11:17.560Z] [8mha:////4JweGav5ysRkFMA3L3H3qRlYhVLhHLEXKhp8XnmPDON8AAAApR+LCAAAAAAAAP9tjTEOwjAQBC9BFLSUPOJSQAFCqWgtN7zAJMY4se6C7eBUvIiv8QcsIlGxxWpnm3m9YRk81OwNdpp6S6GxOLjR5IWJfX91nLDjCzZMgZ1GqZPkVp9mlBw1zClKWAhYaWocB0smwlp06qEqp8hU5+jzdxRQ2vYOTyhENkfl4xcmD5vb2Aamn+evAKZhiFAedrn22w87QV/2vgAAAA==[0m[Pipeline] }
[2021-09-01T05:11:17.588Z] [8mha:////4ISmw9afpCUWd58+7PLnHkI/GsH6f7Wcd4Y4Pvo+LwwAAAAAph+LCAAAAAAAAP9tjTEOwjAQBC9BFLSUPOIiISGBUCpayw0vMIkxTqy7YDs4FS/ia/wBi0hUbLHa2WZeb1gGDzV7g52m3lJoLA5uNHlhYt9fHSfs+IINU2CnUeokudWnGSVHDXOKEhYCVpoax8GSibAWnXqoyiky1Tn6/B0FlLa9wxMKkc1R+fiFycPmNraB6ef5K4BpGCKUh12u/fYDCKIESr4AAAA=[0m[Pipeline] // stage
[2021-09-01T05:11:17.625Z] [8mha:////4BOCTPQ4+2Z040dEhRZngjqVwaaKelhCCPWpldyArSuAAAAAqB+LCAAAAAAAAP9tjTEOwjAUQ3+LOrAycogUFiSEOrFGXThBaEJIGv1fkpR04kRcjTsQqMSEB8u2ZL3nC6rgoSGvmVXYGwydYYMbdU4ske8vjhKzdGYdYSCnWKtSS1Id59pSVDCrKGHBYamwcxQM6ggrbsVd1E6grk/R5+3AoTTyBg8oeCZH4eO3TB7W11EGwh/nLwCmIUK53WTb7z7P6g3e31rywgAAAA==[0m[Pipeline] stage
[2021-09-01T05:11:17.635Z] [8mha:////4DypKDW83wuYjLBd643t80VYqYZLFbxoW5Pd1yhvoOmHAAAApx+LCAAAAAAAAP9tjTEOwjAUQ3+LOrAycoh0AyHE1DXKwglCE0La6P/yk9JOnIircQdaKjHhwbItWe/1hiIynIidaCy2HmPtRRd6NyUxELfXQINo6CJqwkjBCmUHRcZWS1WULCzKclhJWFusA0WPLsFGNvqhy6DRlefE03aUkHtzhydkciInzelbRobtrTeR8Mf5C4CxS5AfdrPt52fxAcawv9fCAAAA[0m[Pipeline] { (Repo Sync)
[2021-09-01T05:11:17.674Z] [8mha:////4DQFU2+CP/KWOtwnfFKhmLjHwnIHr5Gj0aumTCfbocNRAAAAph+LCAAAAAAAAP9tjTEOwjAUQ3+LOrAycoh0BISYukZZOEFoQkgb/V9+UtqJE3E17kBLJSY8WLYl673eUESGE7ETjcXWY6y96ELvpiQG4vYaaBANXURNGClYoeygyNhqqYqShUVZDisJa4t1oOjRJdjIRj90GTS68px42o4Scm/u8IRMTuSkOX3LyLC99SYS/jh/ATB2CfLDbrb9/Cw+091n58IAAAA=[0m[Pipeline] script
[2021-09-01T05:11:17.683Z] [8mha:////4AdH7D0csCS7+QAgXvMI05C/eDZ0gZpui2t6nmOUGeTzAAAAph+LCAAAAAAAAP9tjTEOwjAUQ3+DOrAycoh0pUJMrFEXThCaEJJG/7dJSjpxIq7GHWipxIQHy7ZkvdcbyhjgRMFwp7GzGFvLez+aOfFMobt5ytzRlbeEkbzmjc4NKX1ea0NJw6qCwUbAVmPrKVo0CXbCyYesvERTXVKYt6MAZtUATyjETE4ypG+ZAuzvo4qEP85fAEx9AlYfFquXZ/kBsPrDv8IAAAA=[0m[Pipeline] {
[2021-09-01T05:11:17.729Z] [8mha:////4LqoDotKrjSa/pk9rvdYNHXG23dS2lFrcKVrf52F4yvUAAAApx+LCAAAAAAAAP9tjbEOgjAURR8QBldHP6LgSIwTa8PiF9RSa6F5D9uHMPlF/pr/IEri5J3uucnNeb4gjwGOFKzoDPYOo3Zi8KNdmpgo9BdPk+joLDRhJG9EY6aGWlOv2BAbWJOkkEnYGNSeokPLsJWduqvCK7TFicOyHSSkrr3BAxK5mFkF/sIcYHcd20j48/wVwDwwpFXFkO3L8nPN3zgbWwvDAAAA[0m[Pipeline] retry
[2021-09-01T05:11:17.739Z] [8mha:////4OZLbm8G8huQsGeckrLeNIJATpPOB6MR7h+WsRmBquPzAAAAph+LCAAAAAAAAP9tjbEOgjAYhH8gDK6OPkSB2Ti5Nl18gkprbWn+H9oiTD6Rr+Y7iJA4ecPl7pLL93pDGQOcKBjmNHYWY2tZ70ezJDZR6G6eJuboylrCSF4zoSdBSp+3Kihp2JTlUHDYaWw9RYsmwZ47+ZCVl2iqSwrLduSQWzXAEzK+kJMMaS1zgMN9VJHwx/kLgLlPUDR1vXrz/ZYfJWyAvsQAAAA=[0m[Pipeline] {
[2021-09-01T05:11:17.767Z] [8mha:////4Mu9Zxcw1vHHawaCXlFcp0EB2lC3WfdnnFNqVzwicbDPAAAApB+LCAAAAAAAAP9tjTESgjAQRRcYC1tLD7GIrWNFm0njCWKIMZDZxSQIlSfyat5BBmas/MWf/37z3h/YxABnDhZbQ52jqB32frDzwpFDd/M8YstX1EyRvUFpRsmNqVeUnAysyXIoBGwNac/RkU2wE616qtIrsuUlhfk7Cchd84AXZGI2JxXSAlOA/X1oItPP81cAU5+gqA7V0sf+C6iwe5rAAAAA[0m[Pipeline] bat
[2021-09-01T05:11:18.107Z] [8mha:////4GvSOKKxzuI+q+MpwRGwOxiJFc9/9uTVRNFZ1nKvGkvCAAAApB+LCAAAAAAAAP9tjTESgjAQRRcYC1tLD7HIWDpWtJk0niCGGAOZXUyCUHkir+YdZGDGyl/8+e837/2BTQxw5mCxNdQ5itph7wc7Lxw5dDfPI7Z8Rc0U2RuUZpTcmHpFycnAmiyHQsDWkPYcHdkEO9Gqpyq9IlteUpi/k4DcNQ94QSZmc1IhLTAF2N+HJjL9PH8FMPUJiupQLX3sv+mBYIPAAAAA[0m[Pipeline] bat
[2021-09-01T05:11:18.418Z] 
[2021-09-01T05:11:18.418Z] c:\b\review_autosar>repo init --no-repo-verify -u ssh://rbcm-gerrit.de.bosch.com:29418/clusterbase/manifest -g autosar -b platform-develop-qnx -m hydra.xml 
[2021-09-01T05:11:20.336Z] repo: warning: verification of repo code has been disabled;
[2021-09-01T05:11:20.336Z] repo will not be able to verify the integrity of itself.
[2021-09-01T05:11:20.336Z] 
[2021-09-01T05:11:21.284Z] Downloading manifest from ssh://<EMAIL>:29418/clusterbase/manifest
[2021-09-01T05:11:22.675Z] Total 5518 (delta 0), reused 5476 (delta 0)
[2021-09-01T05:11:26.935Z] 
[2021-09-01T05:11:26.935Z] repo has been initialized in c:\b\review_autosar
[2021-09-01T05:11:26.935Z] Downloading Repo source from C:\Tools\git-repo\.git
[2021-09-01T05:11:26.980Z] [8mha:////4JdZwAUYIrGCsVmxnLd7xVsPVEb7wt9CpR3NdHzkRmDJAAAApR+LCAAAAAAAAP9tjbEOgjAURR8QB1dHP+IhiZtxYm26+AW11Fpo3sO2CJNf5K/5DxJInLzDzT13Oe8PbGKAMweLraHOUdQOez/YeeHIobt5HrHlK2qmyN6gNKPkxtQrSk4G1mQ5FAK2hrTn6Mgm2IlWPVXpFdnyksL8nQTkrnnACzIxm5MKaYEpwP4+NJHp5/krgKlPUFSHaulj/wUuFyHMwAAAAA==[0m[Pipeline] bat
[2021-09-01T05:11:27.442Z] [8mha:////4Nho0OlMKg2ZPlai736w5MN3GhbzNiX2vEePF6vl559RAAAApR+LCAAAAAAAAP9tjTESgjAQRRcYC1tLD7FIYeVY0WbSeIIYYgxkdjEJQuWJvJp3kIEZK3/x57/fvPcHNjHAmYPF1lDnKGqHvR/svHDk0N08j9jyFTVTZG9QmlFyY+oVJScDa7IcCgFbQ9pzdGQT7ESrnqr0imx5SWH+TgJy1zzgBZmYzUmFtMAUYH8fmsj08/wVwNQnKKpDtfSx/wJvJjrVwAAAAA==[0m[Pipeline] bat
[2021-09-01T05:11:27.845Z] 
[2021-09-01T05:11:27.845Z] c:\b\review_autosar>repo forall -c git reset --hard 
[2021-09-01T05:11:28.899Z] [8mha:////4BfQrqcC6OeiblW46ClDseYnGivDYL2aIKbH5moiS50rAAAApR+LCAAAAAAAAP9tjTESgjAQRRcYC1tLD7FIY+NY0WbSeIIYYgxkdjEJQuWJvJp3kIEZK3/x57/fvPcHNjHAmYPF1lDnKGqHvR/svHDk0N08j9jyFTVTZG9QmlFyY+oVJScDa7IcCgFbQ9pzdGQT7ESrnqr0imx5SWH+TgJy1zzgBZmYzUmFtMAUYH8fmsj08/wVwNQnKKpDtfSx/wKsdRf+wAAAAA==[0m[Pipeline] bat
[2021-09-01T05:11:29.754Z] [8mha:////4GOkyUCms1XArMBi2Kkttonlam2Hp7qZQDV0CY50r6ciAAAApR+LCAAAAAAAAP9tjTESgjAQRRcYC1tLD7FIZeFY0WbSeIIYYgxkdjEJQuWJvJp3kIEZK3/x57/fvPcHNjHAmYPF1lDnKGqHvR/svHDk0N08j9jyFTVTZG9QmlFyY+oVJScDa7IcCgFbQ9pzdGQT7ESrnqr0imx5SWH+TgJy1zzgBZmYzUmFtMAUYH8fmsj08/wVwNQnKKpDtfSx/wLtRAznwAAAAA==[0m[Pipeline] bat
[2021-09-01T05:11:30.075Z] 
[2021-09-01T05:11:30.075Z] c:\b\review_autosar>repo forall -c git clean -fdx 
[2021-09-01T05:11:31.088Z] [8mha:////4P2OdXAUgjxn35SAfz52iu0VmoEO2C4K8FAlP7+K4sz4AAAApR+LCAAAAAAAAP9tjbEOgjAURR8QB1dHP+Ihm4lxYm26+AW11Fpo3sO2CJNf5K/5DxJInLzDzT13Oe8PbGKAMweLraHOUdQOez/YeeHIobt5HrHlK2qmyN6gNKPkxtQrSk4G1mQ5FAK2hrTn6Mgm2IlWPVXpFdnyksL8nQTkrnnACzIxm5MKaYEpwP4+NJHp5/krgKlPUFSHaulj/wUiWJRgwAAAAA==[0m[Pipeline] bat
[2021-09-01T05:11:31.420Z] [8mha:////4P8ipTbeIINgiBHeQK2AOvG4J2i4OK+Zl7yAY7iEjAnVAAAApB+LCAAAAAAAAP9tjTESgjAQRRcYC1tLD7FI6ThWtJk0niCGGAOZXUyCUHkir+YdZGDGyl/8+e837/2BTQxw5mCxNdQ5itph7wc7Lxw5dDfPI7Z8Rc0U2RuUZpTcmHpFycnAmiyHQsDWkPYcHdkEO9Gqpyq9IlteUpi/k4DcNQ94QSZmc1IhLTAF2N+HJjL9PH8FMPUJiupQLX3sv2Npj3nAAAAA[0m[Pipeline] bat
[2021-09-01T05:11:31.729Z] 
[2021-09-01T05:11:31.729Z] c:\b\review_autosar>repo sync -d -c -q --force-sync 
[2021-09-01T05:13:53.509Z] Updating files:  25% (812/3128)
Updating files:  26% (814/3128)
Updating files:  27% (845/3128)
Updating files:  28% (876/3128)
Updating files:  29% (908/3128)
Updating files:  30% (939/3128)
Updating files:  31% (970/3128)
Updating files:  32% (1001/3128)
Updating files:  33% (1033/3128)
Updating files:  34% (1064/3128)
Updating files:  35% (1095/3128)
Updating files:  36% (1127/3128)
Updating files:  37% (1158/3128)
Updating files:  38% (1189/3128)
Updating files:  39% (1220/3128)
Updating files:  40% (1252/3128)
Updating files:  41% (1283/3128)
Updating files:  42% (1314/3128)
Updating files:  43% (1346/3128)
Updating files:  43% (1373/3128)
Updating files:  44% (1377/3128)
Updating files:  68% (972/1414)
Updating files:  45% (1408/3128)
Updating files:  69% (976/1414)
Updating files:  46% (1439/3128)
Updating files:  70% (990/1414)
Updating files:  47% (1471/3128)
Updating files:  71% (1004/1414)
Updating files:  72% (1019/1414)
Updating files:  48% (1502/3128)
Updating files:  73% (1033/1414)
Updating files:  74% (1047/1414)
Updating files:  75% (1061/1414)
Updating files:  49% (1533/3128)
Updating files:  76% (1075/1414)
Updating files:  50% (1564/3128)
Updating files:  77% (1089/1414)
Updating files:  78% (1103/1414)
Updating files:  51% (1596/3128)
Updating files:  79% (1118/1414)
Updating files:  80% (1132/1414)
Updating files:  81% (1146/1414)
Updating files:  82% (1160/1414)
Updating files:  83% (1174/1414)
Updating files:  84% (1188/1414)
Updating files:  85% (1202/1414)
Updating files:  86% (1217/1414)
Updating files:  87% (1231/1414)
Updating files:  88% (1245/1414)
Updating files:  89% (1259/1414)
Updating files:  90% (1273/1414)
Updating files:  91% (1287/1414)
Updating files:  92% (1301/1414)
Updating files:  93% (1316/1414)
Updating files:  94% (1330/1414)
Updating files:  95% (1344/1414)
Updating files:  96% (1358/1414)
Updating files:  97% (1372/1414)
Updating files:  98% (1386/1414)
Updating files:  99% (1400/1414)
Updating files: 100% (1414/1414)
Updating files: 100% (1414/1414), done.
[2021-09-01T05:13:53.509Z] Updating files:  52% (1627/3128)
Updating files:  52% (1644/3128)
Updating files:  53% (1658/3128)
Updating files:  54% (1690/3128)
Updating files:  55% (1721/3128)
Updating files:  34% (649/1856)
Updating files:  35% (650/1856)
Updating files:  56% (1752/3128)
Updating files:  36% (669/1856)
Updating files:  37% (687/1856)
Updating files:  38% (706/1856)
Updating files:  56% (1774/3128)
Updating files:  39% (724/1856)
Updating files:  40% (743/1856)
Updating files:  41% (761/1856)
Updating files:  42% (780/1856)
Updating files:  57% (1783/3128)
Updating files:  43% (799/1856)
Updating files:  44% (817/1856)
Updating files:  45% (836/1856)
Updating files:  58% (1815/3128)
Updating files:  46% (854/1856)
Updating files:  47% (873/1856)
Updating files:  59% (1846/3128)
Updating files:  48% (891/1856)
Updating files:  49% (910/1856)
Updating files:  50% (928/1856)
Updating files:  51% (947/1856)
Updating files:  60% (1877/3128)
Updating files:  52% (966/1856)
Updating files:  53% (984/1856)
Updating files:  54% (1003/1856)
Updating files:  61% (1909/3128)
Updating files:  55% (1021/1856)
Updating files:  56% (1040/1856)
Updating files:  57% (1058/1856)
Updating files:  58% (1077/1856)
Updating files:  59% (1096/1856)
Updating files:  60% (1114/1856)
Updating files:  61% (1133/1856)
Updating files:  62% (1151/1856)
Updating files:  63% (1170/1856)
Updating files:  63% (1172/1856)
Updating files:  61% (1939/3128)
Updating files:  62% (1940/3128)
Updating files:  64% (1188/1856)
Updating files:  65% (1207/1856)
Updating files:  66% (1225/1856)
Updating files:  63% (1971/3128)
Updating files:  67% (1244/1856)
Updating files:  68% (1263/1856)
Updating files:  69% (1281/1856)
Updating files:  64% (2002/3128)
Updating files:  70% (1300/1856)
Updating files:  71% (1318/1856)
Updating files:  72% (1337/1856)
Updating files:  73% (1355/1856)
Updating files:  74% (1374/1856)
Updating files:  75% (1392/1856)
Updating files:  76% (1411/1856)
Updating files:  77% (1430/1856)
Updating files:  78% (1448/1856)
Updating files:  65% (2034/3128)
Updating files:  79% (1467/1856)
Updating files:  80% (1485/1856)
Updating files:  81% (1504/1856)
Updating files:  81% (1511/1856)
Updating files:  66% (2065/3128)
Updating files:  82% (1522/1856)
Updating files:  67% (2096/3128)
Updating files:  83% (1541/1856)
Updating files:  68% (2128/3128)
Updating files:  84% (1560/1856)
Updating files:  68% (2144/3128)
Updating files:  85% (1578/1856)
Updating files:  86% (1597/1856)
Updating files:  87% (1615/1856)
Updating files:  69% (2159/3128)
Updating files:  88% (1634/1856)
Updating files:  70% (2190/3128)
Updating files:  89% (1652/1856)
Updating files:  90% (1671/1856)
Updating files:  91% (1689/1856)
Updating files:  92% (1708/1856)
Updating files:  71% (2221/3128)
Updating files:  93% (1727/1856)
Updating files:  94% (1745/1856)
Updating files:  95% (1764/1856)
Updating files:  96% (1782/1856)
Updating files:  97% (1801/1856)
Updating files:  72% (2253/3128)
Updating files:  98% (1819/1856)
Updating files:  99% (1838/1856)
Updating files: 100% (1856/1856)
Updating files: 100% (1856/1856), done.
[2021-09-01T05:13:53.509Z] Updating files:  73% (2284/3128)
Updating files:  74% (2315/3128)
Updating files:  74% (2345/3128)
Updating files:  75% (2346/3128)
Updating files:  76% (2378/3128)
Updating files:  77% (2409/3128)
Updating files:  78% (2440/3128)
Updating files:  79% (2472/3128)
Updating files:  79% (2498/3128)
Updating files:  80% (2503/3128)
Updating files:  30% (274/912)
Updating files:  31% (283/912)
Updating files:  32% (292/912)
Updating files:  81% (2534/3128)
Updating files:  33% (301/912)
Updating files:  34% (311/912)
Updating files:  35% (320/912)
Updating files:  36% (329/912)
Updating files:  37% (338/912)
Updating files:  82% (2565/3128)
Updating files:  38% (347/912)
Updating files:  39% (356/912)
Updating files:  40% (365/912)
Updating files:  83% (2597/3128)
Updating files:  41% (374/912)
Updating files:  42% (384/912)
Updating files:  84% (2628/3128)
Updating files:  43% (393/912)
Updating files:  44% (402/912)
Updating files:  45% (411/912)
Updating files:  85% (2659/3128)
Updating files:  46% (420/912)
Updating files:  47% (429/912)
Updating files:  86% (2691/3128)
Updating files:  48% (438/912)
Updating files:  49% (447/912)
Updating files:  86% (2717/3128)
Updating files:  87% (2722/3128)
Updating files:  50% (456/912)
Updating files:  51% (466/912)
Updating files:  88% (2753/3128)
Updating files:  52% (475/912)
Updating files:  53% (484/912)
Updating files:  89% (2784/3128)
Updating files:  54% (493/912)
Updating files:  55% (502/912)
Updating files:  90% (2816/3128)
Updating files:  56% (511/912)
Updating files:  57% (520/912)
Updating files:  58% (529/912)
Updating files:  91% (2847/3128)
Updating files:  59% (539/912)
Updating files:  60% (548/912)
Updating files:  63% (266/422)
Updating files:  92% (2878/3128)
Updating files:  64% (271/422)
Updating files:  65% (275/422)
Updating files:  61% (557/912)
Updating files:  66% (279/422)
Updating files:  67% (283/422)
Updating files:  68% (287/422)
Updating files:  62% (566/912)
Updating files:  69% (292/422)
Updating files:  93% (2910/3128)
Updating files:  70% (296/422)
Updating files:  63% (575/912)
Updating files:  71% (300/422)
Updating files:  72% (304/422)
Updating files:  73% (309/422)
Updating files:  64% (584/912)
Updating files:  74% (313/422)
Updating files:  75% (317/422)
Updating files:  76% (321/422)
Updating files:  65% (593/912)
Updating files:  77% (325/422)
Updating files:  94% (2941/3128)
Updating files:  78% (330/422)
Updating files:  79% (334/422)
Updating files:  66% (602/912)
Updating files:  80% (338/422)
Updating files:  81% (342/422)
Updating files:  67% (612/912)
Updating files:  82% (347/422)
Updating files:  83% (351/422)
Updating files:  67% (619/912)
Updating files:  84% (355/422)
Updating files:  68% (621/912)
Updating files:  85% (359/422)
Updating files:  86% (363/422)
Updating files:  87% (368/422)
Updating files:  69% (630/912)
Updating files:  88% (372/422)
Updating files:  89% (376/422)
Updating files:  70% (639/912)
Updating files:  90% (380/422)
Updating files:  91% (385/422)
Updating files:  71% (648/912)
Updating files:  92% (389/422)
Updating files:  93% (393/422)
Updating files:  94% (397/422)
Updating files:  72% (657/912)
Updating files:  95% (401/422)
Updating files:  96% (406/422)
Updating files:  97% (410/422)
Updating files:  98% (414/422)
Updating files:  73% (666/912)
Updating files:  99% (418/422)
Updating files: 100% (422/422)
Updating files: 100% (422/422), done.
[2021-09-01T05:13:53.509Z] Updating files:  74% (675/912)
Updating files:  75% (684/912)
Updating files:  76% (694/912)
Updating files:  77% (703/912)
Updating files:  78% (712/912)
Updating files:  79% (721/912)
Updating files:  80% (730/912)
Updating files:  81% (739/912)
Updating files:  82% (748/912)
Updating files:  83% (757/912)
Updating files:  84% (767/912)
Updating files:  85% (776/912)
Updating files:  86% (785/912)
Updating files:  87% (794/912)
Updating files:  88% (803/912)
Updating files:  89% (812/912)
Updating files:  90% (821/912)
Updating files:  91% (830/912)
Updating files:  92% (840/912)
Updating files:  93% (849/912)
Updating files:  94% (858/912)
Updating files:  95% (867/912)
Updating files:  96% (876/912)
Updating files:  97% (885/912)
Updating files:  98% (894/912)
Updating files:  99% (903/912)
Updating files: 100% (912/912)
Updating files: 100% (912/912), done.
[2021-09-01T05:13:53.509Z] Updating files:  94% (2963/3128)
Updating files:  95% (2972/3128)
Updating files:  96% (3003/3128)
Updating files:  97% (3035/3128)
Updating files:  98% (3066/3128)
Updating files:  98% (3078/3128)
Updating files:  99% (3097/3128)
Updating files: 100% (3128/3128)
Updating files: 100% (3128/3128), done.
[2021-09-01T05:14:05.968Z] Updating files:  18% (618/3264)
Updating files:  19% (621/3264)
Updating files:  20% (653/3264)
Updating files:  21% (686/3264)
Updating files:  22% (719/3264)
Updating files:  23% (751/3264)
Updating files:  24% (784/3264)
Updating files:  25% (816/3264)
Updating files:  26% (849/3264)
Updating files:  27% (882/3264)
Updating files:  28% (914/3264)
Updating files:  29% (947/3264)
Updating files:  30% (980/3264)
Updating files:  31% (1012/3264)
Updating files:  32% (1045/3264)
Updating files:  33% (1078/3264)
Updating files:  34% (1110/3264)
Updating files:  35% (1143/3264)
Updating files:  36% (1176/3264)
Updating files:  37% (1208/3264)
Updating files:  38% (1241/3264)
Updating files:  39% (1273/3264)
Updating files:  40% (1306/3264)
Updating files:  41% (1339/3264)
Updating files:  41% (1366/3264)
Updating files:  42% (1371/3264)
Updating files:  43% (1404/3264)
Updating files:  44% (1437/3264)
Updating files:  45% (1469/3264)
Updating files:  46% (1502/3264)
Updating files:  47% (1535/3264)
Updating files:  48% (1567/3264)
Updating files:  49% (1600/3264)
Updating files:  50% (1632/3264)
Updating files:  51% (1665/3264)
Updating files:  52% (1698/3264)
Updating files:  52% (1709/3264)
Updating files:  53% (1730/3264)
Updating files:  54% (1763/3264)
Updating files:  55% (1796/3264)
Updating files:  56% (1828/3264)
Updating files:  56% (1843/3264)
Updating files:  57% (1861/3264)
Updating files:  58% (1894/3264)
Updating files:  59% (1926/3264)
Updating files:  60% (1959/3264)
Updating files:  61% (1992/3264)
Updating files:  62% (2024/3264)
Updating files:  63% (2057/3264)
Updating files:  63% (2080/3264)
Updating files:  64% (2089/3264)
Updating files:  65% (2122/3264)
Updating files:  66% (2155/3264)
Updating files:  67% (2187/3264)
Updating files:  68% (2220/3264)
Updating files:  68% (2221/3264)
Updating files:  69% (2253/3264)
Updating files:  70% (2285/3264)
Updating files:  71% (2318/3264)
Updating files:  72% (2351/3264)
Updating files:  73% (2383/3264)
Updating files:  74% (2416/3264)
Updating files:  75% (2448/3264)
Updating files:  76% (2481/3264)
Updating files:  77% (2514/3264)
Updating files:  77% (2521/3264)
Updating files:  78% (2546/3264)
Updating files:  79% (2579/3264)
Updating files:  80% (2612/3264)
Updating files:  81% (2644/3264)
Updating files:  81% (2658/3264)
Updating files:  82% (2677/3264)
Updating files:  83% (2710/3264)
Updating files:  84% (2742/3264)
Updating files:  85% (2775/3264)
Updating files:  86% (2808/3264)
Updating files:  87% (2840/3264)
Updating files:  88% (2873/3264)
Updating files:  89% (2905/3264)
Updating files:  90% (2938/3264)
Updating files:  91% (2971/3264)
Updating files:  92% (3003/3264)
Updating files:  93% (3036/3264)
Updating files:  94% (3069/3264)
Updating files:  94% (3092/3264)
Updating files:  95% (3101/3264)
Updating files:  96% (3134/3264)
Updating files:  97% (3167/3264)
Updating files:  98% (3199/3264)
Updating files:  98% (3218/3264)
Updating files:  99% (3232/3264)
Updating files: 100% (3264/3264)
Updating files: 100% (3264/3264), done.
[2021-09-01T05:14:16.589Z] Updating files:  15% (555/3532)
Updating files:  16% (566/3532)
Updating files:  17% (601/3532)
Updating files:  18% (636/3532)
Updating files:  19% (672/3532)
Updating files:  20% (707/3532)
Updating files:  21% (742/3532)
Updating files:  22% (778/3532)
Updating files:  23% (813/3532)
Updating files:  24% (848/3532)
Updating files:  25% (883/3532)
Updating files:  26% (919/3532)
Updating files:  27% (954/3532)
Updating files:  28% (989/3532)
Updating files:  29% (1025/3532)
Updating files:  30% (1060/3532)
Updating files:  31% (1095/3532)
Updating files:  32% (1131/3532)
Updating files:  33% (1166/3532)
Updating files:  34% (1201/3532)
Updating files:  34% (1208/3532)
Updating files:  35% (1237/3532)
Updating files:  36% (1272/3532)
Updating files:  37% (1307/3532)
Updating files:  38% (1343/3532)
Updating files:  39% (1378/3532)
Updating files:  40% (1413/3532)
Updating files:  41% (1449/3532)
Updating files:  42% (1484/3532)
Updating files:  43% (1519/3532)
Updating files:  44% (1555/3532)
Updating files:  45% (1590/3532)
Updating files:  46% (1625/3532)
Updating files:  47% (1661/3532)
Updating files:  48% (1696/3532)
Updating files:  49% (1731/3532)
Updating files:  50% (1766/3532)
Updating files:  51% (1802/3532)
Updating files:  52% (1837/3532)
Updating files:  53% (1872/3532)
Updating files:  54% (1908/3532)
Updating files:  55% (1943/3532)
Updating files:  55% (1947/3532)
Updating files:  56% (1978/3532)
Updating files:  57% (2014/3532)
Updating files:  57% (2018/3532)
Updating files:  58% (2049/3532)
Updating files:  59% (2084/3532)
Updating files:  60% (2120/3532)
Updating files:  61% (2155/3532)
Updating files:  62% (2190/3532)
Updating files:  63% (2226/3532)
Updating files:  64% (2261/3532)
Updating files:  65% (2296/3532)
Updating files:  66% (2332/3532)
Updating files:  66% (2360/3532)
Updating files:  67% (2367/3532)
Updating files:  68% (2402/3532)
Updating files:  69% (2438/3532)
Updating files:  70% (2473/3532)
Updating files:  71% (2508/3532)
Updating files:  72% (2544/3532)
Updating files:  73% (2579/3532)
Updating files:  74% (2614/3532)
Updating files:  75% (2649/3532)
Updating files:  76% (2685/3532)
Updating files:  77% (2720/3532)
Updating files:  77% (2726/3532)
Updating files:  78% (2755/3532)
Updating files:  79% (2791/3532)
Updating files:  80% (2826/3532)
Updating files:  81% (2861/3532)
Updating files:  82% (2897/3532)
Updating files:  83% (2932/3532)
Updating files:  83% (2938/3532)
Updating files:  84% (2967/3532)
Updating files:  85% (3003/3532)
Updating files:  86% (3038/3532)
Updating files:  87% (3073/3532)
Updating files:  88% (3109/3532)
Updating files:  89% (3144/3532)
Updating files:  90% (3179/3532)
Updating files:  91% (3215/3532)
Updating files:  92% (3250/3532)
Updating files:  92% (3259/3532)
Updating files:  93% (3285/3532)
Updating files:  94% (3321/3532)
Updating files:  95% (3356/3532)
Updating files:  96% (3391/3532)
Updating files:  97% (3427/3532)
Updating files:  98% (3462/3532)
Updating files:  98% (3480/3532)
Updating files:  99% (3497/3532)
Updating files: 100% (3532/3532)
Updating files: 100% (3532/3532), done.
[2021-09-01T05:14:52.185Z] Updating files:   0% (227/26140)
Updating files:   1% (262/26140)
Updating files:   2% (523/26140)
Updating files:   2% (549/26140)
Updating files:   3% (785/26140)
Updating files:   4% (1046/26140)
Updating files:   4% (1246/26140)
Updating files:   5% (1307/26140)
Updating files:   6% (1569/26140)
Updating files:   7% (1830/26140)
Updating files:   8% (2092/26140)
Updating files:   8% (2110/26140)
Updating files:   9% (2353/26140)
Updating files:  10% (2614/26140)
Updating files:  11% (2876/26140)
Updating files:  12% (3137/26140)
Updating files:  12% (3160/26140)
Updating files:  13% (3399/26140)
Updating files:  14% (3660/26140)
Updating files:  15% (3921/26140)
Updating files:  15% (3969/26140)
Updating files:  16% (4183/26140)
Updating files:  17% (4444/26140)
Updating files:  18% (4706/26140)
Updating files:  18% (4894/26140)
Updating files:  19% (4967/26140)
Updating files:  20% (5228/26140)
Updating files:  20% (5339/26140)
Updating files:  21% (5490/26140)
Updating files:  22% (5751/26140)
Updating files:  23% (6013/26140)
Updating files:  23% (6108/26140)
Updating files:  24% (6274/26140)
Updating files:  25% (6535/26140)
Updating files:  25% (6729/26140)
Updating files:  26% (6797/26140)
Updating files:  27% (7058/26140)
Updating files:  27% (7141/26140)
Updating files:  28% (7320/26140)
Updating files:  29% (7581/26140)
Updating files:  29% (7673/26140)
Updating files:  30% (7842/26140)
Updating files:  31% (8104/26140)
Updating files:  32% (8365/26140)
Updating files:  33% (8627/26140)
Updating files:  33% (8724/26140)
Updating files:  34% (8888/26140)
Updating files:  35% (9149/26140)
Updating files:  36% (9411/26140)
Updating files:  36% (9642/26140)
Updating files:  37% (9672/26140)
Updating files:  38% (9934/26140)
Updating files:  39% (10195/26140)
Updating files:  40% (10456/26140)
Updating files:  40% (10577/26140)
Updating files:  41% (10718/26140)
Updating files:  42% (10979/26140)
Updating files:  43% (11241/26140)
Updating files:  44% (11502/26140)
Updating files:  44% (11513/26140)
Updating files:  45% (11763/26140)
Updating files:  46% (12025/26140)
Updating files:  47% (12286/26140)
Updating files:  47% (12404/26140)
Updating files:  48% (12548/26140)
Updating files:  49% (12809/26140)
Updating files:  50% (13070/26140)
Updating files:  50% (13139/26140)
Updating files:  51% (13332/26140)
Updating files:  52% (13593/26140)
Updating files:  53% (13855/26140)
Updating files:  53% (13986/26140)
Updating files:  54% (14116/26140)
Updating files:  55% (14377/26140)
Updating files:  56% (14639/26140)
Updating files:  56% (14859/26140)
Updating files:  57% (14900/26140)
Updating files:  58% (15162/26140)
Updating files:  59% (15423/26140)
Updating files:  59% (15550/26140)
Updating files:  60% (15684/26140)
Updating files:  61% (15946/26140)
Updating files:  61% (16178/26140)
Updating files:  62% (16207/26140)
Updating files:  63% (16469/26140)
Updating files:  64% (16730/26140)
Updating files:  65% (16991/26140)
Updating files:  65% (17048/26140)
Updating files:  66% (17253/26140)
Updating files:  67% (17514/26140)
Updating files:  68% (17776/26140)
Updating files:  68% (17930/26140)
Updating files:  69% (18037/26140)
Updating files:  70% (18298/26140)
Updating files:  71% (18560/26140)
Updating files:  72% (18821/26140)
Updating files:  72% (19069/26140)
Updating files:  73% (19083/26140)
Updating files:  74% (19344/26140)
Updating files:  75% (19605/26140)
Updating files:  76% (19867/26140)
Updating files:  76% (19916/26140)
Updating files:  77% (20128/26140)
Updating files:  78% (20390/26140)
Updating files:  79% (20651/26140)
Updating files:  79% (20901/26140)
Updating files:  80% (20912/26140)
Updating files:  81% (21174/26140)
Updating files:  82% (21435/26140)
Updating files:  82% (21438/26140)
Updating files:  83% (21697/26140)
Updating files:  84% (21958/26140)
Updating files:  85% (22219/26140)
Updating files:  86% (22481/26140)
Updating files:  86% (22553/26140)
Updating files:  87% (22742/26140)
Updating files:  88% (23004/26140)
Updating files:  89% (23265/26140)
Updating files:  90% (23526/26140)
Updating files:  90% (23638/26140)
Updating files:  91% (23788/26140)
Updating files:  92% (24049/26140)
Updating files:  93% (24311/26140)
Updating files:  94% (24572/26140)
Updating files:  94% (24780/26140)
Updating files:  95% (24833/26140)
Updating files:  96% (25095/26140)
Updating files:  97% (25356/26140)
Updating files:  98% (25618/26140)
Updating files:  99% (25879/26140)
Updating files:  99% (26053/26140)
Updating files: 100% (26140/26140)
Updating files: 100% (26140/26140), done.
[2021-09-01T05:14:59.155Z] Updating files:  15% (608/3844)
Updating files:  16% (616/3844)
Updating files:  17% (654/3844)
Updating files:  18% (692/3844)
Updating files:  19% (731/3844)
Updating files:  20% (769/3844)
Updating files:  21% (808/3844)
Updating files:  21% (830/3844)
Updating files:  22% (846/3844)
Updating files:  23% (885/3844)
Updating files:  24% (923/3844)
Updating files:  25% (961/3844)
Updating files:  26% (1000/3844)
Updating files:  27% (1038/3844)
Updating files:  28% (1077/3844)
Updating files:  29% (1115/3844)
Updating files:  30% (1154/3844)
Updating files:  31% (1192/3844)
Updating files:  32% (1231/3844)
Updating files:  33% (1269/3844)
Updating files:  34% (1307/3844)
Updating files:  35% (1346/3844)
Updating files:  36% (1384/3844)
Updating files:  37% (1423/3844)
Updating files:  38% (1461/3844)
Updating files:  38% (1475/3844)
Updating files:  39% (1500/3844)
Updating files:  40% (1538/3844)
Updating files:  41% (1577/3844)
Updating files:  42% (1615/3844)
Updating files:  43% (1653/3844)
Updating files:  44% (1692/3844)
Updating files:  45% (1730/3844)
Updating files:  46% (1769/3844)
Updating files:  47% (1807/3844)
Updating files:  48% (1846/3844)
Updating files:  49% (1884/3844)
Updating files:  50% (1922/3844)
Updating files:  51% (1961/3844)
Updating files:  52% (1999/3844)
Updating files:  53% (2038/3844)
Updating files:  54% (2076/3844)
Updating files:  55% (2115/3844)
Updating files:  56% (2153/3844)
Updating files:  57% (2192/3844)
Updating files:  58% (2230/3844)
Updating files:  59% (2268/3844)
Updating files:  60% (2307/3844)
Updating files:  61% (2345/3844)
Updating files:  61% (2366/3844)
Updating files:  62% (2384/3844)
Updating files:  63% (2422/3844)
Updating files:  64% (2461/3844)
Updating files:  65% (2499/3844)
Updating files:  66% (2538/3844)
Updating files:  67% (2576/3844)
Updating files:  68% (2614/3844)
Updating files:  69% (2653/3844)
Updating files:  70% (2691/3844)
Updating files:  71% (2730/3844)
Updating files:  72% (2768/3844)
Updating files:  73% (2807/3844)
Updating files:  74% (2845/3844)
Updating files:  75% (2883/3844)
Updating files:  76% (2922/3844)
Updating files:  77% (2960/3844)
Updating files:  78% (2999/3844)
Updating files:  79% (3037/3844)
Updating files:  80% (3076/3844)
Updating files:  81% (3114/3844)
Updating files:  82% (3153/3844)
Updating files:  83% (3191/3844)
Updating files:  83% (3219/3844)
Updating files:  84% (3229/3844)
Updating files:  85% (3268/3844)
Updating files:  86% (3306/3844)
Updating files:  87% (3345/3844)
Updating files:  88% (3383/3844)
Updating files:  89% (3422/3844)
Updating files:  90% (3460/3844)
Updating files:  91% (3499/3844)
Updating files:  92% (3537/3844)
Updating files:  93% (3575/3844)
Updating files:  94% (3614/3844)
Updating files:  95% (3652/3844)
Updating files:  96% (3691/3844)
Updating files:  97% (3729/3844)
Updating files:  98% (3768/3844)
Updating files:  99% (3806/3844)
Updating files: 100% (3844/3844)
Updating files: 100% (3844/3844), done.
[2021-09-01T05:15:09.192Z] [8mha:////4FPbweEjXSRRaxMtOzNkKxZtp6R31BjyIudrmvR5LAX7AAAApB+LCAAAAAAAAP9tjTESgjAURD8wFraWHiJA7VjZZmg8QUxiTMj8H5IgVJ7Iq3kHUWas3GJn3zbv+YJNinCkaJjT2FtM0rLgR7MsNlHsr54m5ujCJGEir1mnp46UPq3YUdawpiih4rDVKD0liybDjjtxF7UXaOpzjst34FBaNcADCr6Ys4j5C3OE/W1UifDn+SuAOWSo2qb9dNuENx24j6nAAAAA[0m[Pipeline] bat
[2021-09-01T05:15:09.529Z] [8mha:////4DH5NjBsLZXGJ67yEiAked5DNX8n8s/N55OIquAAKg/pAAAApB+LCAAAAAAAAP9tjTEOwjAQBC+JKGgpeYSDa0RFa6XhBcY5jBPrLtgOScWL+Bp/wBCJii1WO9vM8wWrGODAwYoOqXcUjRODH21eYuLQXzxPouOzMEyRPYoGp4ZbPC7YcEJYUpRQKVgjGc/RkU2wUZ2+69prsvUphfztFZSuvcEDCpXNSYf0hTnA9jq2kenn+SuAeUhQyZ38tJTDG1yJlLDAAAAA[0m[Pipeline] bat
[2021-09-01T05:15:09.855Z] 
[2021-09-01T05:15:09.855Z] c:\b\review_autosar>repo manifest -r -o static_manifest.xml 
[2021-09-01T05:15:18.116Z] Saved manifest to static_manifest.xml
[2021-09-01T05:15:18.143Z] [8mha:////4GEA3ZtI9bBzyz7q9ysLmYqtaiI0f/a47ouenhUIR+fRAAAApR+LCAAAAAAAAP9tjTESwiAURH+SsbC19BBEbB2rtEwaT4CASML8H4GYVJ7Iq3kH0cxYucXOvm3e8wWrGOBIwbLOYO8wKscGP9q82EShv3iaWEdnpggjecNaM7WkTbNgS8nAkqKESsDaoPIUHdoEG9HJu6y9RFufUsjfQUDp9A0eUIhsTjKkL8wBttdRR8Kf568A5iFBxXf803w/vAGf2rmbwAAAAA==[0m[Pipeline] bat
[2021-09-01T05:15:18.479Z] [8mha:////4KknwYgGw3i6CNkBw1dGGGBNRMbTIrnekiWJ5d2kwF1dAAAApR+LCAAAAAAAAP9tjTESwiAURH+SsbC19BBExtKxSsuk8QQIiCTM/xGISeWJvJp3EM2MlVvs7NvmPV+wigGOFCzrDPYOo3Js8KPNi00U+ouniXV0ZoowkjesNVNL2jQLtpQMLClKqASsDSpP0aFNsBGdvMvaS7T1KYX8HQSUTt/gAYXI5iRD+sIcYHsddST8ef4KYB4SVHzHP833wxve66KCwAAAAA==[0m[Pipeline] bat
[2021-09-01T05:15:18.795Z] 
[2021-09-01T05:15:18.795Z] c:\b\review_autosar>xcopy /y /R static_manifest.xml "c:/b/review_autosar/result_dir" 
[2021-09-01T05:15:18.795Z] C:static_manifest.xml
[2021-09-01T05:15:18.795Z] 1 File(s) copied
[2021-09-01T05:15:18.807Z] [8mha:////4EhGvEI9JgvQ5tj5YJOZ7Nn5b5Y/8KhZlaoKfSFQXXBZAAAAph+LCAAAAAAAAP9tjTEOwjAQBC+JKGgpeYSDJTpEldZKwwuMbYwT6y7YDknFi/gaf8AQiYotVjvbzPMFqxjgSMGyzmDvMCrHBj/avNhEob94mlhHZ6YII3nDWjO1pE2zYEvJwJKihErA2qDyFB3aBBvRybusvURbn1LI30FA6fQNHlCIbE4ypC/MAbbXUUfCn+evAOZhSFBxvv/0jr8BFUkEZMAAAAA=[0m[Pipeline] }
[2021-09-01T05:15:18.826Z] [8mha:////4B+q2PZXv+CLRoEdJ3lhtw7sdg7AI5dhLAe6j4nN5JAQAAAApR+LCAAAAAAAAP9tjTESgjAQRRcYC1tLDxGgsHKsbDM0niCSGAOZXUwWofJEXs07GGXGyl/8+e837/mCVQxwoGBFZ7B3GFsnBj/atMREob94mkRHZ9ESRvJGNGZqSJvjgg2xgSVZDoWEtcHWU3RoGTayU3dVeoW2PHFI315C7vQNHpDJZGYV+AtzgO111JHw5/krgHkYGIq63n26qt43cnS1wAAAAA==[0m[Pipeline] // retry
[2021-09-01T05:15:18.844Z] [8mha:////4L9oboDnYK60Mt4iI1n+gxpEgkP/E01mUMd3w6z5tdRFAAAAph+LCAAAAAAAAP9tjbEOgjAURR8QB1dHP6KExYQYJ9aGxS+oba2F5j1sH8LkF/lr/oNEEifvdM9ZzusNmxThRNGJzmLvMWkvhjC65YmJYn8NNImOLkITJgpWtHZqydhmxZbYwrosh0LC1qIOlDw6hp3s1EOVQaErzxwXd5SQe3OHJ2RyKbOK/IU5wv42mkT46/wNwDwMDEVVHRjyuv4AnlyEnb8AAAA=[0m[Pipeline] }
[2021-09-01T05:15:18.863Z] [8mha:////4KRoiGoTiVfzdzLFDZ+kHKU3ubUs2H+C4FWuY5jVZ3zrAAAApR+LCAAAAAAAAP9tjTESgjAQRRcYC1tLDxGGSh2HyjZD4wliiDGQ2cVkESpP5NW8gxmZsfJX/73mvd6wigFqClZ0BnuHUTsx+NGmJyYK/dXTJDq6CE0YyRvRmKmh1pwWbIgNLMtyKCSsDWpP0aFl2MhOPVTpFdryzCG5o4TctXd4QiZTmVXgL8wBtrexjYS/zt8AzMPAUFTVjiE/7D+tv98hvwAAAA==[0m[Pipeline] // script
[2021-09-01T05:15:18.882Z] [8mha:////4PksUcQ4sdJPtOzwrdVHvSL4hLJ2Oc2wamt5BbMQUXlcAAAApR+LCAAAAAAAAP9tjTESgjAQRRcYC1tLDxGGSh2HyjZD4wliiDGQ2cVkESpP5NW8gxmZsfJX/73mvd6wigFqClZ0BnuHUTsx+NGmJyYK/dXTJDq6CE0YyRvRmKmh1pwWbIgNLMtyKCSsDWpP0aFl2MhOPVTpFdryzCG5o4TctXd4QiZTmVXgL8wBtrexjYS/zt8AzMPAUFTVniE/7D7pEDZAvwAAAA==[0m[Pipeline] }
[2021-09-01T05:15:18.902Z] [8mha:////4OBp/2tEbOdsXyaCBkr4q0egcyx0eCARmh7vcppobN5kAAAAph+LCAAAAAAAAP9tjbEOgjAURR8QB1dHP6KExYQYJ9aGxS+oba2F5j1sH8LkF/lr/oNEEifvdM9ZzusNmxThRNGJzmLvMWkvhjC65YmJYn8NNImOLkITJgpWtHZqydhmxZbYwrosh0LC1qIOlDw6hp3s1EOVQaErzxwXd5SQe3OHJ2RyKbOK/IU5wv42mkT46/wNwDwMDEVV1Qx5ffgA2vNt/L8AAAA=[0m[Pipeline] // stage
[2021-09-01T05:15:18.938Z] [8mha:////4LlwgSx2FwJ+svtMTwh+qzAfRE8l0BHSSZ6MlRy/ooe5AAAApx+LCAAAAAAAAP9tjTESgjAQRRcYCltLDxHA1rGizdB4ghhiDGR2MVmEyhN5Ne8gyoyVv/rvz/x5zxfkMcCRghWdwd5h1E4MfrRLExOF/uJpEh2dhSaM5I1ozNRQa+oVG2IDa5IUMgkbg9pTdGgZtrJTd1V4hbY4cVi2g4TUtTd4QCIXM6vAX5gD7K5jGwl/nr8CmAeGtCoZsmpffq75G+3jblvDAAAA[0m[Pipeline] stage
[2021-09-01T05:15:18.947Z] [8mha:////4Anitrkd4yITgmaAN1fBLCGJ/ZAeCwG/t4vfFEodPv99AAAApx+LCAAAAAAAAP9tjbEOgjAYhH8gDK6OPkQRV+PE2rD4BLXUWmj+H9timXwiX813sELi5A2Xu0su3+sNpXdwIqdZr3Aw6KVho510SiySG66WIuvpwiShJ6tYq2JLnWrW2lJQsCrLoeCwUSgteYM6wJb34iEqK1BX5+DSduSQm+4OT8h4IgfhwlJmB7vb1HnCH+cvAOYxQFEf9ovX32/5AUfIQ0nEAAAA[0m[Pipeline] { (Download Patch)
[2021-09-01T05:15:18.983Z] [8mha:////4Gq2oa6g0812GY1yN5f+f1IgDKzJQzu7SVTtxDQuEKxJAAAApx+LCAAAAAAAAP9tjbEOgjAYhH8gDK6OPkQJrMbJteniE1Raa0vz/9AWYfKJfDXfQYTEyRsud5dcvtcbyhjgRMEwp7GzGFvLej+aJbGJQnfzNDFHV9YSRvKaCT0JUvq8VUFJw6Ysh4LDTmPrKVo0CfbcyYesvERTXVJYtiOH3KoBnpDxhZxkSGuZAxzuo4qEP85fAMx9gqJu6tWb77f8AIb2clTEAAAA[0m[Pipeline] script
[2021-09-01T05:15:18.992Z] [8mha:////4HFTGkFeGecPR5TEXYDlmn+ALiEePwJ6C7gq1zS7tvhHAAAApx+LCAAAAAAAAP9tjbEOgjAYhH8gDK6OPkSJOBon1obFJ6il1kLz/9gWy+QT+Wq+gxUSJ2+43F1y+V5vKL2DEznNeoWDQS8NG+2kU2KR3HC1FFlPFyYJPVnFWhVb6lSz1paCglVZDgWHjUJpyRvUAba8Fw9RWYG6OgeXtiOH3HR3eELGEzkIF5YyO9jdps4T/jh/ATCPAYp9XS9++H7LD9P3sOjEAAAA[0m[Pipeline] {
[2021-09-01T05:15:19.021Z] [8mha:////4NxziXAF8w7G9xT29pbGZQubUBYcu+11bjvu7g0FZpnfAAAApR+LCAAAAAAAAP9tjTsOwjAQRDeJKGgpOYQjPh1KRWul4QTGNsaOtRtsB6fiRFyNOxAlEhVTjOZN894fWMUADQXDnMbOYpSW9X4w02KZQnfzlJmjK5OEkbxmrc4tKX1esKWkYUlRQsVhrVF6ihZNgg134ilqL9DUlxSm78ShtOoBLyj4ZE4ipBnGANv7oCLhz/NXAGOfoNrtD3Mf+y+77HajwAAAAA==[0m[Pipeline] sh
[2021-09-01T05:15:19.660Z] + C:/instr-cluster-tools/python/3.7.4/python jenkins-helper-tools/MergeRebase/download_topic.py -u cma1lr -t PFMProxy_Conf
[2021-09-01T05:15:21.102Z] [8mha:////4CpvCrv6giXHBqh4/tsIp9BrvkUxappOnfq40bhOlF5gAAAApR+LCAAAAAAAAP9tjTEOwjAQBC+JKGgpeYQjQFQoFa2VhhcY2xg71l2wHZyKF/E1/kCUSFRssdrZZt4fWMUADQXDnMbOYpSW9X4w02KZQnfzlJmjK5OEkbxmrc4tKX1esKWkYUlRQsVhrVF6ihZNgg134ilqL9DUlxSm78ShtOoBLyj4ZE4ipBnGANv7oCLhz/NXAGOfoNrtD3Mf+y/63W26wAAAAA==[0m[Pipeline] echo
[2021-09-01T05:15:21.111Z] SHAREDLIB: rbUpdateDescription([description:Downloading changes in gerrit topic PFMProxy_Conf. Top commit(s):
[2021-09-01T05:15:21.111Z] ])
[2021-09-01T05:15:21.138Z] [8mha:////4MejBoOukSo2z1YVcQ2r35OW2C1DwhwBGV8F2bLv1BdeAAAAph+LCAAAAAAAAP9tjTEOwjAQBC+JKGgpeYQjQKJBqWitNLzA2MbYse6C7eBUvIiv8QeiRKJii9XONvP+wCoGaCgY5jR2FqO0rPeDmRbLFLqbp8wcXZkkjOQ1a3VuSenzgi0lDUuKEioOa43SU7RoEmy4E09Re4GmvqQwfScOpVUPeEHBJ3MSIc0wBtjeBxUJf56/Ahj7BNVuf5j72H8BOY5AkcAAAAA=[0m[Pipeline] readJSON
[2021-09-01T05:15:21.190Z] [8mha:////4IRXkE1V1h7/i7H6+vMt6fugUDLoMf/huyZzDZIPniBmAAAAph+LCAAAAAAAAP9tjTEOwjAQBC+JKGgpeYQjoKBAqWitNLzA2MbYse6C7eBUvIiv8QeiRKJii9XONvP+wCoGaCgY5jR2FqO0rPeDmRbLFLqbp8wcXZkkjOQ1a3VuSenzgi0lDUuKEioOa43SU7RoEmy4E09Re4GmvqQwfScOpVUPeEHBJ3MSIc0wBtjeBxUJf56/Ahj7BNVuf5j72H8BeL9biMAAAAA=[0m[Pipeline] echo
[2021-09-01T05:15:21.199Z] Manifest ref: null
[2021-09-01T05:15:21.217Z] [8mha:////4LXN2xq58sSagW6KrEnuPvX33mdT+/kBxL79UO6JqA39AAAAph+LCAAAAAAAAP9tjTEOwjAQBC+JKGgpeYQjoEFCqWitNLzA2MbYse6C7eBUvIiv8QeiRKJii9XONvP+wCoGaCgY5jR2FqO0rPeDmRbLFLqbp8wcXZkkjOQ1a3VuSenzgi0lDUuKEioOa43SU7RoEmy4E09Re4GmvqQwfScOpVUPeEHBJ3MSIc0wBtjeBxUJf56/Ahj7BNVuf5j72H8Bt6PDD8AAAAA=[0m[Pipeline] echo
[2021-09-01T05:15:21.230Z] Changes to download: [338415:[patch:4, project:projects/hydra/config/autosar/stdcore/saic-dms-is31, ref:refs/changes/15/338415/4], 338448:[patch:4, project:clusterbase/autosar/health/pfmproxy, ref:refs/changes/48/338448/4]]
[2021-09-01T05:15:21.258Z] [8mha:////4OJg0Lsm7nsoFxvHMFcBXNnDdt4++jatzSnAI0G0N8+2AAAApR+LCAAAAAAAAP9tjTEOwjAQBC+JKGgpeYQjoEIoFa2VhhcY2xg71l2wHZyKF/E1/kCUSFRssdrZZt4fWMUADQXDnMbOYpSW9X4w02KZQnfzlJmjK5OEkbxmrc4tKX1esKWkYUlRQsVhrVF6ihZNgg134ilqL9DUlxSm78ShtOoBLyj4ZE4ipBnGANv7oCLhz/NXAGOfoNrtD3Mf+y/2ktgWwAAAAA==[0m[Pipeline] sh
[2021-09-01T05:15:21.674Z] + repo.cmd list projects/hydra/config/autosar/stdcore/saic-dms-is31
[2021-09-01T05:15:22.614Z] Config/Autosar/stdcore/saic-dms-is31 : projects/hydra/config/autosar/stdcore/saic-dms-is31
[2021-09-01T05:15:22.633Z] [8mha:////4CPXCIEYnTYynbFVrWOstpBO2d6W1H9fvXjjuK2b6JBAAAAApR+LCAAAAAAAAP9tjTESgjAQRRcYC1tLDxFUSsfKNkPjCSLEmJDZjckiVJ7Iq3kHUWas/MWf/37zni9YpAgHikY4jZ3F1FgRfG+mJQaK3cXTIBydRUOYyGtR66GmVh9nrIk1zMlyKCQsNTaekkXDsJJO3VXpFZryxHH69hJy297gAZmczKwif2GMsL72bSL8ef4KYAwMxXZXfbrahDeIQ9jGwAAAAA==[0m[Pipeline] echo
[2021-09-01T05:15:22.642Z] SHAREDLIB: rbUpdateDescription([description:Downloading change 338415/4 to project projects/hydra/config/autosar/stdcore/saic-dms-is31
[2021-09-01T05:15:22.642Z] ])
[2021-09-01T05:15:22.694Z] [8mha:////4JPGBCMNbAXGGC3SS5dCYhm87Dp4ns9QCyma9wUovEgXAAAApR+LCAAAAAAAAP9tjTESgjAQRRcYC1tLDxEGKR0r2wyNJ4gkxkBmF5NFqDyRV/MORpmx8hd//vvNe75gFQMcKFjRGewdxtaJwY82LTFR6C+eJtHRWbSEkbwRjZka0ua4YENsYEmWQyFhbbD1FB1aho3s1F2VXqEtTxzSt5eQO32DB2QymVkF/sIcYHsddST8ef4KYB4YimpXf7quhjfJcsPfwAAAAA==[0m[Pipeline] sh
[2021-09-01T05:15:23.011Z] + repo.cmd list projects/hydra/config/autosar/stdcore/saic-dms-is31 -p
[2021-09-01T05:15:23.978Z] [8mha:////4MMHvRu8tTL4R90ooiWOt2Nj8Vu/QFXpE4pAdVj7JdOLAAAAqB+LCAAAAAAAAP9tjbEOgjAYhH8gDK6OPkSJMBon14bFJ6htrYXm/7Etlskn8tV8B0ESJ2+43F1y+V5vKIOHI3nDOo29xSAtG9xo5sQS+f7qKLGOLkwSBnKatTq1pPRprS1FDauyHAoOG43SUbBoImx5Jx6icgJNdY5+3g4ccqvu8ISMz+QofPyWycPuNqpA+OP8BcA0RCj2dbN4Uy/f8gOtYz4exAAAAA==[0m[Pipeline] dir
[2021-09-01T05:15:23.978Z] Running in c:/b/review_autosar/Config/Autosar/stdcore/saic-dms-is31
[2021-09-01T05:15:23.987Z] [8mha:////4JAxgngHefxhnOududOQK+Z7BMD0xDRxxJ72s5EzjCExAAAApx+LCAAAAAAAAP9tjbEOgjAYhH8gDK6OPkSJMhon1obFJ6il1kLz/9gWy+QT+Wq+gxUSJ2+43F1y+V5vKL2DEznNeoWDQS8NG+2kU2KR3HC1FFlPFyYJPVnFWhVb6lSz1paCglVZDgWHjUJpyRvUAba8Fw9RWYG6OgeXtiOH3HR3eELGEzkIF5YyO9jdps4T/jh/ATCPAYp9fVi8/n7LD+IlUZPEAAAA[0m[Pipeline] {
[2021-09-01T05:15:24.019Z] [8mha:////4EQtBcesPHfTYLLLYVAZxvWQbmGgSwQ6pcqC0Jg9I+LBAAAApR+LCAAAAAAAAP9tjbEOgjAURR8QB1dHP+IRg5txYm26+AW11Fpo3sO2CJNf5K/5DxJInLzDzT13Oe8PbGKAMweLraHOUdQOez/YeeHIobt5HrHlK2qmyN6gNKPkxtQrSk4G1mQ5FAK2hrTn6Mgm2IlWPVXpFdnyksL8nQTkrnnACzIxm5MKaYEpwP4+NJHp5/krgKlPUByqaulj/wXPks+1wAAAAA==[0m[Pipeline] bat
[2021-09-01T05:15:24.358Z] [8mha:////4NLSLftHnxr362rvB8DDMmEF7PrEQbPU21VPu6PfLYNAAAAApR+LCAAAAAAAAP9tjbEOgjAURR8QB1dHP+IRQ5yME2vTxS+opdZC8x62RZj8In/Nf5BA4uQdbu65y3l/YBMDnDlYbA11jqJ22PvBzgtHDt3N84gtX1EzRfYGpRklN6ZeUXIysCbLoRCwNaQ9R0c2wU606qlKr8iWlxTm7yQgd80DXpCJ2ZxUSAtMAfb3oYlMP89fAUx9guJQVUsf+y+Oo9SswAAAAA==[0m[Pipeline] bat
[2021-09-01T05:15:24.672Z] 
[2021-09-01T05:15:24.672Z] c:\b\review_autosar\Config\Autosar\stdcore\saic-dms-is31>git fetch ssh://rbcm-gerrit.de.bosch.com:29418/projects/hydra/config/autosar/stdcore/saic-dms-is31 refs/changes/15/338415/4   && git merge --no-ff FETCH_HEAD 
[2021-09-01T05:15:24.934Z] Total 28 (delta 2), reused 10 (delta 2)
[2021-09-01T05:15:25.193Z] From ssh://felb-rbcm-gerrit.de.bosch.com:29418/projects/hydra/config/autosar/stdcore/saic-dms-is31
[2021-09-01T05:15:25.193Z]  * branch            refs/changes/15/338415/4 -> FETCH_HEAD
[2021-09-01T05:15:25.795Z] Merge made by the 'recursive' strategy.
[2021-09-01T05:15:25.795Z]  CMakeLists.txt                                     |    7 +
[2021-09-01T05:15:25.795Z]  Config/Developer/ComponentTypes/SWC_PFMProxy.arxml |   66 ++
[2021-09-01T05:15:25.795Z]  Config/Developer/ComponentTypes/SWC_PFMProxy.dvg   |   61 +
[2021-09-01T05:15:25.795Z]  Config/Developer/ComponentTypes/SwcHmiRumba.dvg    |   67 +-
[2021-09-01T05:15:25.795Z]  .../ComponentTypes/Test_Application_QM.dvg         |   53 +-
[2021-09-01T05:15:25.795Z]  Config/Developer/ECUProjects/DMSCCfg.arxml         |   11 +
[2021-09-01T05:15:25.795Z]  Config/Developer/ECUProjects/DMSCCfg.dvg           | 1181 ++++++++++----------
[2021-09-01T05:15:25.795Z]  Config/Developer/Hydra_V3M_SAIC.dcf                |    6 +-
[2021-09-01T05:15:25.795Z]  Config/ECUC/Hydra_V3M_SAIC_EcuM_ecuc.arxml         |    4 +-
[2021-09-01T05:15:25.795Z]  Config/ECUC/Hydra_V3M_SAIC_Rte_ecuc.arxml          |  126 ++-
[2021-09-01T05:15:25.795Z]  Config/ECUC/Hydra_V3M_SAIC_WdgM_ecuc.arxml         |  108 ++
[2021-09-01T05:15:25.795Z]  Config/ServiceComponents/WdgM_swc.arxml            |   85 ++
[2021-09-01T05:15:25.795Z]  Config/System/FlatExtract.arxml                    |   38 +
[2021-09-01T05:15:25.795Z]  Config/System/FlatMap.arxml                        |   16 +
[2021-09-01T05:15:25.795Z]  Hydra_V3M_SAIC.dpa                                 |    1 +
[2021-09-01T05:15:25.795Z]  MemMap/InputData/Modules/MemMapModules.csv         |    1 +
[2021-09-01T05:15:25.795Z]  16 files changed, 1231 insertions(+), 600 deletions(-)
[2021-09-01T05:15:25.795Z]  create mode 100644 Config/Developer/ComponentTypes/SWC_PFMProxy.arxml
[2021-09-01T05:15:25.795Z]  create mode 100644 Config/Developer/ComponentTypes/SWC_PFMProxy.dvg
[2021-09-01T05:15:25.816Z] [8mha:////4Nf31E3u6ThX2zeYdxV8sBUjkiWX0RaoSf6AOt0M9Vl1AAAAph+LCAAAAAAAAP9tjbEOgjAURR8QB1dHP+IRQ+JinFibLn5BLbUWmvewLcLkF/lr/oMEEifvcHPPXc77A5sY4MzBYmuocxS1w94Pdl44cuhunkds+YqaKbI3KM0ouTH1ipKTgTVZDoWArSHtOTqyCXaiVU9VekW2vKQwfycBuWse8IJMzOakQlpgCrC/D01k+nn+CmDq+wTFoTouXX0Brm2ddMAAAAA=[0m[Pipeline] }
[2021-09-01T05:15:25.844Z] [8mha:////4IckXVG2Zk3lEh2zSnAV+s+wPqkxdk1yOSPfHVxBgoFsAAAAph+LCAAAAAAAAP9tjTEOwjAQBC+JKGgpeYQjoKBAqWitNLzA2MbYse6C7eBUvIiv8QeiRKJii9XONvP+wCoGaCgY5jR2FqO0rPeDmRbLFLqbp8wcXZkkjOQ1a3VuSenzgi0lDUuKEioOa43SU7RoEmy4E09Re4GmvqQwfScOpVUPeEHBJ3MSIc0wBtjeBxUJf56/Ahj7PkG1Oxzn3n8BjFbtpcAAAAA=[0m[Pipeline] // dir
[2021-09-01T05:15:25.871Z] [8mha:////4A4ZDRb1Zo07FnfBpqroQyLcxCDuuX9KrqUDdJD1YqOWAAAAph+LCAAAAAAAAP9tjTEOwjAQBC+JKGgpeYQjSIOEqGitNLzAJMbYse6MfSGpeBFf4w8EIlGxxWpnm3m+YJEiHCga4TR2FlNjRfC9mZYYKHYXT4NwdBYNYSKvRa2Hmlp9nLEm1jAny6GQsNTYeEoWDcNKOnVXpVdoyhPH6dtLyG17gwdkcjKzivyFMcL62reJ8Of5K4AxMBSbbfXpahfegMkBDsAAAAA=[0m[Pipeline] sh
[2021-09-01T05:15:26.175Z] + repo.cmd list clusterbase/autosar/health/pfmproxy
[2021-09-01T05:15:27.114Z] Autosar/clusterbase/health/pfmproxy : clusterbase/autosar/health/pfmproxy
[2021-09-01T05:15:27.133Z] [8mha:////4NkWdXadPjkhYLUsU3NOyfgH4xi1JwaMoMfFKKhBALAxAAAAph+LCAAAAAAAAP9tjTEOwjAQBC+JKGgpeYQjSIUQFa2VhheYxBg71p2xLyQVL+Jr/IFAJCq2WO1sM88XLFKEA0UjnMbOYmqsCL430xIDxe7iaRCOzqIhTOS1qPVQU6uPM9bEGuZkORQSlhobT8miYVhJp+6q9ApNeeI4fXsJuW1v8IBMTmZWkb8wRlhf+zYR/jx/BTAGhmKzrT5d7cIbwfgaF8AAAAA=[0m[Pipeline] echo
[2021-09-01T05:15:27.142Z] SHAREDLIB: rbUpdateDescription([description:Downloading change 338448/4 to project clusterbase/autosar/health/pfmproxy
[2021-09-01T05:15:27.142Z] ])
[2021-09-01T05:15:27.169Z] [8mha:////4MlPpAN+DUhJFKvQzVZw+BVbt2gUL0hbfiOCLuBHq0jpAAAAph+LCAAAAAAAAP9tjT0SwiAUhF+SsbC19BDEv86xsmVoPAECIgnzXgRiUnkir+YdRDNj5RY7+23zPV8wiwEOFCxrDLYOo3Ks873Niw0U2oungTV0ZoowkjdMmEGQNscJBSUDU4oSKg5zg8pTdGgTLHgj77L2Em19SiF/ew6l0zd4QMGzOcmQvjAGWF57HQl/nr8CGLsE1Xqz/fRu1b0BDVWXw8AAAAA=[0m[Pipeline] sh
[2021-09-01T05:15:27.477Z] + repo.cmd list clusterbase/autosar/health/pfmproxy -p
[2021-09-01T05:15:28.498Z] [8mha:////4F317Bj92AkhiQvTbc4BTpP1WqHP0Jq6QZO1TGI4ogYJAAAAqB+LCAAAAAAAAP9tjTEOwjAUQ39bdWBl5BCpCmyIiTXqwglCE0La6P+SpKQTJ+Jq3IGUSkx4sGxL1nu9ofQOjuQ06xT2Bn1r2GBHnRKL5Pqrpcg6urCW0JNVrFGxIalOS20oKFiU5VBwWClsLXmDOsCad+IhKitQV+fg0nbgkBt5hydkPJGDcOFbJgeb2yg94Y/zFwDTEKCot7vZ9/X8LT+NLw1dxAAAAA==[0m[Pipeline] dir
[2021-09-01T05:15:28.499Z] Running in c:/b/review_autosar/Autosar/clusterbase/health/pfmproxy
[2021-09-01T05:15:28.507Z] [8mha:////4FgAuIMRZRUMNJ40hT8KmoRRgbo6tDFbWXlA/PeVxM4GAAAAqB+LCAAAAAAAAP9tjbEOgjAYhH8gDK6OPkQJxs04sTYsPkEttRaa/8e2WCafyFfzHayQOHnD5e6Sy/d6Q+kdnMhp1iscDHpp2GgnnRKL5Iarpch6ujBJ6Mkq1qrYUqeatbYUFKzKcig4bBRKS96gDrDlvXiIygrU1Tm4tB055Ka7wxMynshBuLCU2cHuNnWe8Mf5C4B5DFDUh3rx/fdbfgBhHEeXxAAAAA==[0m[Pipeline] {
[2021-09-01T05:15:28.544Z] [8mha:////4FNoXc/P+66Ga50nTSjnQdDcmucp6APyYw2ckuKVr2NfAAAApR+LCAAAAAAAAP9tjTsOwjAQRDeJKGgpOYQjPh1KRWul4QTGNsaOtRtsB6fiRFyNOxAlEhVTjOZN894fWMUADQXDnMbOYpSW9X4w02KZQnfzlJmjK5OEkbxmrc4tKX1esKWkYUlRQsVhrVF6ihZNgg134ilqL9DUlxSm78ShtOoBLyj4ZE4ipBnGANv7oCLhz/NXAGOfoNod93Mf+i/afwpXwAAAAA==[0m[Pipeline] bat
[2021-09-01T05:15:28.879Z] [8mha:////4Gz2m4UxoTS/amOIwemil8a/7jFTYdyT3TuiPChRqDDZAAAApR+LCAAAAAAAAP9tjbEOgjAURR8QB1dHP6JEw2acWJsufkEttRaa9/C1CJNf5K/5DxJInLzDzT13Oe8PbCLDmdiJ1mLnMRov+jC4eYmRuLsFGkVLV2EIIwUrlB0VNbZeUVGysCbLoZCwtWgCRY8uwU62+qnLoNGVl8Tzd5KQ++YBL8jkbE6a0wITw/4+NJHw5/krgKlPUByq49JV/wUd6UsYwAAAAA==[0m[Pipeline] bat
[2021-09-01T05:15:29.185Z] 
[2021-09-01T05:15:29.185Z] c:\b\review_autosar\Autosar\clusterbase\health\pfmproxy>git fetch ssh://rbcm-gerrit.de.bosch.com:29418/clusterbase/autosar/health/pfmproxy refs/changes/48/338448/4   && git merge --no-ff FETCH_HEAD 
[2021-09-01T05:15:29.185Z] Total 4 (delta 0), reused 0 (delta 0)
[2021-09-01T05:15:29.450Z] From ssh://felb-rbcm-gerrit.de.bosch.com:29418/clusterbase/autosar/health/pfmproxy
[2021-09-01T05:15:29.450Z]  * branch            refs/changes/48/338448/4 -> FETCH_HEAD
[2021-09-01T05:15:29.710Z] Auto-merging PfmProxy.h
[2021-09-01T05:15:29.710Z] CONFLICT (content): Merge conflict in PfmProxy.h
[2021-09-01T05:15:29.710Z] Auto-merging PfmProxy.c
[2021-09-01T05:15:29.710Z] Automatic merge failed; fix conflicts and then commit the result.
[2021-09-01T05:15:29.710Z] Recorded preimage for 'PfmProxy.h'
[2021-09-01T05:15:29.730Z] [8mha:////4JnoZdMuSND8vsTvbOLmwmfg/u9aIYzOsyuCzmmIhdMgAAAApR+LCAAAAAAAAP9tjTEOwjAQBC+JKGgpeYQjEFQoFa2VhhcY2xg71l2wHZyKF/E1/kCUSFRssdrZZt4fWMUADQXDnMbOYpSW9X4w02KZQnfzlJmjK5OEkbxmrc4tKX1esKWkYUlRQsVhrVF6ihZNgg134ilqL9DUlxSm78ShtOoBLyj4ZE4ipBnGANv7oCLhz/NXAGPfJ6h2h+Pc+y972PZ3wAAAAA==[0m[Pipeline] }
[2021-09-01T05:15:29.758Z] [8mha:////4BEMfpaF8sEcTPtOWRECsBZW70C5DCpg7aAED7LWkSrmAAAAph+LCAAAAAAAAP9tjTEOwjAQBC+JKGgpeYSjSIgGUaW10vACExtjx7oztkNS8SK+xh+wEomKLVY728z7A5sY4ExBM6twMBh7w7wbdV5sojDcHE3M0pX1hJGcYp2aOpKqXbGjpGBNUULFYauwdxQN6gQ7bsVT1E6gri8p5O/EoTTyAS8oeDYnEdICc4D9fZSR8Of5K4DZ+wRVczgu3XwBXJMX38AAAAA=[0m[Pipeline] // dir
[2021-09-01T05:15:29.785Z] [8mha:////4I/hhoOAftyJbRKLQIKRuvYKK7l0+NU71M8WfpbsRBQ8AAAApx+LCAAAAAAAAP9tjTsOwjAQRDeJKGgpOYQjPhIFoqK13HACYxvjxNoNtkNScSKuxh0wRKJiitG8ad7zBbMY4EDBssZg6zAqxzrf27zYQKG9eBpYQ2emCCN5w4QZBGlznFBQMjClKKHiMDeoPEWHNsGCN/Iuay/R1qcU8rfnUDp9gwcUPJuTDOkLY4DltdeR8Of5K4Cx6xJUq+3u0+vNG0JeNMHAAAAA[0m[Pipeline] }
[2021-09-01T05:15:29.814Z] [8mha:////4DeQkXS26vWevnmHdbIs4rAqv4HqnIYtb/YsFRSQpj+nAAAApx+LCAAAAAAAAP9tjTEOwjAQBC+JKGgpeYQjIgokREVrpeEFJjHGjnVn7AtJxYv4Gn8gEImKLVY728zzBYsU4UDRCKexs5gaK4LvzbTEQLG7eBqEo7NoCBN5LWo91NTq44w1sYY5WQ6FhKXGxlOyaBhW0qm7Kr1CU544Tt9eQm7bGzwgk5OZVeQvjBHW175NhD/PXwGMITAUm+3u01X1Bj0ee0DAAAAA[0m[Pipeline] // script
[2021-09-01T05:15:29.851Z] [8mha:////4Gw9Akz5P+goreB3E44db4SXULEV9WJ6ZWLp6UT1A/LqAAAAph+LCAAAAAAAAP9tjTEOwjAQBC+JKGgpeYSjIBqEqGgtN7zA2MY4se6C7eBUvIiv8QcCkajYYrWzzTxfsIgBDhQsaw12DqNyrPeDnRbLFLqLp8xaOjNFGMkbJkwWpM1xRkHJwJyihIrD0qDyFB3aBCveyrusvURbn1KYvj2H0ukbPKDgkznJkL4wBlhfBx0Jf56/Ahj7PkHVbHef3jRvM0QFf8AAAAA=[0m[Pipeline] }
[2021-09-01T05:15:29.879Z] [8mha:////4ML9OLSVnDUcgBnC9YRYo8br4DWfwvU/lai+9VxwIe0EAAAAph+LCAAAAAAAAP9tjTEOwjAQBC+JKGgpeYQTkKgQFa3lhhcY2xgn1l2wHZyKF/E1/kAgEhVbrHa2mecLFjHAgYJlrcHOYVSO9X6w02KZQnfxlFlLZ6YII3nDhMmCtDnOKCgZmFOUUHFYGlSeokObYMVbeZe1l2jrUwrTt+dQOn2DBxR8MicZ0hfGAOvroCPhz/NXAGPfJ6g2u+bT2+YN6DEwUcAAAAA=[0m[Pipeline] // stage
[2021-09-01T05:15:29.924Z] [8mha:////4FI+d2w5V4StFMgOgBEf6JkCmBlaGybWam9RQ3/ZVXNVAAAAqB+LCAAAAAAAAP9tjTEOwjAQBC+JUtBS8giHFFSIitZKwwuMY4wT6y7YF5KKF/E1/oAhEhVb7ay0mucLyhjgQMGKzmDvMGonBj/a1MREob94mkRHZ6EJI3kjGjM11Jrjgg2xgSVZDoWElUHtKTq0DGvZqbuqvEJbnTikbS8hd+0NHpDJZGYV+AtzgM11bCPhz/NXAPPAkNdbhqLe1Z9r+QbGDpVVwwAAAA==[0m[Pipeline] stage
[2021-09-01T05:15:29.933Z] [8mha:////4McyAz9FmR8juvFWqyCLPLkhGN0NZ/W3QOa/fm+5NU87AAAAqB+LCAAAAAAAAP9tjbEOgjAYhH8gDK6OPkQJJk7GibVh8QlqqbXQ/D+2xTL5RL6a72CFxMkbLneXXL7XG0rv4EROs17hYNBLw0Y76ZRYJDdcLUXW04VJQk9WsVbFljrVrLWloGBVlkPBYaNQWvIGdYAt78VDVFagrs7Bpe3IITfdHZ6Q8UQOwoWlzA52t6nzhD/OXwDMY4CiPtSL77/f8gNQzqbsxAAAAA==[0m[Pipeline] { (Create static manifest with patches)
[2021-09-01T05:15:29.945Z] Stage "Create static manifest with patches" skipped due to earlier failure(s)
[2021-09-01T05:15:29.961Z] [8mha:////4NzTecZKQlNOAtTcaNAl7IcCFIJUE218ZOXzk+xblUBuAAAApR+LCAAAAAAAAP9tjTEOwjAQBC+JKGgpeYQjQFQoFa2VhhcY2xg71l2wHZyKF/E1/kCUSFRssdrZZt4fWMUADQXDnMbOYpSW9X4w02KZQnfzlJmjK5OEkbxmrc4tKX1esKWkYUlRQsVhrVF6ihZNgg134ilqL9DUlxSm78ShtOoBLyj4ZE4ipBnGANv7oCLhz/NXAGPfJ6h2x8Pc+y+e3JfBwAAAAA==[0m[Pipeline] }
[2021-09-01T05:15:29.989Z] [8mha:////4DJHGfvigb2k8WYPgeHGiu+w8QinJ52PiBM6tuRxZBdlAAAApR+LCAAAAAAAAP9tjTEOwjAQBC+JKGgpeYSjSFAhqrRWGl5gYmPsWHfGdkgqXsTX+ANWIlGxxWpnm3l/YBMDnCloZhUOBmNvmHejzotNFIabo4lZurKeMJJTrFNTR1K1K3aUFKwpSqg4bBX2jqJBnWDHrXiK2gnU9SWF/J04lEY+4AUFz+YkQlpgDrC/jzIS/jx/BTB7n6Bqjoelmy+qszmdwAAAAA==[0m[Pipeline] // stage
[2021-09-01T05:15:30.034Z] [8mha:////4Px3IEQWCaYcUJ2MYE2q+x+h0MCIQ76og43vb2WsPEi0AAAAqB+LCAAAAAAAAP9tjTESgjAQRRcYCltLDxGkoHKsaDM0niCGGAOZXUwWofJEXs07iDJj5a/++zN/3vMFeQxwpGBFZ7B3GLUTgx/t0sREob94mkRHZ6EJI3kjGjM11Jp6xYbYwJokhUzCxqD2FB1ahq3s1F0VXqEtThyW7SAhde0NHpDIxcwq8BfmALvr2EbCn+evAOaBIS33DFlZVZ9r/gbQTATOwwAAAA==[0m[Pipeline] stage
[2021-09-01T05:15:30.043Z] [8mha:////4Nz6XYWu7y5MlHltds3H5u8MBmlD3DX2fEFkWM3dVEFlAAAAqB+LCAAAAAAAAP9tjbEOgjAYhH8gDK6OPkSJAy7GibVh8QlqqbXQ/D+2xTL5RL6a72CFxMkbLneXXL7XG0rv4EROs17hYNBLw0Y76ZRYJDdcLUXW04VJQk9WsVbFljrVrLWloGBVlkPBYaNQWvIGdYAt78VDVFagrs7Bpe3IITfdHZ6Q8UQOwoWlzA52t6nzhD/OXwDMY4BiX9eLH77f8gM5tzF0xAAAAA==[0m[Pipeline] { (Build Autosar)
[2021-09-01T05:15:30.054Z] Stage "Build Autosar" skipped due to earlier failure(s)
[2021-09-01T05:15:30.072Z] [8mha:////4NmUsM6hZsTt63zmGn0GalA6QUnfWJLsbRvw90bn2USMAAAAph+LCAAAAAAAAP9tjTEOwjAQBC+JKGgpeYQjCqBAqWitNLzA2MbYse6C7eBUvIiv8QeiRKJii9XONvP+wCoGaCgY5jR2FqO0rPeDmRbLFLqbp8wcXZkkjOQ1a3VuSenzgi0lDUuKEioOa43SU7RoEmy4E09Re4GmvqQwfScOpVUPeEHBJ3MSIc0wBtjeBxUJf56/Ahj7PkG12x/nPnwBlDy1MsAAAAA=[0m[Pipeline] }
[2021-09-01T05:15:30.099Z] [8mha:////4GG3RwRmuzWLYoH+/ZJAcHXkuzJ7N2ZMT0JpBDFKPC+uAAAAph+LCAAAAAAAAP9tjbEOgjAURR8QB1dHP+IRBxIT48TadPELaqm10LyHbREmv8hf8x8kkDh5h5t77nLeH9jEAGcOFltDnaOoHfZ+sPPCkUN38zxiy1fUTJG9QWlGyY2pV5ScDKzJcigEbA1pz9GRTbATrXqq0iuy5SWF+TsJyF3zgBdkYjYnFdICU4D9fWgi08/zVwBT3ycoDtVx6eoLxx30XcAAAAA=[0m[Pipeline] // stage
[2021-09-01T05:15:30.145Z] [8mha:////4LNUmPdBdgx7B5zkVM3hpDPlLJZ/vSpRl6Fpzp1lRxh4AAAAqR+LCAAAAAAAAP9tjbEOgjAURR8QBldHP6LI4GAMk2vD4hfUUmuheQ/bhzD5Rf6a/yBK4uSd7rnJzXm+II8BKgpWtAY7h1E70fvBzk2MFLqLp1G0dBaaMJI3ojZjTY05LlgTG1iSpJBJWBnUnqJDy7CWrbqrwiu0xYnDvB0kpK65wQMSOZtZBf7CFGBzHZpI+PP8FcDUM6TlliErd/vPNX8Dq4zGucMAAAA=[0m[Pipeline] stage
[2021-09-01T05:15:30.153Z] [8mha:////4MRJy3ZEs+6Kzu/Ae5hCwtjEC8O5+zISy6ctFmqjG1beAAAAqh+LCAAAAAAAAP9tjTEOwjAUQ39bdWBl5BApDCAhxMQaZeEEIQ0hbfR/SVLSiRNxNe5ASyUmPFi2Jeu93lAGD0fyhjUaW4tBWda53oyJJfLt1VFiDV2YIgzkNBM6Car1aa6CooZZWQ4Fh4VG5ShYNBGWvJEPWTmJpjpHP24HDrmt7/CEjI/kKH38lsHD6tbXgfDH+QuAoYtQbLb7yXfr6Vt+ADgDipbEAAAA[0m[Pipeline] { (Run Unit Test)
[2021-09-01T05:15:30.164Z] Stage "Run Unit Test" skipped due to earlier failure(s)
[2021-09-01T05:15:30.181Z] [8mha:////4Gmx4L+gYIDL7cTug7e+tE5ss37A2zBmC5oy+JzETvfxAAAApR+LCAAAAAAAAP9tjTESgjAQRRcYC1tLD7FIY+NY0WbSeIIYYgxkdjEJQuWJvJp3kIEZK3/x57/fvPcHNjHAmYPF1lDnKGqHvR/svHDk0N08j9jyFTVTZG9QmlFyY+oVJScDa7IcCgFbQ9pzdGQT7ESrnqr0imx5SWH+TgJy1zzgBZmYzUmFtMAUYH8fmsj08/wVwNT3CYrqWC19+AK7+KQdwAAAAA==[0m[Pipeline] }
[2021-09-01T05:15:30.208Z] [8mha:////4GWKATGNFzWY+0aB6Y+nWYmvFWFygsMMG+i+kFOQMIJQAAAApx+LCAAAAAAAAP9tjTEOwjAQBC+JKGgpeYQjkEBCiIrWcsMLjG2ME+su2A5JxYv4Gn/AEImKLVY728zzBbMY4EDBssZg6zAqxzrf27zYQKG9eBpYQ2emCCN5w4QZBGlznFBQMjClKKHiMDeoPEWHNsGCN/Iuay/R1qcU8rfnUDp9gwcUPJuTDOkLY4DltdeR8Of5K4Cx6xJUq+3605vdG0EJvX7AAAAA[0m[Pipeline] // stage
[2021-09-01T05:15:30.254Z] [8mha:////4ItDKGWaEgFDrm+VD7M3e4A32n5Dw9k0BVdzbj/1f2+6AAAAqR+LCAAAAAAAAP9tjbEOgjAURR8QBldHP6JITFwMk2vD4hfUUmuheQ/bhzD5Rf6a/yBK4uSd7rnJzXm+II8BKgpWtAY7h1E70fvBzk2MFLqLp1G0dBaaMJI3ojZjTY05LlgTG1iSpJBJWBnUnqJDy7CWrbqrwiu0xYnDvB0kpK65wQMSOZtZBf7CFGBzHZpI+PP8FcDUM6TlliEr97vPNX8DULW1KcMAAAA=[0m[Pipeline] stage
[2021-09-01T05:15:30.263Z] [8mha:////4JEZahzrBG0l+k9Wyi8qoJB2sCnvLdm4QEIGYicvlcD9AAAAqB+LCAAAAAAAAP9tjTEOwjAUQ39bdWBl5BCpEIgFdWKNunCCkISQNPq/JCntxIm4GnegtBITHizbkvVebyhjgJqCYU5jazFKyzrfmymxgUJ79TQwRxcmCSN5zRo9NKT0aakNJQ2LshwKDiuN0lO0aBKsuRMPUXmBpjqnMG1HDrlVd3hCxidyEiHNZQywufUqEv44fwEwdgmK7WE3+/77LT+BxeZaxAAAAA==[0m[Pipeline] { (Archive artifacts)
[2021-09-01T05:15:30.276Z] Stage "Archive artifacts" skipped due to earlier failure(s)
[2021-09-01T05:15:30.293Z] [8mha:////4NHH4h9FrSnX7Txayv4JtCQDgWqoC+OfxefFN8m9f5EpAAAAph+LCAAAAAAAAP9tjTEOwjAQBC+JKGgpeYQjJKBBqWitNLzA2MbYse6C7eBUvIiv8QeiRKJii9XONvP+wCoGaCgY5jR2FqO0rPeDmRbLFLqbp8wcXZkkjOQ1a3VuSenzgi0lDUuKEioOa43SU7RoEmy4E09Re4GmvqQwfScOpVUPeEHBJ3MSIc0wBtjeBxUJf56/Ahj7PkG1Ox7m3n8BsRiG7sAAAAA=[0m[Pipeline] }
[2021-09-01T05:15:30.330Z] [8mha:////4H8E5o6bkC5/F1/e0/BtkLb74+rtPHFn3RKAql/1D+iLAAAAph+LCAAAAAAAAP9tjbEOgjAURR8QB1dHP6LEmLAYJ9ami19QS62F5j18LcLkF/lr/oMEEifvcHPPXc77A5vIcCZ2orXYeYzGiz4Mbl5iJO5ugUbR0lUYwkjBCmVHRY2tV1SULKzJcigkbC2aQNGjS7CTrX7qMmh05SXx/J0k5L55wAsyOZuT5rTAxLC/D00k/Hn+CmDq+wTFoaqWPn4Bj5cKQcAAAAA=[0m[Pipeline] // stage
[2021-09-01T05:15:30.375Z] [8mha:////4CbAGMzyvAao/w1CgFsFgZpgITMvZKgQ7SES06hhi9lmAAAAqB+LCAAAAAAAAP9tjTESgjAQRRcYCltLDxGk0cKhss3QeIIYYgxkdjFZhMoTeTXvIMqMlb/678/8ec8X5DFARcGK1mDnMGonej/YuYmRQnfxNIqWzkITRvJG1GasqTHHBWtiA0uSFDIJK4PaU3RoGdayVXdVeIW2OHGYt4OE1DU3eEAiZzOrwF+YAmyuQxMJf56/Aph6hrTcMmTlbv+55m9G9ySywwAAAA==[0m[Pipeline] stage
[2021-09-01T05:15:30.384Z] [8mha:////4Fl608zibguh9QaRUHBXjADV8CNvvHPxE5sihuYrMigGAAAAqB+LCAAAAAAAAP9tjTEOwjAUQ39bdWBl5BCpWAAJdWKNunCCkISQNPq/JCntxIm4GnegtBITHizbkvVebyhjgJqCYU5jazFKyzrfmymxgUJ79TQwRxcmCSN5zRo9NKT0aakNJQ2LshwKDiuN0lO0aBKsuRMPUXmBpjqnMG1HDrlVd3hCxidyEiHNZQywufUqEv44fwEwdgmK7W4/++H7LT+FPiIuxAAAAA==[0m[Pipeline] { (Clean up)
[2021-09-01T05:15:30.395Z] Stage "Clean up" skipped due to earlier failure(s)
[2021-09-01T05:15:30.412Z] [8mha:////4Jg2azELWZBbWDizbXV1cLwez0LYzSW5q/XnxSDtQdCNAAAAph+LCAAAAAAAAP9tjTEOwjAQBC+JKGgpeYQjGgRCqWitNLzA2MbYse6C7eBUvIiv8QeiRKJii9XONvP+wCoGaCgY5jR2FqO0rPeDmRbLFLqbp8wcXZkkjOQ1a3VuSenzgi0lDUuKEioOa43SU7RoEmy4E09Re4GmvqQwfScOpVUPeEHBJ3MSIc0wBtjeBxUJf56/Ahj7PkG12x/nPnwB7j6QIMAAAAA=[0m[Pipeline] }
[2021-09-01T05:15:30.439Z] [8mha:////4KknWbs+xlEHIcWHeUZq5QDx97ko6Yxq6DC9AsjbEBuzAAAAph+LCAAAAAAAAP9tjTESgjAQRRcYC1tLDxG0kcKxss3QeIIIMSZkdmOyCJUn8mreQZQZK3/x57/fvOcLFinCgaIRTmNnMTVWBN+baYmBYnfxNAhHZ9EQJvJa1HqoqdXHGWtiDXOyHAoJS42Np2TRMKykU3dVeoWmPHGcvr2E3LY3eEAmJzOryF8YI6yvfZsIf56/AhhDYCi21ebTu+oNMmYd6cAAAAA=[0m[Pipeline] // stage
[2021-09-01T05:15:30.484Z] [8mha:////4Ab53/u0O/nbo7G3daAGNI7ArDgBwHVXqa+HMYM1zDDkAAAAqB+LCAAAAAAAAP9tjTEOwjAQBC+JUtBS8giHVBSIitZKwwuMY4wT6y7YF5KKF/E1/oAhEhVb7ay0mucLyhjgQMGKzmDvMGonBj/a1MREob94mkRHZ6EJI3kjGjM11Jrjgg2xgSVZDoWElUHtKTq0DGvZqbuqvEJbnTikbS8hd+0NHpDJZGYV+AtzgM11bCPhz/NXAPPAkNdbhqLe1Z9r+QbvHwrCwwAAAA==[0m[Pipeline] stage
[2021-09-01T05:15:30.493Z] [8mha:////4FxtFdKLA4bmkmj2YXqJfcLp1y+Dvvkxm/vrOuaJC+6KAAAAqB+LCAAAAAAAAP9tjbEOgjAYhH8gDK6OPkQJLg7GibVh8QlqqbXQ/D+2xTL5RL6a72CFxMkbLneXXL7XG0rv4EROs17hYNBLw0Y76ZRYJDdcLUXW04VJQk9WsVbFljrVrLWloGBVlkPBYaNQWvIGdYAt78VDVFagrs7Bpe3IITfdHZ6Q8UQOwoWlzA52t6nzhD/OXwDMY4CiPtSL77/f8gMyamUbxAAAAA==[0m[Pipeline] { (Declarative: Post Actions)
[2021-09-01T05:15:30.558Z] [8mha:////4J8gPtVW0oZqB2ielh7cmF26vQ9j6DrlrkxHn+yB6A22AAAAph+LCAAAAAAAAP9tjTEOwjAQBC+JKGgpeYQjoKBAqWitNLzA2MbYse6C7eBUvIiv8QeiRKJii9XONvP+wCoGaCgY5jR2FqO0rPeDmRbLFLqbp8wcXZkkjOQ1a3VuSenzgi0lDUuKEioOa43SU7RoEmy4E09Re4GmvqQwfScOpVUPeEHBJ3MSIc0wBtjeBxUJf56/Ahj7BNXuuJ/70H8BRv3BbMAAAAA=[0m[Pipeline] echo
[2021-09-01T05:15:30.567Z] Stopping CPU Monitoring ...
[2021-09-01T05:15:30.594Z] [8mha:////4NpEkXiWS/M68D8kLAbr47NOabpgiZmm0A6l8z2Z0ZJ/AAAAph+LCAAAAAAAAP9tjTEOwjAQBC+JKGgpeYQjEBIFSkVrpeEFxjbGjnUXbAen4kV8jT8QJRIVW6x2tpn3B1YxQEPBMKexsxilZb0fzLRYptDdPGXm6MokYSSvWatzS0qfF2wpaVhSlFBxWGuUnqJFk2DDnXiK2gs09SWF6TtxKK16wAsKPpmTCGmGMcD2PqhI+PP8FcDYJ6h2x/3ch/4LgWuAI8AAAAA=[0m[Pipeline] echo
[2021-09-01T05:15:30.603Z] Installing cpuBuildMonitoring.ps1 ...
[2021-09-01T05:15:30.621Z] [8mha:////4HigB1RLhiXyyBYql4qW14aOGgIn1Htgxbso01Nbj7DuAAAAph+LCAAAAAAAAP9tjTEOwjAQBC+JKGgpeYQjkBAFSkVrpeEFxjbGjnUXbAen4kV8jT8QJRIVW6x2tpn3B1YxQEPBMKexsxilZb0fzLRYptDdPGXm6MokYSSvWatzS0qfF2wpaVhSlFBxWGuUnqJFk2DDnXiK2gs09SWF6TtxKK16wAsKPpmTCGmGMcD2PqhI+PP8FcDYJ6h2x/3ch/4LwFqbOsAAAAA=[0m[Pipeline] libraryResource
[2021-09-01T05:15:30.648Z] [8mha:////4ERXNuTW2uS4TFUU0hFEIc9BEJIK9v4ihTETflR984IqAAAAph+LCAAAAAAAAP9tjTEOwjAQBC+JKGgpeYQjKKBAqWitNLzA2MbYse6C7eBUvIiv8QeiRKJii9XONvP+wCoGaCgY5jR2FqO0rPeDmRbLFLqbp8wcXZkkjOQ1a3VuSenzgi0lDUuKEioOa43SU7RoEmy4E09Re4GmvqQwfScOpVUPeEHBJ3MSIc0wBtjeBxUJf56/Ahj7BNXuuJ/70H8BAwm2EcAAAAA=[0m[Pipeline] writeFile
[2021-09-01T05:15:30.687Z] [8mha:////4PWJW67IWKGKw/4rEcMVFiIRAGtQ3Eg6jRzgTw+t+b4wAAAApR+LCAAAAAAAAP9tjTESgjAQRRcYC1tLDxFGGwrHijaTxhPEEGMgs4ubIFSeyKt5BxmYsfIXf/77zXt/YBMZzsROtBY7j9F40YfBzUuMxN0t0ChaugpDGClYoeyoqLH1ioqShTVZDoWErUUTKHp0CXay1U9dBo2uvCSev5OE3DcPeEEmZ3PSnBaYGPb3oYmEP89fAUx9guJQHZeu+i9COK0IwAAAAA==[0m[Pipeline] bat
[2021-09-01T05:15:30.990Z] 
[2021-09-01T05:15:30.990Z] c:\b\review_autosar>PowerShell -File C:\Tools\CpuBuildMonitoring\cpuBuildMonitoring.ps1                                                     -Mode stop                                                     -PerfLogs C:\temp\monitor -OutPutShare \\SI0VM4142.de.bosch.com\BuildPerformanceMonitor 
[2021-09-01T05:15:31.251Z] 
[2021-09-01T05:15:31.251Z] 
[2021-09-01T05:15:31.251Z]     Directory: C:\temp
[2021-09-01T05:15:31.251Z] 
[2021-09-01T05:15:31.251Z] 
[2021-09-01T05:15:31.251Z] Mode                LastWriteTime         Length Name                                                                  
[2021-09-01T05:15:31.251Z] ----                -------------         ------ ----                                                                  
[2021-09-01T05:15:31.251Z] d-----       01.09.2021     07:08                monitor                                                               
[2021-09-01T05:15:31.251Z] d-----       01.09.2021     07:08                monitor                                                               
[2021-09-01T05:15:31.251Z] d-----       01.09.2021     07:08                monitor                                                               
[2021-09-01T05:15:31.251Z] d-----       01.09.2021     07:08                monitor                                                               
[2021-09-01T05:15:31.519Z] STDOUT of monitoring process:
[2021-09-01T05:15:31.519Z] ##### Start of log #####
[2021-09-01T05:15:31.519Z] 
[2021-09-01T05:15:31.519Z] ##### End of log #####
[2021-09-01T05:15:31.519Z] STDERR of monitoring process:
[2021-09-01T05:15:31.519Z] ##### Start of log #####
[2021-09-01T05:15:31.519Z] ##### End of log #####
[2021-09-01T05:15:31.519Z] 
[2021-09-01T05:15:31.519Z] 
[2021-09-01T05:15:31.519Z]     Directory: \\SI0VM4142.de.bosch.com\BuildPerformanceMonitor\Git_platform_hydra_autosar_on-review
[2021-09-01T05:15:31.519Z] 
[2021-09-01T05:15:31.519Z] 
[2021-09-01T05:15:31.519Z] Mode                LastWriteTime         Length Name                                                                  
[2021-09-01T05:15:31.519Z] ----                -------------         ------ ----                                                                  
[2021-09-01T05:15:31.519Z] d-----       01.09.2021     07:15                10085                                                                 
[2021-09-01T05:15:31.519Z] CPU monitoring files have been published to
[2021-09-01T05:15:31.519Z] => \\SI0VM4142.de.bosch.com\BuildPerformanceMonitor\Git_platform_hydra_autosar_on-review\10085
[2021-09-01T05:15:31.519Z] 
[2021-09-01T05:15:31.519Z] 
[2021-09-01T05:15:31.797Z] [8mha:////4PM8Mws6Me4iHYTl6dOwAbETRc2n2ezREjLGb/wgGAKwAAAAph+LCAAAAAAAAP9tjTEOwjAQBC+JKGgpeYQjaEBCqWitNLzA2MbYse6C7eBUvIiv8QeiRKJii9XONvP+wCoGaCgY5jR2FqO0rPeDmRbLFLqbp8wcXZkkjOQ1a3VuSenzgi0lDUuKEioOa43SU7RoEmy4E09Re4GmvqQwfScOpVUPeEHBJ3MSIc0wBtjeBxUJf56/Ahj7BNXusJ/72H8BjSQ1j8AAAAA=[0m[Pipeline] echo
[2021-09-01T05:15:31.806Z] ... CPU Monitoring stopped!
[2021-09-01T05:15:31.863Z] [8mha:////4J6Jf1hgLaCpdByb58T7MqzZgfmMHsXh3H0nL9OZN6olAAAAph+LCAAAAAAAAP9tjTEOwjAQBC+JKGgpeYQjaBBCqWitNLzA2MbYse6C7eBUvIiv8QeiRKJii9XONvP+wCoGaCgY5jR2FqO0rPeDmRbLFLqbp8wcXZkkjOQ1a3VuSenzgi0lDUuKEioOa43SU7RoEmy4E09Re4GmvqQwfScOpVUPeEHBJ3MSIc0wBtjeBxUJf56/Ahj7BNXusJ/72H8BzBUulsAAAAA=[0m[Pipeline] echo
[2021-09-01T05:15:31.872Z] The node Exec706_SI-ZENG06W010_4_134 is online: false
[2021-09-01T05:15:31.899Z] [8mha:////4NSw/HLqNvdFfEpx96UjIVpbyf0C7R2NFZwk9bKuDGPOAAAAph+LCAAAAAAAAP9tjTEOwjAQBC+JKGgpeYQDNCAhKlrLDS8wtjFOrLtgOyQVL+Jr/AFDJCq2WO1sM88XzGKAAwXLGoOtw6gc63xv82IDhfbiaWANnZkijOQNE2YQpM1xQkHJwJSihIrD3KDyFB3aBAveyLusvURbn1LI355D6fQNHlDwbE4ypC+MAZbXXkfCn+evAMYuQbXebj69W3VvuOmwTMAAAAA=[0m[Pipeline] echo
[2021-09-01T05:15:31.908Z] Build failed. Deleting workspace.
[2021-09-01T05:15:31.935Z] [8mha:////4CjpVJZIYOkeE0c2NZRooiLCz4XS7RXkO3IjJ/xujBHwAAAAqR+LCAAAAAAAAP9tjTEOwjAUQ39bdWBl5BCpygISYmKNunCC0ISQNvq/JCnpxIm4GncgpRITHizbkvVebyi9gyM5zTqFvUHfGjbYUafEIrn+aimyji6sJfRkFWtUbEiq01IbCgoWZTkUHFYKW0veoA6w5p14iMoK1NU5uLQdOORG3uEJGU/kIFz4lsnB5jZKT/jj/AXANAQo6t129n09f8sPhbonHMQAAAA=[0m[Pipeline] dir
[2021-09-01T05:15:31.935Z] Running in c:/b/review_autosar
[2021-09-01T05:15:31.944Z] [8mha:////4EgOaU/Vfl4Ft6t6AWtTyB6KSEwj+Dm8d7wbWhXUarymAAAAqB+LCAAAAAAAAP9tjbEOgjAYhH8gDK6OPkQJTibGibVh8QlqqbXQ/D+2xTL5RL6a72CFxMkbLneXXL7XG0rv4EROs17hYNBLw0Y76ZRYJDdcLUXW04VJQk9WsVbFljrVrLWloGBVlkPBYaNQWvIGdYAt78VDVFagrs7Bpe3IITfdHZ6Q8UQOwoWlzA52t6nzhD/OXwDMY4CiPtSL77/f8gPuz13KxAAAAA==[0m[Pipeline] {
[2021-09-01T05:15:31.966Z] [8mha:////4KRG/eT9MBtwafzpTZWR5KHfmnUSYKjDZ1W2GZ/TKS5cAAAAph+LCAAAAAAAAP9tjTEOwjAQBC+JKGgpeYQjoEFCqWitNLzA2MbYse6C7eBUvIiv8QeiRKJii9XONvP+wCoGaCgY5jR2FqO0rPeDmRbLFLqbp8wcXZkkjOQ1a3VuSenzgi0lDUuKEioOa43SU7RoEmy4E09Re4GmvqQwfScOpVUPeEHBJ3MSIc0wBtjeBxUJf56/Ahj7BNXuuJ/70H8BqnUkuMAAAAA=[0m[Pipeline] deleteDir
[2021-09-01T05:16:21.924Z] [8mha:////4GFyynYrce8t6swgHTD6tSL92FAxjcyHPKfSzcRY4b8fAAAAph+LCAAAAAAAAP9tjTEOwjAQBC+JKGgpeYQjEAUSSkVrpeEFxjbGjnUXbAen4kV8jT8QJRIVW6x2tpn3B1YxQEPBMKexsxilZb0fzLRYptDdPGXm6MokYSSvWatzS0qfF2wpaVhSlFBxWGuUnqJFk2DDnXiK2gs09SWF6TtxKK16wAsKPpmTCGmGMcD2PqhI+PP8FcDY9wmq3fEw9/4LjImbKsAAAAA=[0m[Pipeline] }
[2021-09-01T05:16:21.943Z] [8mha:////4ML7zUu32h9YZyxbVDI73odmOfHG3s4p7Ro8lmnSLLHiAAAAph+LCAAAAAAAAP9tjTEOwjAQBC+JKGgpeYSjFEhIiCqtlYYXmNgYO9adsR2SihfxNf6AlUhUbLHa2WbeH9jEAGcKmlmFg8HYG+bdqPNiE4Xh5mhilq6sJ4zkFOvU1JFU7YodJQVrihIqDluFvaNoUCfYcSueonYCdX1JIX8nDqWRD3hBwbM5iZAWmAPs76OMhD/PXwHM3ieomuNh6eYLgtPlFcAAAAA=[0m[Pipeline] // dir
[2021-09-01T05:16:21.962Z] [8mha:////4Gw7ampsCshxHJRSKPdDSGAn/c2f3A+mcvHqxT6/ffh+AAAApx+LCAAAAAAAAP9tjTEOwjAQBC+JKGgpeYQjKAAJUdFabniBsY1xYt0F2yGpeBFf4w8YIlGxxWpnm3m+YBYDHChY1hhsHUblWOd7mxcbKLQXTwNr6MwUYSRvmDCDIG2OEwpKBqYUJVQc5gaVp+jQJljwRt5l7SXa+pRC/vYcSqdv8ICCZ3OSIX1hDLC89joS/jx/BTB2XYJqtdt8ert+A2qEnDrAAAAA[0m[Pipeline] }
[2021-09-01T05:16:21.981Z] [8mha:////4K3FEIgwA2Eu4x/pD8IIBeZRKwSOIaFfh5CugVt9A5nXAAAAph+LCAAAAAAAAP9tjTEOwjAQBC+JKGgpeYSjVEFCVLRWGl5gYmOcWHfBvpBUvIiv8QcMkajYYrWzzTxfsIoBDhSs6Az2DmPrxOBHm5aYKPQXT5Po6CxawkjeiMZMDWlzXLAhNrAky6GQsDbYeooOLcNGduquSq/QlicO6dtLyJ2+wQMymcysAn9hDrC9jjoS/jx/BTAPA0NR7epP19UbZN7iBcAAAAA=[0m[Pipeline] // stage
[2021-09-01T05:16:22.000Z] [8mha:////4IXxchAMrJwt63OY5uIKPHq0Eqdi6xutyOEcHowyk5eCAAAApR+LCAAAAAAAAP9tjTESgjAQRRcYC1tLDxGkY8axos3QeIIYYgxkdjFZhMoTeTXvYEZmrPzVf695rzdsYoATBSt6g4PDqJ0Y/WTTEzOF4eppFj1dhCaM5I1ozdxSZ5oVW2ID67IcCglbg9pTdGgZdrJXD1V6hbY8c0juKCF33R2ekMlUZhX4C0uA/W3qIuGv8zcAyzgyFFVdM+TV4QORShODvwAAAA==[0m[Pipeline] }
[2021-09-01T05:16:22.032Z] [8mha:////4EDcJtbHfDmM1jPM5JG71HyFcZLKRjygD+kg8GxzA4MfAAAApB+LCAAAAAAAAP9tjTEOwjAQBC+JKGgpeYQjOiJERWul4QUmNsaJdWfsC07Fi/gafyAiEhVb7UwzrzesUoQjRSt6g4PD1DkR/GjnJzLF4eopi54uoiNM5I1oTW5Jm9OCLbGBZUUJlYS1wc5TcmgZNrJXD1V7hbY+c5zdQULp9B2eUMi5zCryF6YI29uoE+Gv8zcAUwgM1W7fMBTNB0DUM9e+AAAA[0m[Pipeline] // timeout
[2021-09-01T05:16:22.060Z] [8mha:////4IzqdYfVzimxlB+qkCwd83JtMPTZIabmJVBMN6Z/id62AAAApB+LCAAAAAAAAP9tjTEOwjAQBM+JKGgpeYQDHQhR0VppeIFxjHFi3QX7glPxIr7GH4iIRMVWO9PM6w2LFOFI0cnWYucxGS/7MLjpyUyxuwbKsqWLNISJgpW1zTU19jRjTWxhniigVLC0aAIlj45hpVr90FXQ6Kozx8kdFBS+ucMThJrKrCN/YYywvg1NIvx1/gZg7HuGcrvfMIjdBwJVeGa+AAAA[0m[Pipeline] }
[2021-09-01T05:16:22.088Z] [8mha:////4AYgIx77O3hB+ZHU4VMkC1Hew7J2a371cw/3kSCNvInZAAAApB+LCAAAAAAAAP9tjTEOwjAQBM+JKGgpeYSjVAghKlorDS8wsTFOrLtgX3AqXsTX+AMRkajYameaeb1hlSIcKTrZWew9ptbLIYxufjJT7K+BsuzoIlvCRMHKxuaGjD0t2BBbWCYKKBWsLbaBkkfHsFGdfugqaHTVmePsDgoKb+7wBKHmMuvIX5gibG+jSYS/zt8ATMPAUNb7mkHsPiNhp8u+AAAA[0m[Pipeline] // timestamps
[2021-09-01T05:16:22.117Z] [8mha:////4IqyqQnCJZLxe1T0ppeXZ4boryqlsQ+wPfwRSvJhVZnvAAAApR+LCAAAAAAAAP9tjTEOwjAQBM+JKGgpeYQjKJAQoqK10vAC4xjjxLoL9gWn4kV8jT8QEYmKrXammdcbFinCkaKTrcXOYzJe9mFw05OZYncNlGVLF2kIEwUra5trauxpxprYwjxRQKlgadEESh4dw0q1+qGroNFVZ46TOygofHOHJwg1lVlH/sIYYX0bmkT46/wNwNj3DOVmv2UQuw9lKwD7vgAAAA==[0m[Pipeline] }
[2021-09-01T05:16:22.145Z] [8mha:////4MgwKLsu+kIe0oribt+SmLW5cf9Md7r3PEcXQBWtODwYAAAApR+LCAAAAAAAAP9tjTEOwjAQBM+JKGgpeYQjhCgQoqK10vAC4xjjxLoL9gWn4kV8jT8QEYmKrXammdcbFinCkaKTrcXOYzJe9mFw05OZYncNlGVLF2kIEwUra5trauxpxprYwjxRQKlgadEESh4dw0q1+qGroNFVZ46TOygofHOHJwg1lVlH/sIYYX0bmkT46/wNwNj3DOVmv2UQuw9vU2lfvgAAAA==[0m[Pipeline] // ws
[2021-09-01T05:16:22.172Z] [8mha:////4PVfzvVwq9LcUek1xnMFB3Z8qj5B58kW/OUn6+aXwxI3AAAApR+LCAAAAAAAAP9tjTEOwjAQBC+JKGgpeYQjpDQIUdFaaXiBiY1xYt0Z+4JT8SK+xh+IiETFVjvTzOsNqxThSNGK3uDgMHVOBD/a+YlMcbh6yqKni+gIE3kjWpNb0ua0YEtsYFlRQiVhbbDzlBxaho3s1UPVXqGtzxxnd5BQOn2HJxRyLrOK/IUpwvY26kT46/wNwBQCQ7XbNwxF8wHpv06avgAAAA==[0m[Pipeline] }
[2021-09-01T05:16:22.210Z] [8mha:////4CG2JzOvPo7SZCasuvd0vAtOoho+nB/f/L9Vdi+UIeNHAAAApR+LCAAAAAAAAP9tjTEOwjAQBM+JKGgpeYQjhCgQoqK10vAC4xjjxLoL9gWn4kV8jT8QEYmKrXammdcbFinCkaKTrcXOYzJe9mFw05OZYncNlGVLF2kIEwUra5trauxpxprYwjxRQKlgadEESh4dw0q1+qGroNFVZ46TOygofHOHJwg1lVlH/sIYYX0bmkT46/wNwNj3DOVmv2MQ2w/6A0o5vgAAAA==[0m[Pipeline] // node
[2021-09-01T05:16:22.257Z] [8mha:////4EHIGd99bFbypuLDTfo9nrdzt8eI5r+GPwNSWmOtmLOdAAAApR+LCAAAAAAAAP9tjTEOwjAQBM+JKGgpeYQjKJAQoqK10vAC4xjjxLoL9gWn4kV8jT8QEYmKrXammdcbFinCkaKTrcXOYzJe9mFw05OZYncNlGVLF2kIEwUra5trauxpxprYwjxRQKlgadEESh4dw0q1+qGroNFVZ46TOygofHOHJwg1lVlH/sIYYX0bmkT46/wNwNj3DOVmv2MQ2w+8Se0JvgAAAA==[0m[Pipeline] End of Pipeline
[2021-09-01T05:16:22.274Z] ERROR: script returned exit code 1
[2021-09-01T05:16:22.308Z] Finished: FAILURE
