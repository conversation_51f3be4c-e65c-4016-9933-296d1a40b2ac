# Do not use quotes for strings in the config file. The config reader will read the values from the file as strings (unless instructed otherwise) and will not remove the quotes, although ENV variables may require quotes.
# Qdrant UI > https://xc-cp-qdrant-0.apps.606a86d3bb.ccsd.ipz001.internal.bosch.cloud/dashboard
# Configurations can be set as an ENVIRONMENT variable. ENV variables override the values in the config file.
# ENV variable format (variable name in UPPER_CASE, value in normal case): <SECTION>_<OPTION>=<value>
# e.g. VECTORS_HOST=path:../../qdrant_vectors_instance or PROMPT_GENERATOR_PORT=0

[NEURAL_SEARCH]

# The Qdrant server address
# ENV var: NEURAL_SEARCH_HOST=localhost
host = https://xc-cp-qdrant-0.apps.606a86d3bb.ccsd.ipz001.internal.bosch.cloud

# The Qdrant server port
# ENV var: NEURAL_SEARCH_PORT=6333
port = 443

# The Qdrant server API key, unless in a very secure environment it should always be specified in the ENV variables.
# ENV var: NEURAL_SEARCH_API_KEY=<api_key>
api_key = <for_security_use_env_please>

# Recommended is 3. The more threads, the faster the search, to many threads may risk overloading the QDRANT server.
# ENV var: NEURAL_SEARCH_THREADS=<number_of_threads>
threads = 3

# Qdrant collection name which is used to store the good logs embeddings.
# ENV var: NEURAL_SEARCH_GOOD_COLLECTION=<number_of_threads>
good_collection = good_collection

[PROMPT_GENERATOR]

# The Qdrant server address
# ENV var: PROMPT_GENERATOR_HOST=path:../../qdrant_prompt_generator_instance
host = path:./qdrant_prompt_generator_instance

# The Qdrant server port
# ENV var: PROMPT_GENERATOR_PORT=0
port = 0

# The Qdrant server API key
# ENV var: PROMPT_GENERATOR_API_KEY=n09d21d13bc11d99b112fa3c1da895d0
api_key = n09d21d13bc11d99b112fa3c1da895d0

# Max number of characters the LLM will use for the description section.
# ENV var: PROMPT_GENERATOR_MAX_DESCRIPTION_LENGTH=600
max_description_length = 1200

# Max number of characters the LLM will use for the solution section.
# ENV var: PROMPT_GENERATOR_MAX_SOLUTION_LENGTH=600
max_solution_length = 1200

### Remember that ENVIRONMENT variables will override the values from the config file. See example of how to set in the comment below.

### Linux

# Bash set env variables:
# export VECTORS_HOST='path:../../qdrant_vectors_instance'
# export VECTORS_PORT=0
# export VECTORS_API_KEY='b04598d92d058c917f5e36dc3e4e03e8'
# export NEURAL_SEARCH_HOST='localhost'
# export NEURAL_SEARCH_PORT=6333
# export NEURAL_SEARCH_API_KEY=''
# export PROMPT_GENERATOR_HOST='path:../../qdrant_prompt_generator_instance'
# export PROMPT_GENERATOR_PORT=0
# export PROMPT_GENERATOR_MAX_DESCRIPTION_LENGTH=600
# export PROMPT_GENERATOR_MAX_SOLUTION_LENGTH=600

# Bash unset env variables:
# unset VECTORS_HOST
# unset VECTORS_PORT
# unset VECTORS_API_KEY
# unset NEURAL_SEARCH_HOST
# unset NEURAL_SEARCH_PORT
# unset NEURAL_SEARCH_API_KEY
# unset PROMPT_GENERATOR_HOST
# unset PROMPT_GENERATOR_PORT
# unset PROMPT_GENERATOR_MAX_DESCRIPTION_LENGTH
# unset PROMPT_GENERATOR_MAX_SOLUTION_LENGTH

### Windows

# Powershell set env variables:
# $env:VECTORS_HOST='path:../../qdrant_vectors_instance'
# $env:VECTORS_PORT=0
# $env:VECTORS_API_KEY='b04598d92d058c917f5e36dc3e4e03e8'
# $env:NEURAL_SEARCH_HOST='localhost'
# $env:NEURAL_SEARCH_PORT=6333
# $env:NEURAL_SEARCH_API_KEY=''
# $env:PROMPT_GENERATOR_HOST='path:../../qdrant_prompt_generator_instance'
# $env:PROMPT_GENERATOR_PORT=0
# $env:PROMPT_GENERATOR_MAX_DESCRIPTION_LENGTH=600
# $env:PROMPT_GENERATOR_MAX_SOLUTION_LENGTH=600

# Powershell unset env variables:
# $env:VECTORS_HOST=''
# $env:VECTORS_PORT=''
# $env:VECTORS_API_KEY=''
# $env:NEURAL_SEARCH_HOST=''
# $env:NEURAL_SEARCH_PORT=''
# $env:NEURAL_SEARCH_API_KEY=''
# $env:PROMPT_GENERATOR_HOST=''
# $env:PROMPT_GENERATOR_PORT=''
# $env:PROMPT_GENERATOR_MAX_DESCRIPTION_LENGTH=''
# $env:PROMPT_GENERATOR_MAX_SOLUTION_LENGTH=''

# Command Prompt set env variables:
# set VECTORS_HOST=path:../../qdrant_vectors_instance
# set VECTORS_PORT=0
# set VECTORS_API_KEY=b04598d92d058c917f5e36dc3e4e03e8
# set NEURAL_SEARCH_HOST=localhost
# set NEURAL_SEARCH_PORT=6333
# set NEURAL_SEARCH_API_KEY=
# set PROMPT_GENERATOR_HOST=path:../../qdrant_prompt_generator_instance
# set PROMPT_GENERATOR_PORT=0
# set PROMPT_GENERATOR_MAX_DESCRIPTION_LENGTH=600
# set PROMPT_GENERATOR_MAX_SOLUTION_LENGTH=600

# Command Prompt unset env variables:
# set VECTORS_HOST=
# set VECTORS_PORT=
# set VECTORS_API_KEY=
# set NEURAL_SEARCH_HOST=
# set NEURAL_SEARCH_PORT=
# set NEURAL_SEARCH_API_KEY=
# set PROMPT_GENERATOR_HOST=
# set PROMPT_GENERATOR_PORT=
# set PROMPT_GENERATOR_MAX_DESCRIPTION_LENGTH=
# set PROMPT_GENERATOR_MAX_SOLUTION_LENGTH=
