{"errors": ["abort", "bad", "break", "cannot open", "corrupt", "crash", "critical", "damage", "defect", "degrade", "deny", "disconnect", "disrupt", "diverge", "does not exist", "errno", "error", "exceed", "exception", "expire", "fail", "failure", "fatal", "fault", "faulty", "forbidden", "halt", "impair", "inaccessible", "incompatible", "inoperative", "interrupt", "invalid", "malformed", "malfunction", "misalign", "misbehave", "miscalculate", "mismatch", "traceback", "missing", "cannot run", "not found", "null", "obstruct", "out of range", "overflow", "problem", "refuse", "raise", "reject", "stop", "terminate", "timeout", "unable", "unauthorized", "unavailable", "underflow", "unexpected", "unreachable", "unsound", "unstable", "violate", "build failed", "compilation failed", "compile error", "linker error", "linking failed", "dependency not found", "syntax error", "parse error", "import error", "module not found", "package not found", "permission denied", "access denied", "file not found", "out of memory", "segmentation fault", "assertion failed", "test failed", "deployment failed", "connection failed", "killed", "aborted", "core dumped"], "false_positives": ["fault interf", "default", "default value", "default setting", "error handling", "error recovery", "error message", "fault tolerance", "test error", "expected error", "error log", "error report", "checking for errors", "error check"], "hints": ["anomaly", "deprecate", "disallow", "discrepancy", "flaw", "glitch", "ineffective", "infeasible", "insufficient", "inconsistent", "issue", "mishap", "misstep", "neglect", "oversight", "stale", "unsuccessful", "warning", "block", "blunder", "deprecated", "obsolete", "outdated", "experimental", "beta", "performance warning", "memory warning", "compatibility warning", "security warning", "disk space warning", "unused", "unreachable", "todo", "fixme", "0 errors", "no errors", "no issues", "not an issue", "not a problem", "success", "complete"], "successes": ["build complete success", "build execution finished successfully", "build finished successfully", "build operation finished successfully", "build process completed successfully", "build result successful", "finished successfully", "job completed successfully", "pipeline finished successfully", "compilation successful", "build successful", "build completed", "tests passed", "deployment successful", "installation successful", "no errors found", "clean build"]}