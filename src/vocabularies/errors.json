{"errors_strings": ["abort", "anomaly", "bad", "blocked", "blunder", "break", "bug", "cannot open", "corrupt", "crash", "critical", "damage", "defect", "degraded", "denied", "deprecation", "disallowed", "disconnected", "discrepancy", "disrupted", "divergence", "does not exist", "errno", "error", "exceeded", "exception", "expired", "fail", "failed", "failure", "fatal", "fault", "faulty", "flaw", "forbidden", "glitch", "halted", "impaired", "inaccessible", "incompatible", "inconsistent", "ineffective", "infeasible", "inoperable", "insufficient", "interrupt", "invalid", "issue", "leak", "leaked", "malformed", "malfunction", "misaligned", "misbehaving", "miscalculation", "mishap", "mismatch", "missing", "misstep", "neglected", "not found", "null", "obsolete", "obstructed", "out of range", "overflow", "oversight", "problem", "refused", "rejected", "slip", "snag", "stale", "stopped", "terminated", "timeout", "unable", "unauthorized", "unavailable", "underflow", "unexpected", "unintended", "unreachable", "unreliable", "unsound", "unstable", "unsuccessful", "violation", "warning"]}