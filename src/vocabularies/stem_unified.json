{"errors": ["abort", "bad", "break", "cannot open", "corrupt", "crash", "critic", "damag", "defect", "degrad", "deni", "disconnect", "disrupt", "diverg", "does not exist", "errno", "error", "exceed", "except", "expir", "fail", "failur", "fatal", "fault", "faulti", "forbidden", "halt", "impair", "inaccess", "incompat", "inoper", "interrupt", "invalid", "malform", "malfunct", "misalign", "misbehav", "miscalcul", "mismatch", "traceback", "miss", "cannot run", "not found", "null", "obstruct", "out of rang", "overflow", "problem", "refus", "rais ", "reject", "stop", "termin", "timeout", "unabl", "unauthor", "unavail", "underflow", "unexpect", "unreach", "unsound", "unstabl", "violat"], "false_positives": ["faultinterf", "default"], "hints": ["anomali", "deprec", "disallow", "discrep", "flaw", "glitch", "ineffect", "infeas", "insuffici", "inconsist", "issu", "mishap", "misstep", "neglect", "oversight", "stale", "unsuccess", "warn", "block", "blunder"], "successes": ["build complet success", "build execut finish success", "build finish success", "build oper finish success", "build process complet success", "build result success", "finish success", "job complet success", "pipelin finish success"]}