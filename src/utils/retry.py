import logging
import time
import traceback
import uuid
from typing import Optional

from utils.logger import Logger


class Retry:
    """
    Retry context manager and iterator for retrying operations with optional exponential backoff.
    """

    NAME = "Retry"
    LOG_LEVEL = logging.INFO

    def __init__(
        self,
        max_retries: int = 5,
        retry_delay: float = 0.5,
        logger: Optional[Logger] = None,
        exponential: bool = False,
    ) -> None:
        """
        Initialize Retry helper.

        Args:
            max_retries (int, optional): Maximum retry attempts.
            retry_delay (float, optional): Delay between retries in seconds.
            logger (Logger, optional): Logger instance.
            exponential (bool, optional): Use exponential backoff if True.
        """
        self.exponential = exponential
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        # Use the provided logger or create a new one with a specific name to avoid using module-named logger
        self.logger = (
            logger
            if logger
            else Logger(
                name=f"Retry-{uuid.uuid4().hex}",
                logfile="Retry.log",
                level=self.LOG_LEVEL,
            )
        )
        self.attempt = 0  # Initialize attempt counter

    def __iter__(self) -> "Retry":
        """
        Return self as iterator, reset attempt counter.
        Returns:
            Retry: Self.
        """
        self.attempt = 0  # Reset attempts for the current retry loop
        return self

    def __next__(self) -> "Retry":
        """
        Iterator logic for retry handling.
        Returns:
            Retry: Self for next attempt.
        Raises:
            StopIteration: If max retries reached.
        """
        if self.attempt >= self.max_retries:
            raise StopIteration  # Stop retries when max attempts are reached
        self.attempt += 1
        return self  # Provide self for the next retry attempt

    def __enter__(self) -> "Retry":
        """
        Enter context manager.
        Returns:
            Retry: Self.
        """
        return self

    def __exit__(self, exc_type, exc_value, tb) -> bool:
        """
        Exit context manager, handle retry logic on exception.
        Args:
            exc_type: Exception type.
            exc_value: Exception value.
            tb: Traceback object.
        Returns:
            bool: True to suppress exception and retry, False to propagate.
        """
        if exc_type is not None:
            # Format and log the traceback
            exc_value_str = str(exc_value).replace("\n", " ")
            tb_str = "".join(traceback.format_tb(tb))
            exponent = self.retry_delay * self.attempt * self.attempt
            delay = exponent if self.exponential else self.retry_delay
            if self.attempt < self.max_retries:
                self.logger.warning(
                    f"{self.NAME}: Attempt {self.attempt} failed with error: {exc_value_str}, -- retrying in {delay} seconds..."
                )
                time.sleep(delay)
                return True  # Suppress the exception and retry
            else:
                self.logger.error(
                    f"{self.NAME}: Max retries reached. Unable to complete the operation: {exc_value_str}\nTraceback:\n{tb_str}"
                )
        return False  # Propagate the exception if max retries are reached
