import logging
import threading
import traceback
import uuid
from datetime import datetime, timed<PERSON><PERSON>
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union, Iterable

import onnxruntime as ort
from qdrant_client import models, QdrantClient
from qdrant_client.qdrant_fastembed import QdrantFastembedMixin

from utils.logger import Logger
from utils.retry import Retry


class QdrantUtil:
    """
    Helper and wrapper for interfacing with Qdrant Client vector database (dense/sparse embeddings).
    """

    NAME = "Qdrant"
    # Must be a fastembed compatible model > https://qdrant.github.io/fastembed/examples/Supported_Models/
    # all-MiniLM-L6-v2 is the fastest and most performant on both CPU and GPU on average
    EMBEDDING_MODEL = "sentence-transformers/all-MiniLM-L6-v2"
    # Sparse model e.g. "Qdrant/bm25" Must be a fastembed compatible, useful when we want to do fast text words like searcher
    SPARSE_EMBEDDING_MODEL = None
    # Default SSL certificate, it must match the API server's full chain of certificates
    SSL_CERT = str(Path(__file__).resolve().parent / Path("../certificates/qdrant_server_ssl_cert.crt"))
    LOG_LEVEL = logging.INFO

    def __init__(
        self,
        logger: Optional[Logger],
        host: str,
        port: Optional[int],
        api_key: Optional[str] = None,
        transformer: Optional[Any] = None,
        embedding_model: Optional[str] = None,
        sparse_embedding_model: Optional[str] = None,
        ssl_cert: Optional[str] = None,
    ) -> None:
        """
        Initialize Qdrant helper.

        Args:
            logger (Logger, optional): Logger instance.
            host (str): Qdrant host or 'memory' or 'path:<local_path>'.
            port (int, optional): Qdrant port.
            api_key (str, optional): API key.
            transformer (Any, optional): Transformer model.
            embedding_model (str, optional): Dense embedding model.
            sparse_embedding_model (str, optional): Sparse embedding model.
            ssl_cert (str, optional): SSL certificate path.
        """
        self.logger = (
            logger
            if logger
            else Logger(
                name=f"QdrantUtil-{uuid.uuid4().hex}",
                logfile="QdrantUtil.log",
                level=self.LOG_LEVEL,
            )
        )
        self.models = models
        self._api_key = api_key
        self.transformer = transformer
        self.lock = threading.Lock()
        self.available_text_models = list(QdrantFastembedMixin.list_text_models().keys())
        self.available_sparse_models = list(QdrantFastembedMixin.list_sparse_models().keys())
        self.dense_embedding_model = self.get_dense_embedding_model(embedding_model)
        self.sparse_embedding_model = self.get_sparse_embedding_model(sparse_embedding_model)
        self.vectors_config = None
        self.sparse_vectors_config = None
        self.embedding_options = self.get_embedding_options()
        # Make sure all internal properties are set before connecting to Qdrant
        self._ssl_cert = ssl_cert if ssl_cert else self.SSL_CERT
        self.qdrant = self.connect(host, port, self._api_key, self._ssl_cert)

    @staticmethod
    def check_fastembed_support(qdrant_client: QdrantClient) -> None:
        """
        Checks if the Qdrant client supports fastembed.

        Parameters:
            qdrant_client (QdrantClient): The Qdrant client instance to check.

        Raises:
            Exception: If the client does not support fastembed.
        """
        if not hasattr(qdrant_client, "get_fastembed_vector_params"):
            raise Exception(
                "Fastembed vector params not available on qdrant client. " "Use `pip install qdrant-client[fastembed]` to install the correct client with fast-embed support."
            )

    def get_dense_embedding_model(self, embedding_model: Optional[str]) -> str:
        """
        Returns the dense embedding model used by Qdrant.

        Parameters:
            embedding_model (Optional[str]): The embedding model name to use.
                                           If None, the default model will be used.

        Returns:
            str: The name of the dense embedding model to use.
        """
        if embedding_model:
            return embedding_model.strip()
        else:
            self.logger.debug(f"{self.NAME}: No dense embedding model specified, using default: '{self.EMBEDDING_MODEL}'")
            return self.EMBEDDING_MODEL

    def get_sparse_embedding_model(self, sparse_embedding_model: Optional[str]) -> Optional[str]:
        """
        Returns the sparse embedding model used by Qdrant.

        Parameters:
            sparse_embedding_model (Optional[str]): The sparse embedding model name to use.
                                                  If None, the default model will be used.

        Returns:
            Optional[str]: The name of the sparse embedding model to use, or None if no model is specified.
        """
        if sparse_embedding_model:
            return sparse_embedding_model.strip()
        else:
            self.logger.debug(f"{self.NAME}: No sparse embedding model specified, using default: '{self.SPARSE_EMBEDDING_MODEL}'")
            return self.SPARSE_EMBEDDING_MODEL

    @staticmethod
    def hide_key(api_key: Optional[str], reveal_ends: bool = False) -> str:
        """
        Hides the API key for logging purposes.

        Parameters:
            api_key (Optional[str]): The API key to be hidden.
            reveal_ends (bool, optional): If True, reveals the first and last characters of the key. Default is False.

        Returns:
            str: The hidden API key.
        """
        if api_key:
            hidden_key = "*" * len(api_key)
            if len(api_key) > 4 and reveal_ends:
                hidden_key = f"{api_key[:1]}{hidden_key[1:-1]}{api_key[-1:]}"
            return hidden_key
        return ""

    def get_embedding_options(self) -> Dict[str, List[str]]:
        """
        Gets the embedding options for ONNX runtime providers.

        This method determines the available ONNX runtime providers and selects the best ones
        based on a predefined preference order.

        Returns:
            Dict[str, List[str]]: A dictionary with the selected ONNX providers.
        """
        preferred = [
            "TensorrtExecutionProvider",  # NVIDIA GPU using TensorRT (faster than CUDA)
            "CUDAExecutionProvider",  # NVIDIA GPU using CUDA
            "ROCMExecutionProvider",  # AMD GPU using ROCm
            "DirectMLExecutionProvider",  # GPU via DirectML (useful for Windows, even AMD)
            "CoreMLExecutionProvider",  # Apple devices with CoreML
            "DmlExecutionProvider",  # Alias for DirectML (legacy)
            "OpenVINOExecutionProvider",  # Intel CPU, VPU (Neural Compute Stick, etc.)
            "AzureExecutionProvider",  # Azure cloud inference
            "CPUExecutionProvider",  # Fallback for CPU inference
        ]
        available = ort.get_available_providers()
        selected_providers = [p for p in preferred if p in available]
        self.logger.debug(f"{self.NAME}: FastEmbed using ONNX providers: {selected_providers}")
        return {"providers": selected_providers}

    def connect(self, host: str, port: Optional[int], api_key: Optional[str], verify: str) -> QdrantClient:
        """
        Connects to Qdrant or creates an in-memory instance and connects to it.

        Parameters:
            host (str): Hostname or IP address of the Qdrant server. Use "memory" for in-memory instance
                      or "path:<local_path>" for local storage.
            port (Optional[int]): Port number of the Qdrant server. Not used for in-memory or local storage.
            api_key (Optional[str]): API key for authentication with the Qdrant server.
            verify (str): Path to SSL certificate for secure connection.

        Returns:
            QdrantClient: The connected Qdrant client instance.

        Raises:
            Exception: If connection to Qdrant fails.
        """
        if not host:
            self.logger.error(f"{self.NAME}: No Qdrant host was specified!. To fix this either use a valid config file or provide the host in the ENV variables.")
            raise Exception(f"{self.NAME}: No Qdrant host specified! To fix this either use a valid config file or provide the host in the ENV variables.")

        self.logger.debug(f"{self.NAME}: Connecting to Qdrant host:{host}, port:{port}, key:{self.hide_key(api_key)} ...")
        try:
            if host == "memory":
                self.logger.debug(f"{self.NAME}: Client Qdrant memory instance")
                qdrant_client = QdrantClient(":memory:", api_key=api_key, force_disable_check_same_thread=False, on_disk_payload=True)
            elif "path:" in host:
                local = host.split("path:")[-1]
                self.logger.debug(f"{self.NAME}: Client Qdrant local storage: {local}")
                qdrant_client = QdrantClient(path=local, api_key=api_key, force_disable_check_same_thread=False, on_disk_payload=True)
            else:
                self.logger.debug(f"{self.NAME}: Client Qdrant remote instance: {host}:{port}")
                qdrant_client = QdrantClient(
                    url=host,
                    port=port,
                    api_key=api_key,
                    verify=verify,
                    force_disable_check_same_thread=False,
                    https=True,
                )

            self.check_fastembed_support(qdrant_client)
            qdrant_client.set_model(self.dense_embedding_model, options=self.embedding_options, offline=True)
            qdrant_client.set_sparse_model(self.sparse_embedding_model, options=self.embedding_options, offline=True)
            self.vectors_config = qdrant_client.get_fastembed_vector_params()
            self.sparse_vectors_config = qdrant_client.get_fastembed_sparse_vector_params()

            self.logger.info(f"{self.NAME}: Dense vectors_config: {self.vectors_config}, Sparse_vectors_config: {self.sparse_vectors_config}")

        except Exception as e:
            self.logger.error(f"{self.NAME}: Can't connect to Qdrant instance {host}:{port}!\n{e}, {traceback.format_exc()}")
            raise e
        return qdrant_client

    def create(
        self,
        collection_name: str,
        distance: models.Distance = models.Distance.COSINE,
        recreate: bool = False,
    ) -> models.CollectionInfo:
        """
        Creates a Qdrant collection using fastembed vector params.

        Parameters:
            collection_name (str): The name of the collection to create.
            distance (models.Distance, optional): The distance metric to use. Default is COSINE.
            recreate (bool, optional): If True, deletes the collection if it exists before creating it.
                                     Default is False.

        Returns:
            models.CollectionInfo: Information about the created or existing collection.
        """
        if recreate:
            if self.qdrant.collection_exists(collection_name=collection_name):
                self.qdrant.delete_collection(collection_name=collection_name)
        if not self.qdrant.collection_exists(collection_name=collection_name):
            self.logger.debug(f"{self.NAME}: Creating Qdrant collection: {collection_name} ...")
            self.qdrant.create_collection(
                collection_name=collection_name,
                vectors_config=self.vectors_config,
                sparse_vectors_config=self.sparse_vectors_config,
            )
        collection_info = self.qdrant.get_collection(collection_name=collection_name)
        collection_str = collection_info.__dict__
        self.logger.debug(f"{self.NAME}: Using Qdrant collection:\n{collection_str} ...")
        return collection_info

    def create_multi_vector(
        self,
        name: str,
        fields: List[str],
        distance: models.Distance = models.Distance.COSINE,
        recreate: bool = False,
    ) -> models.CollectionInfo:
        """
        Creates a multi-vector Qdrant collection using fastembed vector params.

        Parameters:
            name (str): The name of the collection to create.
            fields (List[str]): The list of field names for which to create vectors.
            distance (models.Distance, optional): The distance metric to use. Default is COSINE.
            recreate (bool, optional): If True, deletes the collection if it exists before creating it.
                                     Default is False.

        Returns:
            models.CollectionInfo: Information about the created or existing collection.
        """
        self.logger.debug(f"{self.NAME}: Creating multi-vector Qdrant collection: `{name}` with fields: {fields}, distance type: `{distance}` ...")
        fastembed_params = self.qdrant.get_fastembed_vector_params()
        vector_param = next(iter(fastembed_params.values()))
        vectors_config = {field: models.VectorParams(size=vector_param.size, distance=distance) for field in fields}
        return self.create(
            collection_name=name,
            vectors_config=vectors_config,
            sparse_vectors_config=self.sparse_vectors_config,
            recreate=recreate,
        )

    def search(
        self,
        query_vector: Union[List[float], Dict[str, List[float]]],
        collection_name: str,
        limit: int,
        query_filter: Optional[Dict[str, Any]] = None,
        vector_name: Optional[str] = None,
    ) -> List[Tuple[Dict[str, Any], float]]:
        """
        Searches for a vector in a Qdrant collection and returns results limited in number to 'limit'.

        Parameters:
            query_vector (Union[List[float], Dict[str, List[float]]]): The vector to search for,
                                                                      or a dictionary of named vectors.
            collection_name (str): The name of the collection to search in.
            limit (int): The maximum number of results to return.
            query_filter (Optional[Dict[str, Any]], optional): Filter to apply to the search. Default is None.
            vector_name (Optional[str], optional): The name of the vector if using named vectors. Default is None.

        Returns:
            List[Tuple[Dict[str, Any], float]]: A list of tuples containing the payload and score for each result.
        """
        if vector_name:
            query_vector = {vector_name: query_vector}
        else:
            query_vector = query_vector
        search_params = {
            "collection_name": collection_name,
            "query_vectors": query_vector,
            "query_filter": query_filter,
            "limit": limit,
        }
        hits = self.qdrant.query_points(**search_params)
        return [(hit.payload, hit.score) for hit in hits]

    def encode_search(
        self,
        text: str,
        collection_name: str,
        limit: int,
        query_filter: Optional[Dict[str, Any]] = None,
        vector_name: Optional[str] = None,
    ) -> List[Tuple[Dict[str, Any], float]]:
        """
        Encodes a string to a vector and performs a vector search in a Qdrant collection.

        Parameters:
            text (str): The string text to be encoded.
            collection_name (str): The name of the collection.
            limit (int): The maximum number of results to return.
            query_filter (Optional[Dict[str, Any]], optional): The filter to be applied to the search. Default is None.
            vector_name (Optional[str], optional): The name of the vector. Default is None.

        Returns:
            List[Tuple[Dict[str, Any], float]]: The search results as a list of tuples containing the payload and score.
        """
        query_vector = self.transformer.encode(text).tolist()
        return self.search(
            query_vector,
            collection_name,
            limit,
            query_filter=query_filter,
            vector_name=vector_name,
        )

    def upload(
        self,
        name: str,
        points: Union[Iterable[models.PointStruct], List[Dict[str, Any]]],
    ) -> None:
        """
        Uploads embeddings to a Qdrant collection.

        Parameters:
            name (str): The name of the collection.
            points (Union[Iterable[models.PointStruct], List[Dict[str, Any]]]): The points to be uploaded.

        Returns:
            None
        """
        self.logger.debug(f'{self.NAME}: Uploading points to Qdrant collection: "{name}" ...')
        self.qdrant.upload_points(collection_name=name, points=points)

    def fastembed_add(
        self,
        collection_name: str,
        docs: List[str],
        metadata: Optional[List[Dict[str, Any]]] = None,
        ids: Optional[List[str]] = None,
        parallel: Optional[int] = None,
    ) -> Dict[str, Any]:
        """
        Adds documents to a Qdrant collection and automatically creates fastembed embeddings.

        Parameters:
            collection_name (str): The name of the collection.
            docs (List[str]): The documents to be added.
            metadata (Optional[List[Dict[str, Any]]], optional): The metadata for the documents. Default is None.
            ids (Optional[List[str]], optional): The IDs for the documents. Default is None.
            parallel (Optional[int], optional): The number of parallel processes to use. Default is None.

        Returns:
            Dict[str, Any]: The response from the Qdrant client.

        Raises:
            Exception: If there is an error adding documents to the collection.
        """
        try:
            results = self.qdrant.add(
                collection_name=collection_name,
                documents=docs,
                metadata=metadata,
                ids=ids,
                parallel=parallel,
            )
            return results
        except Exception as e:
            self.logger.error(f"{self.NAME}: Error adding docs to Qdrant collection: {e}, {traceback.format_exc()}")
            raise e

    def fastembed_add_thread_safe(
        self,
        collection_name: str,
        docs: List[str],
        metadata: Optional[List[Dict[str, Any]]] = None,
        ids: Optional[List[str]] = None,
        parallel: Optional[int] = None,
    ) -> Dict[str, Any]:
        """
        Thread-safe version of fastembed_add method.

        This method acquires a lock before calling fastembed_add to ensure thread safety.

        Parameters:
            collection_name (str): The name of the collection.
            docs (List[str]): The documents to be added.
            metadata (Optional[List[Dict[str, Any]]], optional): The metadata for the documents. Default is None.
            ids (Optional[List[str]], optional): The IDs for the documents. Default is None.
            parallel (Optional[int], optional): The number of parallel processes to use. Default is None.

        Returns:
            Dict[str, Any]: The response from the Qdrant client.
        """
        # with self.lock:
        return self.fastembed_add(collection_name, docs, metadata, ids, parallel)

    def set_indexing(self, collection_name: str, indexing_threshold: int = 20000) -> None:
        """
        Sets the indexing threshold for a Qdrant collection.

        Parameters:
            collection_name (str): The name of the collection.
            indexing_threshold (int, optional): The indexing threshold. Default is 20000.

        Returns:
            None
        """
        self.qdrant.update_collection(
            collection_name=collection_name,
            optimizer_config=models.OptimizersConfigDiff(indexing_threshold=indexing_threshold),
        )

    def fastembed_batch_query(
        self,
        collection_name: str,
        lines: List[str],
        query_filter: Optional[Dict[str, Any]] = None,
        limit: int = 1,
    ) -> List[List[Dict[str, Any]]]:
        """
        Performs a batch query on a Qdrant collection with fastembed embeddings.

        Parameters:
            collection_name (str): The name of the collection.
            lines (List[str]): The lines to be queried.
            query_filter (Optional[Dict[str, Any]], optional): The filter to be applied to the query. Default is None.
            limit (int, optional): The maximum number of results to return for each query. Default is 1.

        Returns:
            List[List[Dict[str, Any]]]: The search results for each batch.

        Raises:
            Exception: If there is an error during batch querying.
        """
        try:
            for attempt in Retry(max_retries=1, retry_delay=0.5, logger=self.logger, exponential=True):
                with attempt:
                    search_results = self.qdrant.query_batch(
                        collection_name=collection_name,
                        query_texts=lines,
                        query_filter=query_filter,
                        limit=limit,
                    )
                    return search_results
        except Exception as e:
            self.logger.error(f"{self.NAME}: Error wile batch querying Qdrant collection: {e}, {traceback.format_exc()}")
            raise e

    def fastembed_query(
        self,
        collection_name: str,
        text: str,
        limit: int = 1,
        query_filter: Optional[Dict[str, Any]] = None,
    ) -> List[Dict[str, Any]]:
        """
        Queries a Qdrant collection with fastembed embeddings.

        Parameters:
            collection_name (str): The name of the collection.
            text (str): The text to be queried.
            limit (int, optional): The maximum number of results to return. Default is 1.
            query_filter (Optional[Dict[str, Any]], optional): The filter to be applied to the query. Default is None.

        Returns:
            List[Dict[str, Any]]: The search results for each vector name.

        Raises:
            Exception: If there is an error during querying.
        """
        try:
            for attempt in Retry(max_retries=1, retry_delay=0.5, logger=self.logger, exponential=True):
                with attempt:
                    search_results = []
                    search_result = self.qdrant.query(
                        collection_name=collection_name,
                        query_text=text,
                        query_filter=query_filter,
                        limit=limit,
                    )
                    if search_result:
                        search_results.append(search_result[0])
                    return search_results
        except Exception as e:
            self.logger.error(f"{self.NAME}: Error while querying Qdrant collection: {e}, {traceback.format_exc()}")
            raise e

    def fastembed_query_thread_safe(
        self,
        collection_name: str,
        text: str,
        limit: int = 1,
        query_filter: Optional[Dict[str, Any]] = None,
    ) -> List[Dict[str, Any]]:
        """
        Thread safe version of fastembed_query(), the plan B in case of collisions.

        This method acquires a lock before calling fastembed_query to ensure thread safety.

        Parameters:
            collection_name (str): The name of the collection.
            text (str): The text to be queried.
            limit (int, optional): The maximum number of results to return. Default is 1.
            query_filter (Optional[Dict[str, Any]], optional): The filter to be applied to the query. Default is None.

        Returns:
            List[Dict[str, Any]]: The search results.
        """
        with self.lock:
            return self.fastembed_query(collection_name, text, limit, query_filter)

    def scroll_thread_safe(self, **kwargs: Any) -> Tuple[List[Any], Optional[str]]:
        """
        Thread safe version of scroll(), the plan B in case of collisions.

        This method acquires a lock before calling the scroll method to ensure thread safety.

        Parameters:
            **kwargs: Arbitrary keyword arguments passed to the scroll method.

        Returns:
            Tuple[List[Any], Optional[str]]: A tuple containing the list of points and the next scroll position.
        """
        with self.lock:
            return self.qdrant.scroll(**kwargs)

    @staticmethod
    def gen_temp_collection_name(prefix: str = "temp_sess", postfix: str = "") -> str:
        """
        Generates a unique collection name using a prefix, the current date and time, and a random UUID.

        Parameters:
            prefix (str, optional): The prefix to use for the collection name.
            postfix (str, optional): The postfix to use for the collection name.

        Returns:
            str: The generated collection name.
        """
        date_time = datetime.now().strftime("%Y%m%d%H%M%S")
        return f"{prefix}{date_time}{uuid.uuid4().hex}{postfix}"

    def clean_temp_collections(self, days_old: int = 1, minutes_old: int = 0, prefix: str = "temp_sess") -> None:
        """
        Cleans up collections that are older than the specified number of days and minutes.

        Parameters:
            days_old (int, optional): The number of days to use as the threshold for cleaning up collections. Default is 1.
            minutes_old (int, optional): The number of minutes to use as the threshold for cleaning up collections. Default is 0.
            prefix (str, optional): A prefix for collections name. Default is "temp_sess".

        Returns:
            None
        """
        threshold_date = datetime.now() - timedelta(days=days_old, minutes=minutes_old)
        collections = self.qdrant.get_collections()

        for collection in collections:
            for c in list(collection[1]):
                if c.name.startswith(f"{prefix}"):
                    try:
                        date_str = c.name.split("__")[1]
                        collection_date = datetime.strptime(date_str, "%Y%m%d_%H%M%S")
                        if collection_date < threshold_date:
                            self.logger.debug(f"{self.NAME}: Deleting expired temp leftover collection: {c.name}")
                            self.qdrant.delete_collection(collection_name=c.name)
                    except Exception as e:
                        self.logger.error(f"{self.NAME}: Error processing collection {c.name}: {e}, {traceback.format_exc()}")

    def merge_collections(self, temp_collection: str, existing_collection: str, batch_size: int = 1000) -> None:
        """
        Merges the temp session collection into the existing collection and deletes the temp session collection.

        Parameters:
            temp_collection (str): The name of the temp session collection to be merged.
            existing_collection (str): The name of the existing collection to merge into.
            batch_size (int, optional): The number of points to process in each batch. Default is 1000.

        Returns:
            None
        """
        self.logger.debug(f"{self.NAME}: Starting merge_collections with temp_collection: {temp_collection}, existing_collection: {existing_collection}, batch_size: {batch_size}")
        scroll_position = None
        try:
            while True:
                self.logger.debug(f"{self.NAME}: .Scrolling through {temp_collection}...")
                response = self.qdrant.scroll(
                    collection_name=temp_collection,
                    scroll_filter=None,
                    limit=batch_size,
                    offset=scroll_position,
                    with_vectors=True,
                )
                points = response[0]  # Access the first element of the tuple
                scroll_position = response[1]  # Update the scroll position
                self.logger.debug(f"{self.NAME}: .Retrieved {len(points)} points from {temp_collection}")

                if not points:
                    break

                valid_points = [
                    self.models.PointStruct(
                        id=point.id,  # Use the existing ID for each point
                        vector=point.vector,  # Copy all vectors
                        payload=point.payload,
                    )
                    for point in points
                    if point.vector
                ]

                if valid_points:
                    self.logger.debug(f"{self.NAME}: .Upserting {len(valid_points)} points to {existing_collection} ...")
                    upsert_response = self.qdrant.upsert(collection_name=existing_collection, points=valid_points)
                    self.logger.debug(f".Upsert response: {upsert_response.status}")
                else:
                    self.logger.warning(".All points have None vectors, consider checking the vector generation process ...")

                # Ensure scroll_position is updated correctly
                if scroll_position is None:
                    # self.logger.debug("Scroll position is None, breaking the loop")
                    break

        except Exception as e:
            self.logger.error(f"{self.NAME}:  An error occurred during merging collections: {e}, {traceback.format_exc()}")
        self.logger.debug(f"Done merging {temp_collection} with {existing_collection}, all good.")

    def is_collection_empty(self, collection_name: str) -> bool:
        """
        Checks if a Qdrant collection is completely empty (without any points).

        Parameters:
            collection_name (str): The name of the collection to check.
        Returns:
            bool: True if the collection is empty, False otherwise.
        Raises:
            Exception: If there is an error checking the collection.
        """
        try:
            count = self.qdrant.count(collection_name=collection_name)
            return count == 0
        except Exception as e:
            self.logger.error(f"{self.NAME}: Error checking if collection '{collection_name}' is empty: {e}, {traceback.format_exc()}")
            raise e

    def get_collection_size(self, collection_name: str) -> int:
        """
        Gets the size of a Qdrant collection.

        Parameters:
            collection_name (str): The name of the collection to check.

        Returns:
            int: The size of the collection (number of points).

        Raises:
            Exception: If there is an error getting the collection size.
        """
        try:
            count_response = self.qdrant.count(collection_name=collection_name)
            return count_response.count
        except Exception as e:
            self.logger.error(f"{self.NAME}: Error getting the size of collection '{collection_name}': {e}, {traceback.format_exc()}")
            raise e
