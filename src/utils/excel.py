import csv
import json
import os
import sys

from openpyxl import load_workbook


class Xls2CSV2Json:

    @staticmethod
    def xls2csv(excel_path: str, csv_path: str) -> None:
        """Convert a single .xlsx Excel file to CSV."""
        wb = load_workbook(excel_path, read_only=True)
        ws = wb.active
        with open(csv_path, "w", newline="", encoding="utf-8") as f:
            writer = csv.writer(f)
            for row in ws.iter_rows(values_only=True):
                writer.writerow(row)
        wb.close()

    def batch_convert_xls2csv(self, folder_path: str, output_folder: str) -> None:
        """Convert all .xlsx Excel files in a folder to CSV files in the output folder."""
        try:
            os.makedirs(output_folder, exist_ok=True)
            print(f"Folder path {folder_path} ...")
            # files = os.listdir(folder_path)
            # print(f"Files {files} ...")
            for filename in os.listdir(folder_path):
                if filename.lower().endswith(".xlsx"):
                    excel_path = os.path.join(folder_path, filename)
                    csv_filename = os.path.splitext(filename)[0] + ".csv"
                    csv_path = os.path.join(output_folder, csv_filename)
                    print(f"Converting (xls2csv) {excel_path} to {csv_path}")
                    self.xls2csv(excel_path, csv_path)
        except Exception as e:
            print(f"Error during batch conversion: {e}")
            import traceback

            traceback.print_exc()

    @staticmethod
    def load_csv(fp_in, delimiter=",", quotechar='"', remove_empty=True, custom_headers=None, **kwargs):
        r = csv.DictReader(fp_in, delimiter=delimiter, quotechar=quotechar, fieldnames=custom_headers)

        rows = [row_dct for row_dct in r]
        # print(f"rows: {rows}")

        if remove_empty:
            dicts = []
            for row in rows:
                d = {}
                for k, v in row.items():
                    v = str(v).strip()
                    k = str(k).strip()
                    # key = k if k else "*"
                    # value = i if i else "*"
                    if v and k:
                        d[k] = v
                if d:
                    dicts.append(d)
            rows = [d for d in dicts if d]
        return rows

    @staticmethod
    def save_json(data, fp_out, pretty_spaces=0, sort_keys=False, columns=None, encoding="utf-8", **kwargs):
        if columns:
            # Only include rows that have all specified columns
            filtered_data = []
            for row in data:
                if all(col in row and row[col] != "" for col in columns):
                    filtered_data.append({k: v for k, v in row.items() if k in columns})
            data = filtered_data
            # jsons = [json.dumps(item, separators=(',', ':'), sort_keys=sort_keys) for item in data]
            for item in data:
                json.dump(item, fp_out, indent=None, separators=(",", ":"), sort_keys=sort_keys)
                fp_out.write("\n")

    def csv2json(self, csv_file, json_file, encoding="utf-8", columns=None, **kwargs):
        csv_file_local, json_local = None, None
        try:
            if csv_file == "-" or csv_file is None:
                csv_file = sys.stdin
            elif isinstance(csv_file, str):
                csv_file = csv_local = open(csv_file, "r", encoding=encoding)

            if json_file == "-" or json_file is None:
                json_file = sys.stdout
            elif isinstance(json_file, str):
                json_file = json_local = open(json_file, "w", encoding=encoding)

            data = self.load_csv(csv_file, **kwargs)
            self.save_json(data, json_file, columns=columns, encoding=encoding, **kwargs)
        finally:
            if csv_local is not None:
                csv_local.close()
            if json_local is not None:
                json_local.close()

    def batch_convert_csv2json(self, folder_path: str, output_folder: str) -> None:
        """Convert all .csv files in a folder to JSON files in the output folder."""
        try:
            os.makedirs(output_folder, exist_ok=True)
            for filename in os.listdir(folder_path):
                if filename.lower().endswith(".csv"):
                    csv_path = os.path.join(folder_path, filename)
                    csv_filename = os.path.splitext(filename)[0] + ".json"
                    json_path = os.path.join(output_folder, csv_filename)
                    print(f"Converting (csv2json) {csv_path} to {json_path}")
                    self.csv2json(csv_path, json_path)
        except Exception as e:
            print(f"Error during batch conversion: {e}")
            import traceback

            traceback.print_exc()


if __name__ == "__main__":
    converter = Xls2CSV2Json()
    print("Starting conversion...")
    # Example usage:
    converter.xls2csv(
        r"C:\dev_rnd\log-analyzer\src\ebike-CAN-logs-error-description\InformationManagerIssues_h.xlsx",
        r"C:\dev_rnd\log-analyzer\src\ebike-CAN-logs-error-description\InformationManagerIssues.csv",
    )
    converter.csv2json(
        r"C:\dev_rnd\log-analyzer\src\ebike-CAN-logs-error-description\InformationManagerIssues.csv",
        r"C:\dev_rnd\log-analyzer\src\ebike-CAN-logs-error-description\InformationManagerIssues.json",
        columns=["Issue ID", "Developer Text"],
    )
    # converter.batch_convert_xls2csv(r"C:\Users\<USER>\Desktop\QA Doc Analyzer", r"C:\Users\<USER>\Desktop\QA Doc Analyzer\csv")
    # converter.batch_convert_csv2json(r"C:\Users\<USER>\Desktop\QA Doc Analyzer\csv", r"C:\Users\<USER>\Desktop\QA Doc Analyzer\json")
