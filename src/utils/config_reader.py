import configparser
import logging
import os
import uuid
from pathlib import Path

from utils.cfg_types import convert_self_type
from utils.logger import Logger


class ConfigReader:
    """
    A class used to read configuration files.
    Providing the environment variables will override the values specified in the config file.
    """
    NAME = "ConfigReader"
    LOG_LEVEL = logging.INFO

    def __init__(self, config_file, logger=None):
        """
        Constructs all the necessary attributes for the ConfigReader object.

        Args:
            config_file (str): The path to the configuration file.
            logger (logging.Logger, optional): Logger for logging warnings. Defaults to None.
        """
        self.logger = logger if logger else Logger(name=f'ConfigReader-{uuid.uuid4().hex}', logfile='ConfigReader.log', level=self.LOG_LEVEL)
        self.config_file = None
        self.config = configparser.ConfigParser()
        if not config_file or not Path(config_file).exists():
            logger.critical(
                f"{self.NAME}: Configuration file `{config_file}` not found! Cannot proceed without the configuration file, please ensure that the configuration file exists.")
            raise FileNotFoundError(f"{self.NAME}: Configuration file `{config_file}` not found!")
        else:
            self.config.read(config_file)
        # Override the config values with environment variables if those exist
        self.override_with_env()

    def override_with_env(self, debug_env=True):
        if not self.config:
            return
        for section in self.config.sections():
            for key in self.config[section]:
                env_var_name = f"{section.upper()}_{key.upper()}"
                env_var = os.getenv(env_var_name)
                if env_var:
                    if debug_env:
                        self.logger.debug(f"{self.NAME}: Found env var: '{env_var_name}' corresponding to config: [{section}][{key}], overriding...")
                    self.config.set(section, key, env_var)

    def get(self, section, option, default=None):
        """
        Retrieves the value of the specified option in the specified section as string
        and tries to convert it to its type.

        Args:
            section (str): The section in the configuration file.
            option (str): The option in the section.
            default (str, optional): The default value if the option is not found. Defaults to None.

        Returns:
            str/int/float/bool: The value of the option.
        """
        if section in self.config and option in self.config[section]:
            config_value = self.config[section].get(option)
            if config_value:
                return convert_self_type(config_value)
        return default

    def set(self, section, option, value):
        """
        Sets the value of the specified option in the specified section.

        Args:
            section (str): The section in the configuration file.
            option (str): The option in the section.
            value (str): The value to set.
        """
        if section not in self.config:
            self.config[section] = {}
        self.config[section][option] = value
        self.logger.debug(f"{self.NAME}: Config [{section}][{option}], set to `{value}` ...")
