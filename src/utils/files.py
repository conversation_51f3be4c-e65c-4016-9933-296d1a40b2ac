import gzip
import json
import uuid
from pathlib import Path
from typing import Optional, Any, List, Generator, Union

from utils.logger import Logger


class Files:
    """
    File helper with handy methods for file operations and error checking.
    """

    NAME = "Files"
    LOG_LEVEL = Logger.INFO

    def __init__(self, logger: Optional[Logger] = None) -> None:
        """
        Initialize Files helper.

        Args:
            logger (Logger, optional): Logger instance.
        """
        self.logger = (
            logger
            if logger
            else Logger(
                name=f"FilesLogger-{uuid.uuid4().hex}",
                logfile="Files.log",
                level=self.LOG_LEVEL,
            )
        )

    @staticmethod
    def str_gzip(string: str) -> bytes:
        """
        Compress a string using GZIP.

        Args:
            string (str): String to compress.
        Returns:
            bytes: Compressed bytes.
        """
        return gzip.compress(str(string).encode("utf-8"))

    @staticmethod
    def str_gunzip(gz_bytes: bytes) -> str:
        """
        Decompress GZIP bytes to string.

        Args:
            gz_bytes (bytes): Bytes to decompress.
        Returns:
            str: Decompressed string.
        """
        return gzip.decompress(gz_bytes).decode("utf-8")

    def get_gzipped(self, file: str) -> bytes:
        """
        Get file content as gzipped bytes.

        Args:
            file (str): File path.
        Returns:
            bytes: Gzipped content.
        """
        self.logger.debug(f'{self.NAME}: Loading file "{file}" and returning gzip compressed result ...')
        try:
            with open(file, "r", encoding="utf-8") as f:
                return self.str_gzip(f.read())
        except (OSError, FileNotFoundError) as e:
            self.logger.error(f'{self.NAME}: Failed to open, read and encode file: "{file}"')
            raise e

    def extract_gz_file(self, gz_file_path: str, output_folder: str) -> None:
        """
        Extract gzipped file to output folder.

        Args:
            gz_file_path (str): Gzipped file path.
            output_folder (str): Output folder path.
        """
        try:
            output_folder = Path(output_folder)
            output_folder.mkdir(parents=True, exist_ok=True)
            output_file = Path(output_folder) / Path(gz_file_path).stem
            with gzip.open(gz_file_path, "rb") as f:
                content = f.read()
                with output_file.open("wb") as output:
                    output.write(content)
        except (OSError, FileNotFoundError) as e:
            self.logger.error(f'{self.NAME}: Failed to open, read and extract gzip file: "{gz_file_path}"')
            raise e

    def get_lines(
        self,
        file: str,
        as_list: bool = True,
        limit: Optional[int] = None,
        ignore_errors: bool = True,
    ) -> Union[List[str], str]:
        """
        Get lines from a file as list or string.

        Args:
            file (str): File path.
            as_list (bool, optional): Return as list if True, else as string.
            limit (int, optional): Number of lines from end.
            ignore_errors (bool, optional): Ignore encoding errors if True.
        Returns:
            list or str: File lines.
        """
        try:
            errors_handle = "replace" if ignore_errors else "strict"
            self.logger.debug(f'{self.NAME}: Loading file: "{file}" ...')
            with open(file, "r", encoding="utf-8", errors=errors_handle) as f:
                lines = f.readlines()
                self.logger.debug(f'{self.NAME}: Read "{len(lines)}" lines from  "{file}" ...')
                if limit:
                    lines = lines[-limit:]
                if as_list:
                    return [line.encode("utf-8").decode("utf-8", errors=errors_handle) for line in lines]
                return "".join([line.encode("utf-8").decode("utf-8", errors=errors_handle) for line in lines])
        except (OSError, FileNotFoundError) as e:
            self.logger.error(f'{self.NAME}: Failed to open and read file: "{file}"')
            raise e

    def get_lines_iter(self, file: str, ignore_errors: bool = True) -> Generator[str, None, None]:
        """
        Yield lines from a file one by one.

        Args:
            file (str): File path.
            ignore_errors (bool, optional): Ignore encoding errors if True.
        Yields:
            str: File line.
        """
        try:
            errors_handle = "replace" if ignore_errors else "strict"
            self.logger.debug(f'{self.NAME}: Loading file (iter): "{file}" ...')
            with open(file, "r", encoding="utf-8", errors=errors_handle) as f:
                for line in f:
                    yield line.encode("utf-8").decode("utf-8", errors=errors_handle)
        except (OSError, FileNotFoundError) as e:
            self.logger.error(f'{self.NAME}: Failed to open and read file: "{file}"')
            raise e

    def count_lines_fast(self, file: str) -> int:
        """
        Fast line counting without loading file into memory.

        Uses binary mode for maximum performance - much faster than text mode
        for large files since it doesn't need to decode UTF-8.

        Args:
            file (str): File path to count lines in.

        Returns:
            int: Number of lines in the file.
        """
        try:
            self.logger.debug(f'{self.NAME}: Counting lines in "{file}" ...')
            with open(file, 'rb') as f:
                count = sum(1 for _ in f)
            self.logger.debug(f'{self.NAME}: Found {count} lines in "{file}"')
            return count
        except (OSError, FileNotFoundError) as e:
            self.logger.error(f'{self.NAME}: Failed to count lines in file: "{file}"')
            raise e

    def get_json(self, file: str) -> Any:
        """
        Load a JSON file as dict.

        Args:
            file (str): JSON file path.
        Returns:
            dict: JSON content.
        """
        self.logger.debug(f'{self.NAME}: Loading JSON file: "{file}" ...')
        try:
            with open(file, "r", encoding="utf-8") as f:
                try:
                    return json.load(f)
                except json.JSONDecodeError as e:
                    raise ValueError(f'"{file}" is not a valid JSON: {e}')
        except OSError as e:
            self.logger.error(f'{self.NAME}: Failed to open and read JSON file: "{file}"')
            raise e

    def write_file(self, content: Any, file: str, file_mode: str = "w", silent: bool = False) -> None:
        """
        Write content to a file.

        Args:
            content (str or list): Content to write.
            file (str): File path.
            file_mode (str, optional): File mode.
            silent (bool, optional): Suppress logging if True.
        """
        if not silent:
            self.logger.debug(f'{self.NAME}: Writing content to file: "{file}" ...')
        try:
            with open(file, file_mode, encoding="utf-8") as f:
                if isinstance(content, list) and all(isinstance(item, str) for item in content):
                    f.writelines(content)
                else:
                    f.write(f"{content}")
        except OSError as e:
            self.logger.error(f'{self.NAME}: Failed to write to file: "{file}"')
            raise e

    @staticmethod
    def recreate_file(file_path: str) -> None:
        """
        Create or clear a file.

        Args:
            file_path (str): File path.
        """
        with open(file_path, "w", encoding="utf-8") as _:
            pass
