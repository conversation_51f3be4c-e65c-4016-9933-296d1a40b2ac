import copy
import json
import logging
import random
import re
import string
import traceback
import uuid
from pathlib import Path
from typing import Optional, Any, List, Dict, Generator, Union

import Stemmer

from utils.files import Files
from utils.logger import Logger


class Logs:
    """
    Utility for log line filtering, normalization, and ground-truth handling.
    """

    NAME = "LogsUtil"
    UTC_TIMESTAMP_PATTERN = re.compile(r"(.*\d\d\d\d-\d\d-\d\d)T(\d\d:\d\d:\d\d.*)")
    TIMESTAMP_RE = re.compile(r"^\[?\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d+Z]?\s*")
    GT_IDENTIFIER = "groundtruth"
    JENKINS_LINE_PRE = "[8mha:"
    JENKINS_LINE_POST = "[0m"
    EMPTY_LINE = ""
    # Precompiled regular expressions for better performance
    NON_ALPHANUMERIC_RE = re.compile(r"[^a-zA-Z0-9]")
    NUMBERS_RE = re.compile(r"\d+")
    SPACES_RE = re.compile(r"\s+")
    LOG_LEVEL = logging.INFO

    def __init__(self, logger: Optional[Logger] = None) -> None:
        """
        Initialize Logs helper.

        Args:
            logger (Logger, optional): Logger instance.
        """
        self.id = uuid.uuid4()
        self.logger = (
            logger
            if logger
            else Logger(
                name=f"LogsLogger-{uuid.uuid4().hex}",
                logfile="LogsLogger.log",
                level=self.LOG_LEVEL,
            )
        )
        self.files = Files(logger)
        self.stemmer = Stemmer.Stemmer("english")

    def is_zulu_timestamp(self, text: str) -> bool:
        """
        Check if string is UTC (Zulu) timestamp.

        Args:
            text (str): String to check.
        Returns:
            bool: True if UTC timestamp.
        """
        return bool(self.UTC_TIMESTAMP_PATTERN.search(text))

    def filter_time_stamp(self, line: str) -> str:
        """
        Strip Zulu-style timestamp from line.

        Args:
            line (str): Log line.
        Returns:
            str: Line without timestamp.
        """
        return self.TIMESTAMP_RE.sub("", line).strip()

    def filter_timestamps(self, log_file_lines: List[str]) -> List[str]:
        """
        Remove timestamps and empty lines from log lines.

        Args:
            log_file_lines (list): Log lines.
        Returns:
            list: Filtered log lines.
        """
        return [self.filter_time_stamp(line).strip() or self.EMPTY_LINE for line in log_file_lines]

    def filter_jenkins_trash(self, line: str, min_line_len: int = 3) -> str:
        """
        Remove Jenkins console lines from log line.

        Args:
            line (str): Log line.
            min_line_len (int, optional): Minimum valid line length.
        Returns:
            str: Cleaned line or empty string.
        """
        if len(line.strip()) < min_line_len:
            return self.EMPTY_LINE
        if self.JENKINS_LINE_PRE in line and self.JENKINS_LINE_POST in line:
            return self.EMPTY_LINE  # Filter jenkins trash lines which are not actual log lines but jenkins internal console display instructions
        return line

    def filter_trash_lines(self, lines: List[str], stem: bool = False) -> Generator[str, None, None]:
        """
        Remove Jenkins/color info from lines, optionally stem.

        Args:
            lines (list): Log lines.
            stem (bool, optional): Stem lines if True.
        Yields:
            str: Filtered line.
        """
        for line in lines:
            if stem:
                yield self.stem_log_line(self.filter_jenkins_trash(line))
            else:
                yield self.filter_jenkins_trash(line)

    def filter_trash_line(self, line: str, stem: bool = False) -> str:
        """
        Remove Jenkins/color info from a line, optionally stem.

        Args:
            line (str): Log line.
            stem (bool, optional): Stem line if True.
        Returns:
            str: Filtered line.
        """
        if stem:
            return self.stem_log_line(self.filter_jenkins_trash(line))
        else:
            return self.filter_jenkins_trash(line)

    def stem_log_line(self, log_line: str, trim: int = 256) -> str:
        """
        Stem and normalize a log line for anomaly detection.

        Args:
            log_line (str): Log line.
            trim (int, optional): Max length to consider.
        Returns:
            str: Stemmed log line.
        """
        if not len(log_line):
            return ""
        if len(log_line) > trim:
            log_line = log_line[:trim]
        log_line = self.filter_time_stamp(log_line)
        line = log_line.encode("ascii", "ignore").decode("ascii").lower()
        # Replace all non-alphanumeric characters with spaces
        line = self.NON_ALPHANUMERIC_RE.sub(" ", line)
        # Remove all numbers
        line = self.NUMBERS_RE.sub(" ", line)
        # Normalize all spaces to a single space
        line = self.SPACES_RE.sub(" ", line).strip()
        # Stem the words
        stem = " ".join(self.stemmer.stemWords(line.split()))
        return stem.strip()

    @staticmethod
    def format_result(
        errors: List[Any],
        hints: List[Any],
        success: List[Any],
        file: str,
        exit_code: Optional[int] = None,
    ) -> Dict[str, Any]:
        """
        Format result in ground-truth compatible JSON.

        Args:
            errors (list): Error lines.
            hints (list): Hint lines.
            success (list): Success lines.
            file (str): File name.
            exit_code (int, optional): Exit code.
        Returns:
            dict: Formatted result.
        """

        def _sort_lines(lines):
            if lines and isinstance(lines[0], dict) and "line" in lines[0]:
                return sorted(lines, key=lambda d: d["line"])
            return sorted(lines)

        return {
            "file": f"{Path(file).name}",
            "exit_code": f"{exit_code}",
            "root_causes": [{"error_lines": _sort_lines(errors), "hint_lines": _sort_lines(hints)}],
            "success_lines": _sort_lines(success),
        }

    def get_pair(self, entry: Path):
        """
        Gets a pair of ground-truth and log files in a path.

        Args:
            entry (Path): The path entry to be checked.

        Returns:
            list of Path: The pair of ground-truth and log files, or None if not found.

        Raises:
            OSError: If the file cannot be accessed.
        """
        try:
            if entry.is_file():
                if self.GT_IDENTIFIER in entry.name:
                    log = Path(str(entry.absolute()).split(".groundtruth")[0])
                    if log.exists() and log.is_file():
                        self.logger.debug(f"{self.NAME}: Pair found: <ground-truth-file:{entry.absolute()} -- log-file:{log.absolute()}>")
                        return [entry, log]
        except OSError as e:
            self.logger.error(f"{e}, {traceback.format_exc()}")
            raise e

    def get_pairs(self, path: str):
        """
        Gets all pairs of ground-truth and log files in a path.

        Args:
            path (str): The path to be searched.

        Returns:
            list of Path: The pairs of ground-truth and log files.
        """
        pairs = []
        for entry in Path(path).rglob("*"):
            pair = self.get_pair(entry)
            if pair:
                pairs.append(pair)
        self.logger.debug(f"{self.NAME}: Number of <ground-truth-file -- log-file> pairs found: {len(pairs)}, instance: {self.id}")
        return pairs

    @staticmethod
    def get_dict(gt: Union[str, dict]):
        """
        Get a dict from a JSON string or dict.

        Args:
            gt (str or dict): The JSON string or dict.

        Returns:
            dict: The parsed dict.
        """
        if not isinstance(gt, dict):
            return json.loads(gt)
        return gt

    def is_successful(self, gt: Union[str, dict]):
        """
        Check if the ground-truth compatible dict is successful.

        Args:
            gt (dict or str): The ground-truth dict or JSON string.

        Returns:
            bool: True if successful, False otherwise.
        """
        gt = self.get_dict(gt)
        if "success_lines" in gt:
            if gt["success_lines"]:
                return True
        return False

    def get_log_lines(self, positions_list, log, position_adjust=1):
        """
        Get the log lines strings from a log line list for the numerical positions in list.

        Args:
            positions_list (list): The list of positions or ranges.
            log (list): The log lines.
            position_adjust (int, optional): The position adjustment. Default is 1.

        Returns:
            dict: The log lines at the specified positions.
        """
        lines = {}
        positions_list = self.expand_range(positions_list)
        for position in positions_list:
            lines[position] = self.filter_time_stamp(log[position - position_adjust])
        return lines

    def insert_log_lines(self, gt, log):
        """
        Insert the log lines strings (from a log lines list) into a ground-truth compatible dict.

        Args:
            gt (dict or str): The ground-truth dict or JSON string.
            log (list of str): The log lines.

        Returns:
            dict: The updated ground-truth dict.
        """
        gt = self.get_dict(gt)
        if self.is_successful(gt):
            gt["success_lines"] = self.get_log_lines(gt["success_lines"], [lin for lin in log])
        root_causes = []
        if "root_causes" in gt:
            for root_cause in gt["root_causes"]:
                if root_cause["error_lines"]:
                    root_cause["error_lines"] = self.get_log_lines(root_cause["error_lines"], log)
                if root_cause["hint_lines"]:
                    root_cause["hint_lines"] = self.get_log_lines(root_cause["hint_lines"], log)
                root_causes.append(root_cause)
            gt["root_causes"] = root_causes
        return gt

    def is_valid_gt(self, gt):
        """
        Check if the ground-truth is valid.

        Args:
            gt (dict or str): The ground-truth dict or JSON string.

        Returns:
            bool: True if valid, False otherwise.
        """
        gt = self.get_dict(gt)
        return "exit_code" in gt

    def scramble(self, log_lines, mono=False, mono_char="█"):
        """
        Scrambles the characters in log lines with random characters or a mono char.

        Args:
            log_lines (list of str): The log lines to be scrambled.
            mono (bool, optional): If True, use a mono char. Default is False.
            mono_char (str, optional): The mono char to be used. Default is '█'.

        Returns:
            list of str: The scrambled log lines.
        """
        new_lines = []
        for line in log_lines:
            timestamp = ""
            if self.is_zulu_timestamp(line):
                timestamp = line[0:27]
                line = line[27:]
            result = ""
            for char in line:
                if char in [" ", "\t", "\n"]:
                    result += char
                else:
                    if mono:
                        result += mono_char
                    else:
                        if char in string.digits:
                            result += random.choice(string.digits)
                        elif char in string.punctuation:
                            result += random.choice(string.punctuation)
                        else:
                            ch = random.choice(string.ascii_letters)
                            if char.isupper():
                                result += ch.upper()
                            else:
                                result += ch.lower()
            new_lines.append(timestamp + result)
        return new_lines

    @staticmethod
    def expand_range(range_list):
        """
        Expands a list of ranges into a list of integers.

        Args:
            range_list (list of str or int): The list of ranges or integers.

        Returns:
            list of int: The expanded list of integers.
        """
        temp_list = []
        for element in range_list:
            if "-" not in str(element):
                temp_list.append(int(element))
                continue
            [first, last] = element.split("-")
            for nbr in range(int(first), int(last) + 1):
                temp_list.append(nbr)
        return temp_list

    @staticmethod
    def add_concat_fields(data_dict):
        """
        Adds concatenated fields for hints and errors within the root causes.

        Args:
            data_dict (dict): Dictionary containing root cause data. Expected fields
                include "hint_lines" and "error_lines".

        Returns:
            list[dict]: List of root cause dictionaries with concatenated
            'hints_match' and 'errors_match' fields.
        """
        root_causes = []
        for root_cause in data_dict["root_causes"]:
            root_cause["hints_match"] = "".join(root_cause["hint_lines"].values()) if root_cause["hint_lines"] else "*"
            root_cause["errors_match"] = "".join(root_cause["error_lines"].values()) if root_cause["error_lines"] else "*"
            root_causes.append(root_cause)
        return root_causes

    @staticmethod
    def clean_dict_fields(fields, dictionary):
        """
        Cleans specified fields from a dictionary.

        Args:
            fields (list): The list of fields to clean.
            dictionary (dict): The dictionary to clean.

        Returns:
            dict: The cleaned dictionary.
        """
        dd = copy.deepcopy(dictionary)
        for k in fields:
            dd.pop(k, None)
        if "root_causes" in dd:
            for root_cause in dd["root_causes"]:
                for k in fields:
                    root_cause.pop(k, None)
        return dd

    def is_dict_fully_empty(self, d):
        """
        Checks if a dictionary or list is fully empty.

        Args:
            d (dict or list): The dictionary or list to check.

        Returns:
            bool: True if the dictionary or list is fully empty, False otherwise.
        """
        if isinstance(d, dict):
            return all(self.is_dict_fully_empty(value) for value in d.values() if isinstance(value, (dict, list))) and not any(d.values())
        elif isinstance(d, list):
            return all(self.is_dict_fully_empty(item) for item in d)
        return not d

    @staticmethod
    def get_pretty_dict(dct):
        """
        Returns a dictionary as a JSON string in a pretty format, useful for printing and debugging.

        Args:
            dct (dict): The dictionary to print.
        """
        return json.dumps(dct, indent=3, sort_keys=True)

    def get_analysis_lines(self, log_file, analysis=None):
        """
        Retrieves log lines and inserts them into an analysis.

        Args:
            log_file (str): Path to the log file to be analyzed.
            analysis (dict, optional): Dictionary containing existing analysis data.
            If not provided, creates a new one.

        Returns:
            dict: The updated or newly created analysis dictionary with log lines inserted.
        """
        self.logger.debug(f"{self.NAME}: Getting log lines from '{log_file}' ...")
        log = self.filter_timestamps(self.filter_trash_lines(self.files.get_lines(log_file)))
        analysis_with_logs = self.insert_log_lines(analysis, log)
        analysis_with_logs["root_causes"] = self.add_concat_fields(analysis_with_logs)
        return analysis_with_logs

    def get_analysis(self, log_file, analysis_file):
        """
        Gets the analysis from a file and inserts log lines.

        Args:
            log_file (str): The path to the log file.
            analysis_file (str): The path to the analysis file.

        Returns:
            dict: The analysis dictionary with log lines inserted.
        """
        self.logger.debug(f"Getting analysis from '{analysis_file}' ...")
        analysis = self.files.get_json(analysis_file)
        return self.get_analysis_lines(log_file, analysis)

    def normalize_line_length(self, analysis_with_lines, max_char_length=256):
        """
        Normalizes the line length in the analysis.

        Args:
            analysis_with_lines (dict): The analysis dictionary with log lines.
            max_char_length (int, optional): The maximum number of characters allowed. Defaults to 256.

        Returns:
            dict: The normalized analysis dictionary.
        """
        self.logger.debug(f"{self.NAME}: Normalizing line lengths to max {max_char_length} chars to prevent prompter overflow...")
        for root_cause in analysis_with_lines["root_causes"]:
            for k, v in root_cause.items():
                if isinstance(v, dict):
                    for kk, vv in v.items():
                        if len(vv) > max_char_length:
                            normalized = vv[:max_char_length]
                            # self.logger.debug(f"-- Line {kk} had a length of {len(vv)} chars and was normalized to {len(normalized)} chars")
                            root_cause[k][kk] = normalized
                elif isinstance(v, list):
                    for i, vv in enumerate(v):
                        if len(vv) > max_char_length:
                            # print(f"{vv}")
                            root_cause[k][i] = vv[:max_char_length]
        return analysis_with_lines


if __name__ == "__main__":
    logs = Logs()
    print(logs.is_zulu_timestamp("2023-10-01T12:00:00.000Z"))
    print(logs.filter_time_stamp("[2023-10-01T12:00:00.000Z] This is a log line."))
    print(logs.stem_log_line("[2023-10-01T12:00:00.000Z] This is a test log line with numbers 12345 and punctuation!"))
