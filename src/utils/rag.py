import logging
import sys
import traceback
import uuid
from pathlib import Path
from subprocess import Popen, PIPE

from qdrant_client import models
# from sentence_transformers import SentenceTransformer

from utils.config_reader import ConfigReader
from utils.default_config import DEFAULT_CONFIG_FILE
from utils.files import Files
from utils.logger import Logger
from utils.logs import Logs
from utils.qdrant_util import QdrantUtil


class RetrivalAugmentedGeneration:
    """
    Helper for Sentence transformers, references:
    https://www.sbert.net/docs/pretrained_models.html
    https://huggingface.co/sentence-transformers/all-MiniLM-L6-v2
    """

    NAME = "Rag"

    PATH = Path(sys.executable)
    ROOT_PATH = Path(PATH.drive) / "/" if PATH.root else PATH.root  # Linux friendly ...
    STM_MODELS = ROOT_PATH / "sentence_transformers"
    # GIT_FOLDER = STM_MODELS / ".git"
    ST_MODEL_PATH = STM_MODELS / "all-MiniLM-L6-v2"
    HUGGING_REPO = "https://huggingface.co/sentence-transformers"
    LOG_LEVEL = logging.INFO

    STM = {"local": str(ROOT_PATH / "sentence_transformers/all-MiniLM-L6-v2"), "remote": "all-MiniLM-L6-v2"}

    def __init__(self, logger, ini_config=DEFAULT_CONFIG_FILE, cfg=None):
        """
        Initializes the Stm helper with specified parameters.

        Parameters:
            logger (Logger): An instance of a Logger.
            ini_config (str): The path to the configuration file.
        """
        self.logger = logger if logger else Logger(name=f"RetrivalAugmentedGeneration-{uuid.uuid4().hex}", logfile="RetrivalAugmentedGeneration.log", level=self.LOG_LEVEL)
        self.files = Files(self.logger)
        self.cfg = cfg if cfg else ConfigReader(ini_config, self.logger)
        self.lg = Logs(self.logger)
        self.stm = None
        self.qh = None
        self.model = None

    def init_rag(self):
        """
        Initializes and configures RAG (Retrieval-Augmented Generation) components.

        This method sets up the transformer, Qdrant client, and connects them using
        configurations provided in the instance's settings.
        """

        # self.model = SentenceTransformer("sentence-transformers/all-MiniLM-L6-v2")

        self.qh = QdrantUtil(
            self.logger, self.cfg.get("PROMPT_GENERATOR", "host"), self.cfg.get("PROMPT_GENERATOR", "port"), self.cfg.get("PROMPT_GENERATOR", "api_key"), transformer=self.model
        )
        self.qh.model = self.model

    def init_local_stm(self, stm_to_use=None):
        """
        Extracts and loads a local Sentence Transformer model.

        Parameters:
            stm_to_use (str, optional): The path to the Sentence Transformer model to use. Default is None.
        """
        self.gitclone_sentence_transformer(self.ST_MODEL_PATH, self.STM["remote"])
        self.stm = stm_to_use
        self.model = self.load_transformer(stm_to_use)

    def check_archives(self, st_model_archives):
        """
        Checks if the specified model archives exist.

        Parameters:
            st_model_archives (list of Path): The list of model archive paths to check.

        Returns:
            bool: True if all archives exist, False otherwise.
        """
        for archive in st_model_archives:
            if not archive.exists() or not archive.is_file():
                self.logger.debug(f"{self.NAME}: Local {archive} not found ...")
                return False
        return True

    def gitclone_sentence_transformer(self, st_model_path, stm_model):
        """
        Clones the Sentence Transformer model from huggingface.co.

        Parameters:
            st_model_path (Path): The path where the model should be cloned.
            stm_model (str): The name of the Sentence Transformer model to clone.
        """
        if not st_model_path.exists() or not st_model_path.is_dir():
            self.logger.debug(f"{self.NAME}: Local {st_model_path} not found, trying to get model from huggingface.co ...")
            self.logger.debug(f"{self.NAME}: Default model repo is about about 700MB, it may take some minutes, please be patient ...")
            try:
                git_command = ["git", "lfs", "clone", f"{self.HUGGING_REPO}/{stm_model}", f"{st_model_path}"]
                self.logger.debug(f'{" ".join(git_command)}')
                with Popen(git_command, stdout=PIPE, bufsize=1, universal_newlines=True) as output:
                    for line in output.stdout:
                        self.logger.debug(line.strip())
                    try:
                        output.wait()
                    except PermissionError as e:
                        self.logger.error(f"{e}, {traceback.format_exc()}")
                        raise e
                    self.logger.debug(f"Git Done.")
            except Exception as e:
                self.logger.error(f"{e}, {traceback.format_exc()}")
                raise e
        else:
            self.logger.debug(f"Local {st_model_path} found ...")

    def load_transformer(self, stm_to_use):
        """
        Loads/initializes the Sentence Transformer model.

        Parameters:
            stm_to_use (str): The path to the Sentence Transformer model to use.

        Returns:
            SentenceTransformer: The loaded Sentence Transformer model.
        """
        self.logger.debug(f"Loading Sentence Transformer Model: {stm_to_use}...")
        try:
            # return SentenceTransformer(str(stm_to_use))
            return stm_to_use
        except Exception as e:
            self.logger.error(f"{e}, {traceback.format_exc()}")
            raise e

    def should_show(self, show_progress):
        """
        Determines whether to show the progress bar.

        Parameters:
            show_progress (bool): Whether to show the progress bar.

        Returns:
            bool: True if the progress bar should be shown, False otherwise.
        """
        if show_progress:
            return show_progress
        return self.logger.logger.level == self.logger.DEBUG

    def vectorize_str_list(self, data: list, show_progress=False):
        """
        Converts a list of strings to vectors.

        Parameters:
            data (list of str): The list of strings to be vectorized.
            show_progress (bool, optional): Whether to show the progress bar. Default is False.

        Returns:
            list of numpy.ndarray: The list of vectors.
        """
        self.logger.debug(f"Embedding string list, time is depending on system CPU/GPU and data size...")
        return self.model.encode(data, show_progress_bar=self.should_show(show_progress))

    def vectorize_log_lines(self, log_lines, show_progress=False):
        """
        Converts log lines to vectors.

        Parameters:
            log_lines (list of str): The log lines to be vectorized.
            show_progress (bool, optional): Whether to show the progress bar. Default is False.

        Returns:
            list of numpy.ndarray: The list of vectors.
        """
        self.logger.debug(f"Embedding {len(log_lines)} log lines ...")
        return self.model.encode(log_lines, show_progress_bar=self.should_show(show_progress))

    def vectorize_data(self, encoding_field: str, data: list, show_progress=False):
        """
        Converts data to vectors, creating embeddings on a specified field.

        Parameters:
            encoding_field (str): The field to be encoded.
            data (list of dict): The data to be vectorized.
            show_progress (bool, optional): Whether to show the progress bar. Default is False.

        Returns:
            list of models.PointStruct: The list of point structures with vectors and payloads.
        """
        self.logger.debug(f"Embedding data, time is depending on system CPU/GPU and data size...")
        return [
            models.PointStruct(id=idx, vector=self.model.encode(doc[encoding_field], show_progress_bar=self.should_show(show_progress)).tolist(), payload=doc)
            for idx, doc in enumerate(data)
        ]

    def vectorize_multi(self, encoding_fields, data: list, show_progress=False):
        """
        Converts data to vectors, creating embeddings on multiple fields.

        Parameters:
            encoding_fields (list of str): The fields to be encoded.
            data (list of dict): The data to be vectorized.
            show_progress (bool, optional): Whether to show the progress bar. Default is False.

        Returns:
            list of models.PointStruct: The list of point structures with vectors and payloads.
        """
        self.logger.debug(f"Embedding data ...")
        embeddings = []
        vectors = {}
        for idx, doc in enumerate(data):
            for field in encoding_fields:
                vectors[field] = self.qh.model.encode(doc[field], show_progress_bar=self.should_show(show_progress)).tolist()
            embeddings.append(models.PointStruct(id=idx, vector=vectors, payload=doc))
        return embeddings

    def vectorize_data_multi(self, encoding_field, data):
        """
        Converts data to vectors using multiprocessing and GPUs/CUDA power.

        Parameters:
            encoding_field (str): The field to be encoded.
            data (list of dict): The data to be vectorized.

        Returns:
            list of models.PointStruct: The list of point structures with vectors and payloads.
        """
        self.logger.debug(f"Embedding data, multi-process mode, time depending on system CPU/GPU and data size...")
        pool = self.model.start_multi_process_pool()
        embeddings = [models.PointStruct(id=idx, vector=self.model.encode_multi_process(doc[encoding_field], pool).tolist(), payload=doc) for idx, doc in enumerate(data)]
        self.model.stop_multi_process_pool(pool)
        return embeddings

    def get_root_causes(self, ground_truth_folder):
        """
        Identifies and extracts root causes from logs and their ground truths.

        Args:
            ground_truth_folder (str): Path to the folder containing ground truth JSON data.

        Returns:
            list[dict]: List of root causes extracted from the ground truths,
            each including hints and errors concatenated.
        """
        self.logger.debug(f"{self.NAME}: Processing logs and ground truths from {ground_truth_folder} ...")
        root_causes = []
        pairs = self.lg.get_pairs(ground_truth_folder)
        for gt_file, log_file in pairs:
            gt = self.files.get_json(gt_file)
            if self.lg.is_valid_gt(gt):
                log = self.files.get_lines(log_file)
                gt = self.lg.insert_log_lines(gt, log)
                root_causes += self.lg.add_concat_fields(gt)
            else:
                self.logger.warning(f"{self.NAME}: Skipping invalid ground truth file: {gt_file}")
        return root_causes

    def get_root_cause_matches(self, root_cause, match_fields, collection_name, limit=1):
        """
        Searches for root cause matches in Qdrant.

        Args:
            root_cause (dict): The root cause dictionary.
            match_fields (list): The list of fields to match.
            collection_name (str): The name of the Qdrant collection.
            limit (int, optional): The maximum number of matches to return. Defaults to 1.

        Returns:
            dict: A dictionary containing the match results.
        """
        self.logger.debug(f"{self.NAME}: Searching Qdrant collection: '{collection_name}' with limit '{limit}'")
        root_causes_matches = {}
        for field in match_fields:
            res = self.qh.encode_search(
                text=root_cause[field],
                collection_name=collection_name,
                limit=limit,
                vector_name=field,
            )
            root_causes_matches[field] = {"results": self.lg.clean_dict_fields(match_fields, res[0][0]) if res else "", "score": res[0][1] if res else 0}
        return root_causes_matches

    def get_matches(self, analysis, collection_name, limit=1):
        """
        Gets matches for the analysis from Qdrant.

        Args:
            analysis (dict): The analysis dictionary.
            collection_name (str): The name of the Qdrant collection.
            limit (int, optional): The maximum number of matches to return. Defaults to 1.

        Returns:
            list: A list of match vocabularies.
        """
        self.logger.debug("Getting matches...")
        return [self.get_root_cause_matches(root_cause, ["errors_match", "hints_match"], collection_name, limit) for root_cause in analysis["root_causes"]]

    def get_payload(self, matches):
        """
        Processes match results into a payload.

        Args:
            matches (list): The list of match vocabularies.

        Returns:
            list: A list of context objects.
        """
        self.logger.debug("Processing results...")
        context_objects = []
        for match in matches:
            for v in match.values():
                if v["results"] not in context_objects:
                    context_objects.append(v["results"])
        # self.logger.debug(f"Result Objects: \n{json.dumps(context_objects, indent=2)}")
        return context_objects
