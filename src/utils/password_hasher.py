import hashlib
import secrets


def hash_password(password: str) -> str:
    """Hash a password using SHA-256 to match the frontend hashing

    Args:
        password (str): The plain text password to hash
    Returns:
        str: The SHA-256 hashed password
    """
    return hashlib.sha256(password.encode('utf-8')).hexdigest()

def generate_key(length: int = 64) -> str:
    """Generate a secure random key for encryption or other purposes.
    The default length is 64 bytes, 512 bits of entropy."""
    return secrets.token_urlsafe(length)