import logging
import io
import logging
import sys
import uuid
from logging.handlers import RotatingFileHandler
from pathlib import Path
from typing import Any

import colorama
from colorama import Fore, Style

# Initialize colorama for Windows terminal color support
colorama.init()

CURRENT_DIR = Path(__file__).parent.resolve()


class ColoredFormatter(logging.Formatter):
    """Custom formatter to add colors to console logs."""

    COLORS = {
        "DEBUG": Fore.LIGHTBLACK_EX,
        "INFO": Fore.GREEN,
        "WARNING": Fore.YELLOW,
        "ERROR": Fore.RED,
        "CRITICAL": Fore.RED + Style.BRIGHT,
    }

    def format(self, record):
        """Format the log record with colors based on the level."""
        formatted_msg = super().format(record)
        levelname = record.levelname
        if levelname in self.COLORS:
            return f"{self.COLORS[levelname]}{formatted_msg}{Style.RESET_ALL}"
        return formatted_msg


class Logger:
    """
    Logger utility class for easier log management.
    """

    CRITICAL = logging.CRITICAL
    FATAL = logging.FATAL
    ERROR = logging.ERROR
    WARNING = logging.WARNING
    WARN = logging.WARN
    INFO = logging.INFO
    DEBUG = logging.DEBUG
    NOTSET = logging.NOTSET

    LOG_LEVEL = logging.INFO

    LOGS_FOLDER = CURRENT_DIR / Path("../../log/")

    def __init__(
        self,
        name: str = f"Log-{uuid.uuid4().hex}",
        logfile: str = "Log.log",
        max_bytes: int = 1024 * 1024 * 100,  # 100 MB
        backup_count: int = 10,
        level: int = logging.INFO,
        mode: str = "w",
        colored_console: bool = False,
    ) -> None:
        self.level = level
        self.logger = logging.getLogger(name)
        self.logger.setLevel(self.level)
        self.logger.propagate = False

        if not self.logger.handlers:
            # File handler (UTF-8 safe)
            file_handler = RotatingFileHandler(str(self.LOGS_FOLDER / Path(logfile)), maxBytes=max_bytes, backupCount=backup_count, mode=mode, encoding="utf-8")
            file_formatter = logging.Formatter(
                "%(asctime)s    %(levelname)-8s   %(message)s",
                datefmt="%Y-%m-%d %H:%M:%S",
            )
            file_handler.setFormatter(file_formatter)

            # Console handler with forced UTF-8 encoding
            stream = io.TextIOWrapper(sys.stdout.buffer, encoding="utf-8", errors="replace")
            stream_handler = logging.StreamHandler(stream)
            if colored_console:
                stream_formatter = ColoredFormatter(
                    "%(asctime)s    %(levelname)-8s   %(message)s",
                    datefmt="%Y-%m-%d %H:%M:%S",
                )
            else:
                stream_formatter = file_formatter
            stream_handler.setFormatter(stream_formatter)

            self.logger.addHandler(file_handler)
            self.logger.addHandler(stream_handler)

    @staticmethod
    def sanitize(s: Any) -> str:
        """
        Remove characters not encodable in the current output stream encoding.
        """
        encoding = sys.stdout.encoding or "utf-8"
        if not isinstance(s, str):
            s = str(s)
        return s.encode(encoding, errors="replace").decode(encoding)

    def debug(self, message: Any) -> None:
        self.logger.debug(self.sanitize(message))

    def info(self, message: Any) -> None:
        self.logger.info(self.sanitize(message))

    def warning(self, message: Any) -> None:
        self.logger.warning(f"<!> {self.sanitize(message)}")

    def error(self, message: Any) -> None:
        self.logger.error(f"<<!>> {self.sanitize(message)}")

    def critical(self, message: Any) -> None:
        self.logger.critical(f"<<<!>>> {self.sanitize(message)}")
