import re
from typing import List


class Tokenizer:
    """
    Approximates OpenAI token counts for log data using a fallback heuristic.
    """

    TOKEN_PATTERN = re.compile(r'\w+|[^\w\s]|"(?:\\.|[^"\\])*"|\'(?:\\.|[^\'\\])*\'|\{|}|\[|]|[:,]', re.UNICODE)
    SPECIAL_PATTERN = re.compile(r"\w+|[^\w\s]", re.UNICODE)

    def approximate(self, text: str, correction_factor: float = 0.93222) -> int:
        """
        Approximate token count for a text string.

        Args:
            text (str): Input text.
            correction_factor (float, optional): Correction factor for token count.
        Returns:
            int: Approximated token count.
        """
        text = text.lower()
        # Tokenize text into basic segments (words, punctuation, JSON structures, strings, etc.)
        tokens = self.TOKEN_PATTERN.findall(text)
        # Refine tokens further to handle special cases for finer granularity
        refined_tokens: List[str] = []
        for token in tokens:
            refined_tokens.extend(self.SPECIAL_PATTERN.findall(token))
        # Adjust token count based on token lengths (OpenAI's tokens often split long text pieces)
        adjusted_token_count = 0
        for token in refined_tokens:
            if len(token) > 1:
                adjusted_token_count += len(token) // 10 + 1
            else:
                adjusted_token_count += 1 * correction_factor
        approx_count = int(adjusted_token_count * correction_factor)
        safety_margin = max(5, int(approx_count * 0.05))
        return approx_count + safety_margin
