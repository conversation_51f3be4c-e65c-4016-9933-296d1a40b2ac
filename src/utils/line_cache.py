import gc
from typing import Dict, Iterator, Optional, Tuple


class LineCache:
    """
    A class used to cache lines of text with their corresponding indices.
    """

    def __init__(self, unique=False):
        """
        Initializes the LineCache with an empty store and an empty set of lines.

        Args:
            unique (bool): If True, only unique lines will be stored in self.store.
                           Used for training to avoid duplicate lines.
                           If False (default), all lines will be stored, including duplicates.
                           Used for analysis to mark and identify all occurrences.
        """
        self.store = {}  # Maps indices to lines, the actual lines may contain duplicates (unless unique=True)
        self.lines_set = set()  # Contains only unique lines
        self.unique = unique

    def add(self, i: int, line: str, unique=None) -> None:
        """
        Adds a line to the cache.

        Args:
            i (int): The index of the line.
            line (str): The line of text to be added.
            unique (bool): If True, only adds the line if it is not already in the cache.
                           If None, uses the instance's unique setting.
        """
        # Skip if line is None
        if line is None:
            return

        # Use instance's unique setting if not specified
        if unique is None:
            unique = self.unique

        # For training (unique=True): only add if line is not already in lines_set
        # For analysis (unique=False): add all lines including duplicates
        should_add = not unique or (unique and line not in self.lines_set)

        if should_add:
            self.store[i] = line  # Contains duplicates if unique=False, only uniques if unique=True
            self.lines_set.add(line)  # Always contains unique lines, as python sets store only unique items

    def add_to_lines_set(self, line: str) -> None:
        """
        Adds a line to the lines_set (unique lines).

        Args:
            line (str): The line of text to be added.
        """
        self.lines_set.add(line)

    def get_by_index(self, i: int) -> Optional[str]:
        """
        Retrieves a line from the cache by its index.

        Args:
            i (int): The index of the line to retrieve.

        Returns:
            Optional[str]: The line of text at the given index, or None if not found.
        """
        value = self.store.get(i)
        return str(value) if value is not None else None

    def is_stored(self, line: str) -> bool:
        """
        Checks if a line is stored in the cache.

        Args:
            line (str): The line of text to check.

        Returns:
            bool: True if the line is stored, False otherwise.
        """
        return line in self.lines_set

    def get_all(self) -> Dict[int, str]:
        """
        Retrieves all lines stored in the cache.

        Returns:
            Dict[int, str]: A dictionary of all stored lines with their indices.
        """
        return self.store

    def get_iterator(self) -> Iterator[Tuple[int, str]]:
        """
        Returns an iterator for the stored lines.

        Returns:
            Iterator[Tuple[int, str]]: An iterator yielding tuples of (index, line).
        """
        return self.store.items()

    def clear(self, gc_collect=False) -> None:
        """
        Clears the cache and optionally runs garbage collection.

        Args:
            gc_collect (bool): If True, runs garbage collection after clearing the cache.
        """
        self.store.clear()
        # Note: We intentionally don't clear lines_set to maintain the history of seen lines
        if gc_collect:
            gc.collect()

    def get_all_and_clear(self, gc_collect=False) -> Dict[int, str]:
        """
        Retrieves all lines stored in the cache and then clears the cache.

        Args:
            gc_collect (bool): If True, runs garbage collection after clearing the cache.

        Returns:
            Dict[int, str]: A dictionary of all stored lines with their indices before clearing.
        """
        all_lines = self.get_all().copy()
        self.clear(gc_collect)
        return all_lines

    def get_size(self) -> int:
        """
        Returns the number of lines stored in the cache.

        Returns:
            int: The number of lines stored in the cache.
        """
        return len(self.store)

    def remove(self, i: int) -> None:
        """
        Removes a line from the cache by its index.
        Note: This only removes the entry from self.store, not from lines_set.

        Args:
            i (int): The index of the line to remove.
        """
        if i in self.store:
            del self.store[i]
