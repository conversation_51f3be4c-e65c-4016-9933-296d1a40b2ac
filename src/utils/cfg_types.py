def convert_self_type(value):
    """
    Converts a string to the appropriate type (int, float, bool, or str).

    Args:
        value (str): The string to convert.

    Returns:
        The converted value in its appropriate type.
    """
    if value.isdigit():
        return int(value)
    try:
        return float(value)
    except ValueError:
        pass
    if value.lower() in ["true", "false"]:
        return value.lower() == "true"
    return value
