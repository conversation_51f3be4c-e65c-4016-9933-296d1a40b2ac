import contextlib
import warnings
from functools import wraps

import requests
import urllib3
from urllib3.exceptions import InsecureRequestWarning

urllib3.disable_warnings(category=urllib3.exceptions.InsecureRequestWarning)
old_merge_environment_settings = requests.Session.merge_environment_settings


@contextlib.contextmanager
def no_ssl_verification():
    """
    Context manager to disable SSL verification and warnings for requests within this context.
    When under proxy and not possible to set Verify=False to an inner library function than you can use this.
    """
    opened_adapters = set()

    @wraps(old_merge_environment_settings)
    def merge_environment_settings(self, url, proxies, stream, verify, cert):
        """
        Override the merge_environment_settings method of requests.Session to disable SSL verification.

        Parameters:
            url (str): The URL for the request.
            proxies (dict): The proxies to be used for the request.
            stream (bool): Whether to stream the request.
            verify (bool): Whether to verify SSL certificates.
            cert (str or tuple): The client certificate to be used.

        Returns:
            dict: The merged environment settings with SSL verification disabled.
        """
        opened_adapters.add(self.get_adapter(url))
        settings = old_merge_environment_settings(self, url, proxies, stream, verify, cert)
        settings['verify'] = False
        return settings

    requests.Session.merge_environment_settings = merge_environment_settings

    try:
        with warnings.catch_warnings():
            warnings.simplefilter('ignore', InsecureRequestWarning)
            yield
    finally:
        requests.Session.merge_environment_settings = old_merge_environment_settings
        for adapter in opened_adapters:
            try:
                adapter.close()
            except Exception as e:
                print(f"Error closing adapter: {e}")
