import logging
import multiprocessing
import os
import platform
import time
import uuid
from functools import wraps
from typing import Dict, Optional, Any, Callable, TypeVar, cast, List

import psutil
from pympler import asizeof

from utils.logger import Logger

# Type variables for generic functions
T = TypeVar("T")
R = TypeVar("R")


def get_recommended_threads() -> int:
    """
    Get recommended number of threads based on CPU cores.

    Returns:
        int: Number of CPU cores, or 1 if error.
    """
    try:
        # Get the number of CPU cores
        cpu_cores = os.cpu_count()
        if cpu_cores is None:
            # Fallback to multiprocessing if os.cpu_count() returns None
            cpu_cores = multiprocessing.cpu_count()
        # Recommended number of threads can be set to the number of CPU cores
        recommended_threads = cpu_cores
        return recommended_threads
    except Exception as e:
        print(f"Error getting CPU cores: {e}")
        return 1  # Default to 1 thread if there is an error


def get_memory_info() -> Dict[str, int]:
    """
    Get total and available system memory.

    Returns:
        dict: Total and available memory in bytes.
    """
    mem = psutil.virtual_memory()
    return {"total_memory": mem.total, "available_memory": mem.available}


def get_mem_info(ob: Any, msg: str = "Size of object is:", logger: Optional[Logger] = None) -> None:
    """
    Print/log the real memory size of an object.

    Args:
        ob (Any): Object to measure.
        msg (str, optional): Message prefix.
        logger (Logger, optional): Logger instance.
    """
    mem_size = f"{msg} {asizeof.asizeof(ob)}"
    if logger:
        logger.debug(mem_size)
    else:
        print(mem_size)


def timed(fn: Callable[..., R]) -> Callable[..., R]:
    """
    Decorator to benchmark and measure function execution time.

    Args:
        fn (Callable): Function to decorate.

    Returns:
        Callable: Wrapped function with timing.
    """

    @wraps(fn)
    def wrapper(*args: Any, **kwargs: Any) -> R:
        result: Optional[R] = None
        start = time.time()
        if args and kwargs:
            result = fn(*args, **kwargs)
        elif args and not kwargs:
            result = fn(*args)
        elif not args and not kwargs:
            result = fn()
        else:
            result = fn(*args, **kwargs)  # Default case to handle any combination

        elapsed_time = time.time() - start
        hours, rem = divmod(elapsed_time, 3600)
        minutes, seconds = divmod(rem, 60)

        # Format parameters for logging
        max_var_len = 80
        parameters: List[str] = []
        for value in args:
            value_str = str(value)
            if len(value_str) > max_var_len:
                value_str = value_str[:max_var_len] + "..."
            parameters.append(f"{value_str}")

        for name, value in kwargs.items():
            value_str = str(value)
            if len(value_str) > max_var_len:
                value_str = value_str[:max_var_len] + "..."
            parameters.append(f"{name}={value_str}")

        param_str = ", ".join(parameters)
        runtime = ">>> Performance benchmark\n   -- function: {}({})\n   -- run-time: {:0>2}:{:0>2}:{:05.5f}".format(fn.__name__, param_str, int(hours), int(minutes), seconds)

        if args and hasattr(args[0], "logger"):
            runtime = runtime.replace("\n", "")
            args[0].logger.debug(runtime)
        else:
            print(runtime)

        return cast(R, result)

    return wrapper


def get_hw_spec(recommended_threads: int = 3) -> str:
    """
    Get formatted hardware specifications string.

    Args:
        recommended_threads (int, optional): Recommended threads. Default 3.

    Returns:
        str: Hardware specs summary.
    """
    memory_info = get_memory_info()
    architecture = " ".join(platform.architecture())
    return (
        f".: "
        f"{platform.processor()} :: "
        f"{os.cpu_count()} cores :: "
        f"{platform.machine()} :: "
        f"{architecture} :: "
        f"RAM Total: {memory_info['total_memory'] / (1024 ** 3):.2f}GB :: "
        f"Free: {memory_info['available_memory'] / (1024 ** 3):.2f}GB :: "
        f"THREADS Max: {get_recommended_threads()} Recommended: {recommended_threads} ::"
    )


class TimeMonitor:
    """
    Context manager for benchmarking code execution time.
    """

    NAME: str = "TimeMonitor"
    LOG_LEVEL: int = logging.INFO

    def __init__(self, msg: str = "Elapsed time:", logger: Optional[Logger] = None) -> None:
        """
        Initialize TimeMonitor context manager.

        Args:
            msg (str, optional): Message prefix.
            logger (Logger, optional): Logger instance.
        """
        self.start: float = time.time()
        self.msg: str = msg
        self.end: float = 0.0
        # Use the provided logger or create a new one with a specific name to avoid using the root logger
        self.logger: Optional[Logger] = (
            logger
            if logger
            else Logger(
                name=f"TimeMonitor-{uuid.uuid4().hex}",
                logfile="TimeMonitor.log",
                level=self.LOG_LEVEL,
            )
        )

    def __enter__(self) -> "TimeMonitor":
        """
        Start timing on context enter.

        Returns:
            TimeMonitor: Self.
        """
        self.start = time.time()
        return self

    def __exit__(
        self,
        exc_type: Optional[type],
        exc_val: Optional[Exception],
        exc_tb: Optional[Any],
    ) -> None:
        """
        Stop timing on context exit and log/print result.

        Args:
            exc_type (Optional[type]): Exception type.
            exc_val (Optional[Exception]): Exception value.
            exc_tb (Optional[Any]): Exception traceback.
        """
        self.end = time.time()
        hours, rem = divmod(self.end - self.start, 3600)
        minutes, seconds = divmod(rem, 60)
        current_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        message = f"{self.msg} {int(hours):0>2}:{int(minutes):0>2}:{seconds:05.5f} (Current date/time: {current_time})"
        if self.logger:
            self.logger.info(f"{self.NAME}: {message}")
        else:
            print(message)
