import csv
import json
import sys


def load_csv(fp_in, delimiter=",", quotechar='"', remove_empty=False, custom_headers=None, **kwargs):
    r = csv.DictReader(fp_in, delimiter=delimiter, quotechar=quotechar, fieldnames=custom_headers)
    rows = [row_dct for row_dct in r]
    if remove_empty:
        rows = [dict([(k, item) for k, item in row.items() if item]) for row in rows]
    return rows


def save_json(data, fp_out, pretty_spaces=4, sort_keys=False, **kwargs):
    json.dump(data, fp_out, indent=pretty_spaces, sort_keys=sort_keys)


def csv2json(csv_file, json_file, **kwargs):
    csv_file_local, json_local = None, None
    try:
        if csv_file == "-" or csv_file is None:
            csv_file = sys.stdin
        elif isinstance(csv_file, str):
            csv_file = csv_local = open(csv_file, "r")

        if json_file == "-" or json_file is None:
            json_file = sys.stdout
        elif isinstance(json_file, str):
            json_file = json_local = open(json_file, "w")

        data = load_csv(csv_file, **kwargs)
        save_json(data, json_file, **kwargs)
    finally:
        if csv_local is not None:
            csv_local.close()
        if json_local is not None:
            json_local.close()


with open("InformationManagerIssues.csv", encoding="utf-8") as r, open("./InformationManagerIssues.json", "w", encoding="utf-8") as w:
    convert(r, w)
