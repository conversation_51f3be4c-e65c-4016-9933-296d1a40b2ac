// File: src/index.js
import React from 'react';
import {createRoot} from 'react-dom/client';
import {createBrowserRouter, RouterProvider} from 'react-router-dom';
import LoginPage from './pages/LoginPage';
import LogoutPage from './pages/LogoutPage';
import HomePage from './pages/HomePage';
import AnalysisPage from './pages/AnalysisPage';
import JobPage from './pages/JobPage';
import TrainPage from './pages/TrainPage';
import KnowledgeCollectionPage from './pages/KnowledgeCollectionPage';
import KnowledgeCollectionView from './pages/KnowledgeCollectionView';
import AboutKnutGPTPage from './pages/AboutKnutGPTPage';
import App from './App';
import {UserProvider} from './context/UserContext';
import AuthGuard from './components/AuthGuard';
import './index.css';

// Temporarily suppress React Router v7 warnings until they fix the issue
const originalWarn = console.warn;
console.warn = (...args) => {
    if (args[0]?.includes?.('React Router Future Flag Warning')) {
        return;
    }
    originalWarn.apply(console, args);
};

const container = document.getElementById('root');

const router = createBrowserRouter([
    {
        path: "/login",
        element: <LoginPage/>
    },
    {
        path: "/logout",
        element: <LogoutPage/>
    },
    {
        path: "/",
        element: <AuthGuard><App/></AuthGuard>,
        children: [
            {path: "/", element: <HomePage/>},
            {path: "analysis", element: <AnalysisPage/>},
            {path: "analysis/:id", element: <AnalysisPage/>},
            {path: "job/:id", element: <JobPage/>},
            {path: "train", element: <TrainPage/>},
            {path: "knowledge-collections", element: <KnowledgeCollectionPage/>},
            {path: "knowledge-collection/:id", element: <KnowledgeCollectionView/>},
            {path: "about", element: <AboutKnutGPTPage/>},
        ]
    }
], {
    future: {
        v7_startTransition: true,
        v7_relativeSplatPath: true
    }
});

createRoot(container).render(
    <UserProvider>
        <RouterProvider router={router}/>
    </UserProvider>
);