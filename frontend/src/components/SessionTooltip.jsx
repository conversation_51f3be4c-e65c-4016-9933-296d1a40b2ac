// File: src/components/SessionTooltip.jsx

import React, {useEffect, useRef, useState} from 'react';

/**
 * Helper to normalize sessionType strings into friendly labels.
 */
const getFriendlySessionType = (session) => {
    if (!session.sessionType) return '';
    const type = String(session.sessionType).toLowerCase();
    if (type === 'train') return 'Training';
    if (type === 'analyze') return 'Analysis';
    return session.sessionType;
};

/**
 * Helper to normalize analysis method strings into friendly labels.
 */
const getFriendlyMethod = (method) => {
    if (!method) return '';
    const lower = String(method).toLowerCase();
    if (lower === 'neural') return 'Neural';
    if (lower === 'keywords') return 'Keywords';
    if (lower === 'regex') return 'RegEx';
    return method;
};

/**
 * SessionTooltip
 * Renders a hover-triggered tooltip displaying session metadata.
 * Props:
 *  - session: full session object (optional)
 *  - project, method, sessionType: fallback values if session prop is missing
 *  - children: label or trigger element
 */
const SessionTooltip = ({session, project, method, sessionType, animatedState, children}) => {
    const [isVisible, setIsVisible] = useState(false);
    const titleRef = useRef(null);
    const tooltipRef = useRef(null);

    // Show/hide tooltip on hover
    const handleMouseEnter = () => setIsVisible(true);
    const handleMouseLeave = () => setIsVisible(false);

    // Position tooltip just below the trigger
    useEffect(() => {
        if (isVisible && titleRef.current && tooltipRef.current) {
            const {height} = titleRef.current.getBoundingClientRect();
            tooltipRef.current.style.top = `${height + 8}px`;
            tooltipRef.current.style.left = '0';
        }
    }, [isVisible]);

    // Use either full session object or provided individual props
    const sessionData = session || {
        sessionId: 'Loading...',
        userId: 'Loading...',
        project: project || 'Loading...',
        sessionType: sessionType || 'Loading...',
        method: method || null,
        logFile: 'Loading...',
        knowledgeCollection: null
    };

    return (
        <div className="relative inline-block">
            {/* Trigger element */}
            <span
                ref={titleRef}
                className="text-xl text-sky-600 cursor-pointer relative group"
                onMouseEnter={handleMouseEnter}
                onMouseLeave={handleMouseLeave}
            >
        {children || `${getFriendlySessionType(sessionData)} Session`}
                <span
                    className="absolute bottom-0 left-0 w-0 h-0.5 bg-sky-600 transition-all duration-200 ease-out group-hover:w-full"/>
      </span>

            {/* Tooltip panel */}
            <div
                ref={tooltipRef}
                className={`absolute min-w-max p-3 bg-white border border-gray-400/70 rounded-lg shadow-lg z-10
          transition-all duration-200 ease-in-out mt-2
          ${isVisible ? 'opacity-100 translate-y-0 scale-100' : 'opacity-0 -translate-y-1 scale-95 pointer-events-none'}`}
                onMouseEnter={handleMouseEnter}
                onMouseLeave={handleMouseLeave}
            >
                <div className="grid gap-1 text-sm">
                    {/* Session fields */}
                    <div className="flex items-center px-2 py-1">
                        <span className="font-medium text-sky-900">Session ID:</span>
                        <span className="ml-2 text-gray-600">{sessionData.sessionId}</span>
                    </div>
                    <div className="flex items-center px-2 py-1">
                        <span className="font-medium text-sky-900">User ID:</span>
                        <span className="ml-2 text-gray-600">{sessionData.userId}</span>
                    </div>
                    <div className="flex items-center px-2 py-1">
                        <span className="font-medium text-sky-900">Project:</span>
                        <span className="ml-2 text-gray-600">{sessionData.project}</span>
                    </div>
                    <div className="flex items-center px-2 py-1">
                        <span className="font-medium text-sky-900">Session Type:</span>
                        <span className="ml-2 text-gray-600">
              {getFriendlySessionType(sessionData) || (sessionData.method ? 'Analysis' : 'Training')}
            </span>
                    </div>
                    {sessionData.method && (
                        <div className="flex items-center px-2 py-1">
                            <span className="font-medium text-sky-900">Method:</span>
                            <span className="ml-2 text-gray-600">{getFriendlyMethod(sessionData.method)}</span>
                        </div>
                    )}
                    <div className="flex items-center px-2 py-1">
                        <span className="font-medium text-sky-900">Log File:</span>
                        <span className="ml-2 text-gray-600 truncate max-w-xs">{sessionData.logFile}</span>
                    </div>
                    {sessionData.knowledgeCollection && (
                        <div className="flex items-center px-2 py-1">
                            <span className="font-medium text-sky-900">Collection:</span>
                            <span className="ml-2 text-gray-600">{sessionData.knowledgeCollection}</span>
                        </div>
                    )}
                    {/* Show keyword details for keyword-based sessions */}
                    {sessionData.method === 'keywords' && sessionData.keywords && (
                        <>
                            <div className="flex items-center px-2 py-1">
                                <span className="font-medium text-sky-900">Keywords:</span>
                                <span className="ml-2 text-gray-600">{sessionData.keywords.join(', ')}</span>
                            </div>
                            <div className="flex items-center px-2 py-1">
                                <span className="font-medium text-sky-900">False Positives:</span>
                                <span className="ml-2 text-gray-600">{sessionData.falsePositives.join(', ')}</span>
                            </div>
                        </>
                    )}
                </div>
                {/* Tooltip arrow */}
                <div
                    className="absolute -top-2 left-8 w-4 h-4 bg-white border-l border-t rounded-sm border-gray-400/70 transform rotate-45"></div>
            </div>
        </div>
    );
};

export default SessionTooltip;