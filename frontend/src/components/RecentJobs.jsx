// File: src/components/RecentJobs.jsx

import React, {useEffect, useState} from 'react';
import {useNavigate} from 'react-router-dom';
import api from '../services/api';
import {useUser} from '../context/UserContext';
import JobListItem from './JobListItem';

// Column definitions for the jobs table
const columns = [
    {label: 'ID', key: 'jobId'},
    {label: 'Project', key: 'project'},
    {label: 'Type', key: 'jobType'},
    {label: 'Method', key: 'method'},
    {label: 'Collection', key: 'knowledgeCollection'},
    {label: 'Log File', key: 'logFile'},
    {label: 'Status', key: 'status'},
    {label: 'Results', key: 'resultHTML'},
    {label: 'Expire Time', key: 'expire'}
];

/**
 * RecentJobs
 * Fetches and displays a user's recent jobs in a table.
 * Handles loading, error, and empty states.
 */
const RecentJobs = () => {
    const navigate = useNavigate();
    const {user} = useUser();
    const [jobs, setJobs] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState('');

    // Fetch jobs when user changes
    useEffect(() => {
        let active = true;
        setLoading(true);
        setError('');

        if (!user?.id) {
            setJobs([]);
            setLoading(false);
            return;
        }

        api.listJobs(user.id)
            .then(data => {
                if (active) {
                    setJobs(data);
                    setLoading(false);
                }
            })
            .catch(err => {
                if (active) {
                    setError('Could not load job history');
                    setLoading(false);
                }
            });

        return () => {
            active = false;
        };
    }, [user]);

    // Navigate to job detail on row click
    const handleRowClick = (job) => {
        navigate(`/job/${job.jobId || job.id}`, {state: {job}});
    };

    // Prompt login if no user
    if (!user?.id) {
        return <div className="p-6 text-gray-600 text-center">Login to see your job history.</div>;
    }

    // Show loading spinner
    if (loading) {
        return <div className="flex items-center justify-center py-8">Loading jobs…</div>;
    }

    // Show error message
    if (error) {
        return <div className="text-red-500 p-4 text-center">{error}</div>;
    }

    return (
        <div className="mt-4 w-full max-w-6xl mx-auto">
            {jobs.length === 0 ? (
                <div className="py-8 text-gray-500 text-center">No jobs found!</div>
            ) : (
                <div className="bg-white rounded-xl shadow-xl border border-gray-300 overflow-auto">
                    <table className="w-full table-auto">
                        <thead className="bg-gray-100 border-b border-gray-300">
                        <tr>
                            {columns.map(col => (
                                <th
                                    key={col.key}
                                    className="px-3 py-2 text-left text-xs font-semibold text-gray-700 uppercase tracking-wide whitespace-nowrap"
                                >
                                    {col.label}
                                </th>
                            ))}
                        </tr>
                        </thead>
                        <tbody>
                        {jobs.map(job => (
                            <JobListItem
                                key={job.jobId || job.id}
                                job={job}
                                onClick={handleRowClick}
                            />
                        ))}
                        </tbody>
                    </table>
                </div>
            )}
        </div>
    );
};

export default RecentJobs;
