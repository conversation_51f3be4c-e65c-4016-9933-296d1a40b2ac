import React from 'react';
import {Loader2} from 'lucide-react';

/**
 * LoadingModal - Beautiful transparent loading overlay
 * Blocks user interaction during async operations
 * Props:
 *  - isOpen: boolean - whether modal is visible
 *  - message: string - loading message to display
 *  - subMessage: string - optional secondary message
 */
const LoadingModal = ({
                          isOpen = false,
                          message = 'Processing...',
                          subMessage = ''
                      }) => {
    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
            {/* Backdrop with subtle blur */}
            <div className="absolute inset-0 bg-white/30 backdrop-blur-sm"/>

            {/* Loading content */}
            <div
                className="relative z-10 bg-white/90 backdrop-blur-md rounded-2xl shadow-2xl border border-white/50 p-8 mx-4 max-w-sm w-full">
                <div className="flex flex-col items-center space-y-6">
                    {/* Beautiful spinner */}
                    <div className="relative">
                        <Loader2 className="w-12 h-12 text-sky-500 animate-spin"/>
                        <div className="absolute inset-0 w-12 h-12 border-2 border-sky-200 rounded-full animate-pulse"/>
                        <div className="absolute inset-0 w-12 h-12 bg-sky-500/20 rounded-full animate-ping"/>
                    </div>

                    {/* Loading text */}
                    <div className="text-center space-y-2">
                        <h3 className="text-lg font-semibold text-gray-800">
                            {message}
                        </h3>
                        {subMessage && (
                            <p className="text-sm text-gray-600">
                                {subMessage}
                            </p>
                        )}
                    </div>

                    {/* Animated dots */}
                    <div className="flex space-x-1">
                        <div className="w-2 h-2 bg-sky-500 rounded-full animate-bounce"
                             style={{animationDelay: '0ms'}}/>
                        <div className="w-2 h-2 bg-sky-500 rounded-full animate-bounce"
                             style={{animationDelay: '150ms'}}/>
                        <div className="w-2 h-2 bg-sky-500 rounded-full animate-bounce"
                             style={{animationDelay: '300ms'}}/>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default LoadingModal;
