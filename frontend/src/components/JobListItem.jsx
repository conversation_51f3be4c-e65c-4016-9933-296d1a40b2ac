// File: src/components/JobListItem.jsx

import React from 'react';
import {FileCheck2} from 'lucide-react';

/**
 * JobListItem
 * Renders one row in the RecentJobs table, displaying job details.
 * Props:
 *  - job: job object with all display fields
 *  - onClick: row click handler
 */
const JobListItem = ({job, onClick}) => {
    const {
        jobId,
        project,
        jobType,
        method,
        knowledgeCollection,
        logFile,
        status,
        resultHTML,
        expire
    } = job;

    // Format expire time for display
    const formatExpireTime = (expireStr) => {
        if (!expireStr) return 'N/A';
        try {
            const date = new Date(expireStr);
            return date.toLocaleString();
        } catch {
            return expireStr;
        }
    };

    // Determine status styling
    const getStatusStyle = (status) => {
        switch (status?.toLowerCase()) {
            case 'completed':
                return 'text-green-600 bg-green-50 border-green-200';
            case 'running':
            case 'processing':
                return 'text-blue-600 bg-blue-50 border-blue-200';
            case 'failed':
            case 'error':
                return 'text-red-600 bg-red-50 border-red-200';
            case 'pending':
            case 'queued':
                return 'text-yellow-600 bg-yellow-50 border-yellow-200';
            default:
                return 'text-gray-600 bg-gray-50 border-gray-200';
        }
    };

    return (
        <tr
            className="border-b border-gray-200 hover:bg-gray-50 cursor-pointer transition-colors"
            onClick={() => onClick(job)}
        >
            <td className="px-3 py-2 text-xs font-medium text-gray-900">
                {jobId || 'N/A'}
            </td>
            <td className="px-3 py-2 text-xs text-gray-700">
                {project || 'N/A'}
            </td>
            <td className="px-3 py-2 text-xs text-gray-700 whitespace-nowrap">
                <span className={`px-2 py-1 rounded-full text-xs font-medium border ${
                    jobType === 'analyze' ? 'text-blue-600 bg-blue-50 border-blue-200' :
                        jobType === 'train' ? 'text-purple-600 bg-purple-50 border-purple-200' :
                            'text-gray-600 bg-gray-50 border-gray-200'
                }`}>
                    {jobType || 'N/A'}
                </span>
            </td>
            <td className="px-3 py-2 text-xs text-gray-700 whitespace-nowrap">
                {method || 'N/A'}
            </td>
            <td className="px-3 py-2 text-xs text-gray-700">
                {knowledgeCollection || 'N/A'}
            </td>
            <td className="px-3 py-2 text-xs text-gray-700">
                {logFile || 'N/A'}
            </td>
            <td className="px-3 py-2 text-xs whitespace-nowrap">
                <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getStatusStyle(status)}`}>
                    {status || 'Unknown'}
                </span>
            </td>
            <td className="px-3 py-2 text-xs text-gray-700 whitespace-nowrap">
                {resultHTML ? (
                    <div className="flex items-center text-green-600">
                        <FileCheck2 size={14} className="mr-1"/>
                        Available
                    </div>
                ) : (
                    <span className="text-gray-400">N/A</span>
                )}
            </td>
            <td className="px-3 py-2 text-xs text-gray-700">
                {formatExpireTime(expire)}
            </td>
        </tr>
    );
};

export default JobListItem;
