// File: src/components/LoadingComponent.jsx
import React from 'react';
import {Loader, Search} from 'lucide-react';

/**
 * LoadingComponent
 * Renders a status icon, progress bar, percentage text, and optional message
 * Props:
 *  - percentage: number 0 to 100
 *  - label: stage label (Uploading, Training, etc.)
 *  - method: analysis method name for conditional styling
 *  - status: backend status string for special case
 *  - message: optional status message
 */
const LoadingComponent = ({
                              percentage = 0,
                              label = 'Processing', // "Analyzing", "Training", "Processing", "Uploading"
                              method = '', // e.g. 'keywords', 'neural', 'regex', only for analyze
                              status = '', // backend status like 'waiting-for-exec', 'analyzing-neural', 'uploading', etc.
                              message = '', // optional message to display
                              linesProcessed = 0, // lines processed so far
                              totalLines = 0, // total lines to process
                              lastUpdated = null, // last update timestamp
                          }) => {
    // Handle waiting state
    if (status === 'waiting-for-exec') {
        return (
            <div className="py-12">
                <div className="flex flex-col items-center justify-center space-y-6">
                    <div className="relative">
                        <Loader className="animate-spin w-16 h-16 text-blue-600"/>
                        <div className="absolute inset-0 bg-blue-600/20 rounded-full animate-pulse blur-md"/>
                    </div>

                    <div className="text-center space-y-2">
                        <h3 className="text-2xl font-semibold text-gray-800">
                            Waiting for Executor
                        </h3>
                        <p className="text-gray-600 font-medium">
                            Initializing system components...
                        </p>
                    </div>
                </div>
            </div>
        );
    }

    // Determine styling based on label
    const isUploading = label.toLowerCase() === 'uploading';
    const isTraining = label.toLowerCase() === 'training';

    const getStatusConfig = () => {
        if (isUploading) {
            return {
                icon: (
                    <div className="relative">
                        <div
                            className="w-16 h-16 bg-gradient-to-br from-emerald-500 to-green-600 rounded-full flex items-center justify-center">
                            <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2}
                                      d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"/>
                            </svg>
                        </div>
                        <div className="absolute inset-0 bg-emerald-500/30 rounded-full animate-pulse blur-lg"/>
                    </div>
                ),
                colors: {
                    progress: 'from-emerald-500 to-green-600',
                    glow: 'from-emerald-400 to-green-500'
                }
            };
        } else if (isTraining) {
            return {
                icon: (
                    <div className="relative">
                        <div
                            className="w-16 h-16 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-full flex items-center justify-center">
                            <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2}
                                      d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
                            </svg>
                        </div>
                        <div className="absolute inset-0 bg-purple-500/30 rounded-full animate-pulse blur-lg"/>
                    </div>
                ),
                colors: {
                    progress: 'from-purple-500 to-indigo-600',
                    glow: 'from-purple-400 to-indigo-500'
                }
            };
        } else {
            return {
                icon: (
                    <div className="relative">
                        <div
                            className="w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center">
                            <Search className="w-8 h-8 text-white"/>
                        </div>
                        <div className="absolute inset-0 bg-blue-500/30 rounded-full animate-pulse blur-lg"/>
                    </div>
                ),
                colors: {
                    progress: 'from-blue-500 to-indigo-600',
                    glow: 'from-blue-400 to-indigo-500'
                }
            };
        }
    };

    const statusConfig = getStatusConfig();
    const clampedPercentage = Math.min(Math.max(percentage, 0), 100);

    return (
        <div className="py-12">
            <div className="flex flex-col items-center justify-center space-y-8">
                {/* Status Icon */}
                <div className="flex justify-center">
                    {statusConfig.icon}
                </div>

                {/* Status Text */}
                <div className="text-center space-y-3">
                    <h3 className="text-2xl font-semibold text-gray-800">
                        {label}
                    </h3>

                    <div className="flex items-center justify-center gap-3 text-gray-600">
            <span className="text-lg font-medium">
              {clampedPercentage}% complete
            </span>
                        {clampedPercentage > 0 && (
                            <>
                                <div className="h-1 w-1 bg-gray-400 rounded-full"/>
                                <span className="text-sm">
                  {isUploading ?
                      (clampedPercentage < 50 ? 'Uploading files...' :
                          clampedPercentage < 90 ? 'Processing files...' : 'Almost done...') :
                      isTraining ?
                          (clampedPercentage < 50 ? 'Training model...' :
                              clampedPercentage < 90 ? 'Optimizing...' : 'Finalizing...') :
                          (clampedPercentage < 50 ? 'Starting analysis...' :
                              clampedPercentage < 90 ? 'Processing data...' : 'Almost done...')
                  }
                </span>
                            </>
                        )}
                    </div>
                </div>

                {/* Progress Bar */}
                <div className="w-full max-w-md space-y-3">
                    <div className="relative h-3 bg-gray-200 rounded-full overflow-hidden">
                        <div
                            className={`h-full bg-gradient-to-r ${statusConfig.colors.progress} rounded-full transition-all duration-700 ease-out relative`}
                            style={{width: `${clampedPercentage}%`}}
                        >
                            {/* Shine effect */}
                            <div
                                className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent rounded-full"/>

                            {/* Animated glow */}
                            {clampedPercentage > 0 && (
                                <div
                                    className={`absolute inset-0 bg-gradient-to-r ${statusConfig.colors.glow} rounded-full blur-sm opacity-50 animate-pulse`}
                                />
                            )}
                        </div>
                    </div>

                    <div className="flex justify-between text-sm text-gray-500 font-medium">
                        <span>0%</span>
                        <span className="text-gray-700 font-semibold">{clampedPercentage}%</span>
                        <span>100%</span>
                    </div>
                </div>

                {/* Detailed Progress Information */}
                {(linesProcessed > 0 || totalLines > 0) && (
                    <div className="mt-4 max-w-md">
                        <div className="bg-blue-50 rounded-lg border border-blue-200 p-3">
                            <div className="text-center space-y-1">
                                {totalLines > 0 && (
                                    <p className="text-sm text-blue-700">
                                        <span className="font-medium">{linesProcessed.toLocaleString()}</span>
                                        {' of '}
                                        <span className="font-medium">{totalLines.toLocaleString()}</span>
                                        {' lines processed'}
                                    </p>
                                )}
                                {lastUpdated && (
                                    <p className="text-xs text-blue-600">
                                        Last updated: {new Date(lastUpdated).toLocaleTimeString()}
                                    </p>
                                )}
                            </div>
                        </div>
                    </div>
                )}

                {/* Message section */}
                {message && (
                    <div className="mt-6 max-w-md">
                        <div className="bg-gray-50 rounded-lg border border-gray-200 p-4">
                            <p className="text-gray-700 text-center">{message}</p>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
};

export default LoadingComponent;