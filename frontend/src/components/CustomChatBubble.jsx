// File: src/components/CustomChatBubble.jsx
import React, {useEffect, useState} from 'react';
import MessageFeedback from './MessageFeedback';
import MarkdownRenderer from './MarkdownRenderer';

/**
 * CustomChatBubble Component
 *
 * An animated chat bubble with typewriter effect that displays analysis results.
 * Features feedback functionality, markdown rendering, and customizable styling.
 *
 * @param {string} analysisResult - Main content to display
 * @param {string} message - Additional message shown after analysis result
 * @param {boolean} enableEffects - Whether to enable typewriter animation and delays (default: true)
 * @param {number} typingSpeed - Speed of typewriter animation in milliseconds (only if enableEffects=true)
 * @param {number} startDelay - Delay before animation starts in milliseconds (only if enableEffects=true)
 * @param {string} backgroundColor - Background color of the chat bubble
 * @param {string} textColor - Text color (currently unused)
 * @param {number} tailPosition - Position of the tail as percentage from left
 * @param {Function} onFeedbackSubmit - Callback when feedback is submitted
 * @param {Function} onComplete - Callback when animation completes (or immediately if no effects)
 * @param {boolean} showFeedback - Whether to show feedback component
 * @param {ReactNode} children - Additional content to show after animation
 */
const CustomChatBubble = ({
                              analysisResult,
                              message = "Not happy with the result? Then you can try another method or re-trigger the same analysis.",
                              enableEffects = true,
                              typingSpeed = 1,
                              startDelay = 200,
                              backgroundColor = "#e1edf5",
                              textColor = "#283140",
                              tailPosition = 94,
                              onFeedbackSubmit = (feedback) => console.log('Feedback received:', feedback),
                              onComplete = () => {
                              },
                              showFeedback = true,
                              children,
                          }) => {
    const [displayText, setDisplayText] = useState('');
    const [isAnimationComplete, setIsAnimationComplete] = useState(false);
    const [showFeedbackUI, setShowFeedbackUI] = useState(false);

    const combinedMessage = `${analysisResult}\n\n${message}`;

    /**
     * Text display effect - either typewriter animation or instant display
     * Controlled by enableEffects parameter
     */
    useEffect(() => {
        if (!enableEffects) {
            // No effects - show everything immediately
            setDisplayText(combinedMessage);
            setIsAnimationComplete(true);
            setShowFeedbackUI(true);
            onComplete();
            return;
        }

        // Effects enabled - use typewriter animation
        let currentIndex = 0;
        let typingInterval;
        let delay;

        setDisplayText('');
        setIsAnimationComplete(false);
        setShowFeedbackUI(false);

        delay = setTimeout(() => {
            typingInterval = setInterval(() => {
                if (currentIndex < combinedMessage.length) {
                    setDisplayText(combinedMessage.substring(0, currentIndex + 1));
                    currentIndex++;
                } else {
                    clearInterval(typingInterval);
                    setIsAnimationComplete(true);
                    onComplete();
                    setTimeout(() => {
                        setShowFeedbackUI(true);
                    }, 300);
                }
            }, typingSpeed);
        }, startDelay);

        return () => {
            clearTimeout(delay);
            clearInterval(typingInterval);
        };
        // eslint-disable-next-line
    }, [combinedMessage, typingSpeed, startDelay, enableEffects]);

    // Split displayed text into analysis result and message portions
    const analysisResultLength = analysisResult.length;
    const displayedAnalysisResult = displayText.substring(0, analysisResultLength);
    const displayedMessage = displayText.substring(analysisResultLength);

    /**
     * Handle feedback submission - enhances feedback with additional metadata
     * @param {Object} feedback - Feedback object from MessageFeedback component
     */
    const handleFeedback = (feedback) => {
        const enhancedFeedback = {
            ...feedback,
            responseContent: analysisResult,
            timestamp: new Date().toISOString(),
        };
        onFeedbackSubmit(enhancedFeedback);
    };

    return (
        <div className="relative flex flex-col items-center w-full">
            <div className="relative w-full">
                {/* Main chat bubble with customizable background and border */}
                <div
                    className="rounded-2xl p-4 relative border border-sky-800/15 shadow-md text-gray-700"
                    style={{backgroundColor}}
                >
                    {/* Feedback component in the top-right corner, only if enabled */}
                    {showFeedbackUI && showFeedback && (
                        <div className="absolute top-2 right-2 z-10">
                            <MessageFeedback onFeedbackSubmit={handleFeedback}/>
                        </div>
                    )}
                    <div className="text-base leading-relaxed pb-4 pt-1">
                        <MarkdownRenderer content={displayedAnalysisResult}/>
                        <div className="-mt-2">
                            <div className="flex items-center flex-wrap">
                <span className="font-medium mr-4">{displayedMessage}
                    {(isAnimationComplete || !enableEffects) && children && (
                        <div className="inline-flex items-center">
                            {children}
                        </div>
                    )}
                </span>
                            </div>
                        </div>
                    </div>
                </div>
                {/* Tail */}
                <div className="relative">
                    {/* Main tail */}
                    <div
                        className="absolute w-0 h-0"
                        style={{
                            bottom: "-29px",
                            left: `${tailPosition}%`,
                            transform: "translateX(-50%)",
                            borderLeft: "15px solid transparent",
                            borderRight: "15px solid transparent",
                            borderTop: `30px solid ${backgroundColor}`,
                            zIndex: 2,
                        }}
                    />
                    {/* Border tail - 1px larger */}
                    <div
                        className="absolute w-0 h-0"
                        style={{
                            bottom: "-30px",
                            left: `${tailPosition}%`,
                            transform: "translateX(-50%)",
                            borderLeft: "16px solid transparent",
                            borderRight: "16px solid transparent",
                            borderTop: "31px solid rgba(7, 89, 133, 0.2)", // gray-400/50 equivalent
                            zIndex: 1,
                        }}
                    />
                </div>
            </div>
        </div>
    );
};

export default CustomChatBubble;