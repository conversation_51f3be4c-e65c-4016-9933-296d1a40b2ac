// File: src/components/InfoSection.jsx
import React, {useState} from 'react';
import {HelpCircle} from 'lucide-react';
import Modal from './Modal';

/**
 * InfoSection
 * Shows a title with description and opens a modal for extended info
 * Props:
 *  - title: heading text
 *  - description: brief line under title
 *  - extendedDescription: longer paragraph shown in modal
 *  - listItems: optional bullet list entries for modal
 *  - children: extra node (e.g. a button) rendered beside the title
 */

const InfoSection = ({
                         title,
                         description,
                         extendedDescription,
                         listItems = [],
                         children
                     }) => {
    const [isModalOpen, setIsModalOpen] = useState(false);
    const openModal = () => setIsModalOpen(true);
    const closeModal = () => setIsModalOpen(false);

    const hasListItems = Array.isArray(listItems) && listItems.length > 0;

    return (
        <div className="p-4 px-2.5 rounded-xl">
            <div className="flex items-center justify-between">
                <div className="flex items-center space-x-1 pr-4">
                    <h2 className="text-lg font-medium text-gray-700">{title}</h2>
                    <button
                        onClick={openModal}
                        className="rounded-full p-1 text-gray-700 hover:text-sky-600 hover:bg-sky-50 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-sky-200 focus:ring-offset-1"
                        aria-label="More information"
                    >
                        <HelpCircle size={21} className=" stroke-current fill-white"/>
                    </button>
                </div>
                {children && (
                    <div className="flex-shrink-0">
                        {children}
                    </div>
                )}
            </div>
            <p className="text-gray-600 leading-snug mt-1">{description}</p>

            {isModalOpen && (
                <Modal onClose={closeModal}>
                    <div className="min-h-[50vh] flex flex-col text-lg">
                        <h2 className="text-2xl font-semibold mb-4 text-gray-700 pr-8">{title}</h2>
                        <div className="text-gray-700 space-y- flex-1">
                            {extendedDescription && <p className="text-base leading-relaxed">{extendedDescription}</p>}

                            {hasListItems && (
                                <ul className="space-y-3 pl-1">
                                    {listItems.map((item, index) => (
                                        <li key={index} className="flex items-start">
                                            <span
                                                className="inline-block w-1.5 h-1.5 rounded-full bg-sky-500 mt-2 mr-2 flex-shrink-0"/>
                                            <span className="leading-relaxed">{item}</span>
                                        </li>
                                    ))}
                                </ul>
                            )}
                        </div>
                    </div>
                </Modal>
            )}
        </div>
    );
};

export default InfoSection;