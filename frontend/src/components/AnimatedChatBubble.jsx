import React, {useEffect, useRef, useState} from 'react';

/**
 * AnimatedChatBubble Component
 *
 * A chat bubble with typewriter animation effect and blinking cursor.
 * Features dynamic height tracking and customizable styling.
 *
 * @param {string} message - Text to display with typewriter animation
 * @param {number} typingSpeed - Animation speed in milliseconds per character
 * @param {number} startDelay - Delay before animation starts in milliseconds
 * @param {string} backgroundColor - Background color of the chat bubble
 * @param {string} textColor - Text color inside the bubble
 * @param {number} tailPosition - Position of tail as percentage from left (0-100)
 * @param {string} width - CSS width value for the container
 * @param {string} maxWidth - CSS max-width value for the container
 */
const AnimatedChatBubble = ({
                                message = "Placeholder ",
                                typingSpeed = 25,
                                startDelay = 400,
                                backgroundColor = "#dfe1e6",
                                textColor = "#334155",
                                tailPosition = 50, // Position of the tail in percentage from left
                                width = "100%",
                                maxWidth = "800px"
                            }) => {
    const [displayText, setDisplayText] = useState('');
    const [isAnimationComplete, setIsAnimationComplete] = useState(false);
    const [showCursor, setShowCursor] = useState(true);
    const bubbleRef = useRef(null);
    const [bubbleHeight, setBubbleHeight] = useState(0);

    /**
     * Track bubble height changes using ResizeObserver
     * Ensures container maintains proper height as content changes
     */
    useEffect(() => {
        if (bubbleRef.current) {
            const observer = new ResizeObserver(entries => {
                for (let entry of entries) {
                    setBubbleHeight(entry.contentRect.height);
                }
            });

            observer.observe(bubbleRef.current);
            return () => observer.disconnect();
        }
    }, []);

    /**
     * Typewriter animation effect with blinking cursor
     * Displays text character by character, then shows cursor briefly
     */
    useEffect(() => {
        let currentIndex = 0;
        let typingInterval;
        let cursorInterval;

        // Start the typing animation after the specified delay
        const delay = setTimeout(() => {
            typingInterval = setInterval(() => {
                if (currentIndex < message.length) {
                    setDisplayText(message.substring(0, currentIndex + 1));
                    currentIndex++;
                } else {
                    clearInterval(typingInterval);
                    setIsAnimationComplete(true);

                    // Show cursor for a short period, then hide it
                    setTimeout(() => {
                        setShowCursor(false);
                    }, 1500); // Show cursor for 1.5 seconds after typing completes
                }
            }, typingSpeed);
        }, startDelay);

        return () => {
            clearTimeout(delay);
            clearInterval(typingInterval);
            clearInterval(cursorInterval);
        };
    }, [message, typingSpeed, startDelay]);

    return (
        <div className="relative" style={{width, maxWidth, height: bubbleHeight + 20}}>
            <div
                ref={bubbleRef}
                className="absolute bottom-0 w-full"
            >
                {/* Main chat bubble with customizable styling */}
                <div
                    className="rounded-3xl shadow-md p-6"
                    style={{backgroundColor, color: textColor}}
                >
                    <p className="text-base md:text-xl leading-relaxed">
                        {displayText}
                        {!isAnimationComplete || showCursor ? <span className="ml-1 animate-pulse">|</span> : null}
                    </p>
                </div>

                {/* Tail/pointer positioned based on tailPosition prop */}
                <div
                    className="absolute w-0 h-0"
                    style={{
                        bottom: "-30px",
                        left: `${tailPosition}%`,
                        transform: "translateX(-50%)",
                        borderLeft: "20px solid transparent",
                        borderRight: "20px solid transparent",
                        borderTop: `30px solid ${backgroundColor}`,
                    }}
                />
            </div>
        </div>
    );
};

export default AnimatedChatBubble;