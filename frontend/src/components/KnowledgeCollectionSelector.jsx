// File: src/components/KnowledgeCollectionSelector.jsx

import React, {useState} from 'react';
import {Calendar, Clock, Database, Download, Lock, PlusCircle, Upload} from 'lucide-react';
import Modal from './Modal';
import {useNavigate} from 'react-router-dom';
import {useUser} from '../context/UserContext';

/**
 * Formats an ISO date string to a readable short date format
 * @param {string} dateString - ISO date string to format
 * @returns {string} Formatted date (e.g., "Jul 29") or "N/A" if invalid
 */
function formatDate(dateString) {
    if (!dateString) return 'N/A';
    const d = new Date(dateString);
    if (isNaN(d.getTime())) return 'N/A';
    return d.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric'
    });
}

/**
 * Extracts and separates project collections and user collections from the collections object
 * @param {Object} collections - Collections object containing project_collections and user_collections
 * @param {Object} selectedProject - Currently selected project object (unused but kept for future filtering)
 * @param {Object} user - Current user object (unused but kept for future filtering)
 * @returns {Object} Object with general (project) and custom (user) collections arrays
 */
function getSpecificCollections(collections, selectedProject, user) {
    // We keep this function in case we need to filter collections on the FE, we do int on BE now
    const general = collections.project_collections || [];
    const custom = collections.user_collections || [];
    return {general, custom};
}


/**
 * KnowledgeCollectionSelector Component
 *
 * A React component that displays a unified interface for managing knowledge collections.
 * Shows both project-wide (general) and user-specific (custom) collections with functionality
 * for selection, export, import, and creation of new collections.
 *
 * Features:
 * - Displays collections in a unified list with visual distinction between types
 * - Toggle selection of collections (click to select/deselect)
 * - Export collections as JSON files
 * - Import collections from .snapshot or .json files (UI only, backend pending)
 * - Navigate to create new collection page
 * - Loading state with skeleton placeholders
 * - Accessible keyboard navigation and ARIA labels
 *
 * @param {Object} props - Component props
 * @param {Object|null} props.selectedProject - Currently selected project object, required for collections display
 * @param {Object} props.collections - Collections object with project_collections and user_collections arrays
 * @param {Object|null} props.selectedCollection - Currently selected collection object
 * @param {Function} props.onCollectionSelect - Callback function called when collection is selected/deselected
 * @param {boolean} [props.loading=false] - Whether to show loading skeleton placeholders
 *
 * @returns {JSX.Element} The rendered knowledge collection selector interface
 *
 * @example
 * <KnowledgeCollectionSelector
 *   selectedProject={project}
 *   collections={{
 *     project_collections: [{ id: '1', name: 'Main Collection', type: 'general' }],
 *     user_collections: [{ id: '2', name: 'My Collection', type: 'custom' }]
 *   }}
 *   selectedCollection={selectedCollection}
 *   onCollectionSelect={(collection) => setSelectedCollection(collection)}
 *   loading={false}
 * />
 */
const KnowledgeCollectionSelector = ({
                                         selectedProject,
                                         collections = [],
                                         selectedCollection,
                                         onCollectionSelect,
                                         loading = false,
                                     }) => {
    const {user} = useUser();
    // const userId = user?.id;
    const navigate = useNavigate();
    const [importModal, setImportModal] = useState(false);

    // Get general/custom, always current actual user
    const {general, custom} = getSpecificCollections(collections, selectedProject, user);

    // Combine collections for unified display
    const allCollections = [
        ...(general && general.length ? general.map(col => ({...col, type: 'general'})) : []),
        ...custom.map(col => ({...col, type: 'custom'}))
    ];

    const handleCollectionClick = (col) => {
        if (selectedCollection?.id === col.id) {
            onCollectionSelect(null);
        } else {
            onCollectionSelect(col);
        }
    };

    const handleCreateNew = () => {
        navigate('/knowledge-collection/new');
    };

    const handleExport = (collection, e) => {
        e.stopPropagation();
        const filename = collection.name + '.snapshot.json';
        const blob = new Blob([JSON.stringify(collection, null, 2)], {type: 'application/json'});
        const link = document.createElement('a');
        link.download = filename;
        link.href = URL.createObjectURL(blob);
        link.click();
        setTimeout(() => URL.revokeObjectURL(link.href), 5000);
    };

    const handleImport = (e) => {
        setImportModal(false);
        alert('Import feature coming soon (file accepted, no backend)!');
    };

    return (
        <div className="space-y-4">
            {!selectedProject ? (
                <div
                    className="flex items-center justify-center h-32 border-2 border-dashed border-gray-200 rounded-lg">
                    <p className="text-gray-500">Select a project first</p>
                </div>
            ) : loading ? (
                <div className="space-y-3">
                    {[...Array(2)].map((_, i) => (
                        <div key={i} className="h-20 bg-gray-100 rounded-xl animate-pulse"></div>
                    ))}
                </div>
            ) : (
                <>
                    {/* Unified Collection List */}
                    <div className="space-y-3">
                        {allCollections.map(col => (
                            <div
                                key={col.id}
                                className={`group relative p-3 rounded-lg border cursor-pointer transition-all duration-200 ${
                                    selectedCollection?.id === col.id
                                        ? 'bg-sky-100 border border-sky-500'
                                        : 'bg-sky-50/80 border-gray-400/70 hover:bg-sky-100/70 shadow-sm hover:border-sky-400'
                                }`}
                                onClick={() => handleCollectionClick(col)}
                                tabIndex={0}
                                role="button"
                                aria-pressed={selectedCollection?.id === col.id}
                                aria-label={`Select ${col.type} collection ${col.name}`}
                            >
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center space-x-4 flex-1 min-w-0">
                                        {/* Icon */}
                                        <div className="flex-shrink-0">
                                            <div className=" flex items-center justify-center">
                                                {col.type === 'general' ? (
                                                    <Database className="w-6 h-6 text-sky-600"/>
                                                ) : (
                                                    <Lock className="w-6 h-6 text-sky-600"/>
                                                )}
                                            </div>
                                        </div>

                                        {/* Content */}
                                        <div className="flex-1 min-w-0 grid grid-cols-2 gap-8">
                                            <div>
                                                <h3 className="font-medium text-gray-900 truncate">
                                                    {col.name}
                                                </h3>
                                                <p className="text-sm text-gray-500 mt-0.5">
                                                    {col.type === 'general' ? 'Shared Collection' : 'Private Collection'}
                                                </p>
                                            </div>

                                            <div className="grid grid-cols-2 gap-4 text-sm">
                                                <div className="flex items-center space-x-2 text-gray-600">
                                                    <Clock className="w-4 h-4 text-gray-400 flex-shrink-0"/>
                                                    <div>
                                                        <div
                                                            className="font-medium text-xs text-gray-500 uppercase tracking-wide leading-tight">Updated
                                                        </div>
                                                        <div
                                                            className="font-medium text-xs">{formatDate(col.last_updated)}</div>
                                                    </div>
                                                </div>
                                                <div className="flex items-center space-x-2 text-gray-600">
                                                    <Calendar className="w-4 h-4 text-gray-400 flex-shrink-0"/>
                                                    <div>
                                                        <div
                                                            className="font-medium text-xs text-gray-500 uppercase tracking-wide leading-tight">Expires
                                                        </div>
                                                        <div
                                                            className="font-medium text-xs">{formatDate(col.expiration)}</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    {/* Export Button - More Prominent */}
                                    <button
                                        className="flex items-center space-x-2 px-4 py-3 ml-4 rounded-lg bg-white border border-gray-400/60 text-gray-700 hover:bg-gray-100 transition-all duration-200 font-semibold "
                                        onClick={(e) => handleExport(col, e)}
                                        title="Export collection"
                                        tabIndex={0}
                                        type="button"
                                    >
                                        <Download className="w-4 h-4"/>
                                        <span className="text-sm">Export</span>
                                    </button>
                                </div>
                            </div>
                        ))}

                        {allCollections.length === 0 && (
                            <div className="py-8 text-center text-gray-400 italic">
                                No collections available for this project.
                            </div>
                        )}
                    </div>

                    {/* Action Buttons - Styled as Collection Items */}
                    <div className="grid grid-cols-2 gap-3 mt-1">
                        <button
                            className="group p-3 rounded-lg border bg-white border-gray-400/70 hover:bg-sky-100/50 hover:shadow-sm transition-all duration-200 text-left shadow-sm"
                            onClick={handleCreateNew}
                            aria-label="Create new collection"
                            type="button"
                        >
                            <div className="flex items-center space-x-4">
                                <div className="flex items-center justify-center  transition-colors">
                                    <PlusCircle className="w-6 h-6 text-sky-600"/>
                                </div>
                                <div className="flex-1">
                                    <h3 className="font-medium text-gray-900 leading-tight">Create New Collection</h3>
                                    <p className="text-sm text-gray-500 mt-1">Build a custom knowledge base</p>
                                </div>
                            </div>
                        </button>

                        <button
                            className="group p-3 rounded-lg border bg-white border-gray-400/70 hover:bg-sky-100/50 hover:shadow-sm transition-all duration-200 text-left shadow-sm"
                            onClick={() => setImportModal(true)}
                            aria-label="Import collection"
                            type="button"
                        >
                            <div className="flex items-center space-x-4">
                                <div className=" flex items-center justify-center transition-colors">
                                    <Upload className="w-6 h-6 text-sky-600"/>
                                </div>
                                <div className="flex-1">
                                    <h3 className="font-medium text-gray-900 leading-tight">Import Collection</h3>
                                    <p className="text-sm text-gray-500 mt-1">Upload existing knowledge data</p>
                                </div>
                            </div>
                        </button>
                    </div>

                    {/* Import Modal */}
                    {importModal && (
                        <Modal onClose={() => setImportModal(false)} maxWidth="max-w-md">
                            <div className="text-xl font-semibold mb-4">Import Knowledge Collection</div>
                            <form
                                onSubmit={e => {
                                    e.preventDefault();
                                    handleImport(e);
                                }}
                                className="space-y-4"
                            >
                                <input
                                    type="file"
                                    accept=".snapshot,.json,application/json"
                                    className="w-full border-2 border-gray-200 rounded-lg px-3 py-3 focus:border-sky-500 focus:outline-none transition-colors"
                                    required
                                />
                                <div className="flex justify-end gap-3 mt-6">
                                    <button
                                        type="button"
                                        onClick={() => setImportModal(false)}
                                        className="px-4 py-2 rounded-lg border border-gray-300 bg-gray-100 text-gray-700 hover:bg-gray-200 transition-colors"
                                    >
                                        Cancel
                                    </button>
                                    <button
                                        type="submit"
                                        className="px-5 py-2 bg-sky-600 text-white rounded-lg font-medium hover:bg-sky-700 transition-colors"
                                    >
                                        Import
                                    </button>
                                </div>
                                <div className="text-xs text-gray-400 mt-2">
                                    Accepted files: <span className="font-mono">.snapshot, .json</span>
                                </div>
                            </form>
                        </Modal>
                    )}
                </>
            )}
        </div>
    );
};

export default KnowledgeCollectionSelector;
