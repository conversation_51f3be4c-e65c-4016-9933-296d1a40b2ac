// File: src/components/GenericRegexInput.jsx

import React, {useEffect, useRef, useState} from 'react';
import {AlertCircle, Loader2, X} from 'lucide-react';

const variantStyles = {
    error: {
        tagBg: "bg-red-100",
        tagText: "text-red-700",
        tagBorder: "border-red-600",
        inputBorder: "border-gray-300",
        focusBorder: "focus-within:border-red-600",
        removeHover: "hover:text-red-800",
    },
    falsePositive: {
        tagBg: "bg-amber-100",
        tagText: "text-amber-700",
        tagBorder: "border-amber-600",
        inputBorder: "border-gray-300",
        focusBorder: "focus-within:border-amber-600",
        removeHover: "hover:text-amber-800",
    },
    hints: {
        tagBg: "bg-cyan-100",
        tagText: "text-cyan-700",
        tagBorder: "border-cyan-600",
        inputBorder: "border-gray-300",
        focusBorder: "focus-within:border-cyan-600",
        removeHover: "hover:text-cyan-800",
    },
    success: {
        tagBg: "bg-green-100",
        tagText: "text-green-700",
        tagBorder: "border-green-600",
        inputBorder: "border-gray-300",
        focusBorder: "focus-within:border-green-600",
        removeHover: "hover:text-green-800",
    }
};

/**
 * GenericRegexInput
 *
 * Props:
 * - regexes: array, current regex list
 * - setRegexes: function, updates regex state
 * - placeholder: string
 * - variant: string ('error', 'falsePositive', 'hints', 'success')
 * - fetchVocabulary: async function (returns vocabulary array for this variant)
 */
const GenericRegexInput = ({
                               regexes,
                               setRegexes,
                               placeholder,
                               variant = "error",
                               fetchVocabulary,
                           }) => {
    const [inputValue, setInputValue] = useState('');
    const [error, setError] = useState(null);
    const [loadingVocab, setLoadingVocab] = useState(false);
    const containerRef = useRef(null);
    const hasLoadedRef = useRef(false);
    const styles = variantStyles[variant];

    useEffect(() => {
        async function load() {
            if (typeof fetchVocabulary === 'function' && !hasLoadedRef.current) {
                hasLoadedRef.current = true; // Set immediately to prevent concurrent calls
                setLoadingVocab(true);
                try {
                    const vocab = await fetchVocabulary();
                    if (Array.isArray(vocab)) {
                        setRegexes([...vocab]);
                    }
                } catch (err) {
                    console.error('Error loading vocabulary:', err);
                    hasLoadedRef.current = false; // Reset on error to allow retry
                }
                setLoadingVocab(false);
            }
        }

        load();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [fetchVocabulary, variant]);

    // Reset loading guard when regexes array is cleared (for reset functionality)
    useEffect(() => {
        if (regexes.length === 0 && hasLoadedRef.current) {
            hasLoadedRef.current = false;
        }
    }, [regexes.length, variant]);

    useEffect(() => {
        if (containerRef.current) {
            containerRef.current.scrollTop = containerRef.current.scrollHeight;
        }
    }, [regexes.length]);

    // Validate if the input is a valid regex
    const isValidRegex = (pattern) => {
        try {
            new RegExp(pattern);
            return true;
        } catch (e) {
            return false;
        }
    };

    const handleKeyDown = (e) => {
        if (e.key === 'Enter' && inputValue.trim()) {
            if (!isValidRegex(inputValue.trim())) {
                setError('Invalid regular expression');
                return;
            }
            if (!regexes.includes(inputValue.trim())) {
                setRegexes([...regexes, inputValue.trim()]);
                setInputValue('');
                setError(null);
            } else {
                setError('This regular expression already exists');
            }
        }
    };

    const removeRegex = (regexToRemove) => {
        setRegexes(regexes.filter(regex => regex !== regexToRemove));
    };

    return (
        <div className="flex flex-col w-full">
            <div
                ref={containerRef}
                className="flex flex-wrap gap-2 p-2 border border-b-0 border-gray-300 rounded-t-lg bg-white max-h-[7.5rem] overflow-y-auto"
            >
                {loadingVocab ? (
                    <div className="flex items-center space-x-2 animate-pulse">
                        <Loader2 className="w-5 h-5 text-sky-400 animate-spin"/>
                        <span className="text-gray-400">Loading vocabulary...</span>
                    </div>
                ) : (
                    regexes.map((regex, index) => (
                        <span
                            key={index}
                            className={`inline-flex items-center px-3 py-1 rounded-xl text-sm ${styles.tagBg} ${styles.tagText} border ${styles.tagBorder}`}
                        >
              {regex}
                            <button
                                onClick={() => removeRegex(regex)}
                                className={`ml-2 focus:outline-none ${styles.removeHover}`}
                                aria-label="Remove regex"
                            >
                <X className="w-4 h-4"/>
              </button>
            </span>
                    ))
                )}
            </div>
            <div
                className={`flex border ${error ? 'border-red-400' : styles.inputBorder} rounded-b-lg overflow-hidden transition-colors ${styles.focusBorder}`}>
                <input
                    type="text"
                    value={inputValue}
                    onChange={(e) => {
                        setInputValue(e.target.value);
                        setError(null);
                    }}
                    onKeyDown={handleKeyDown}
                    className="flex-grow p-2.5 outline-none text-gray-700 placeholder-gray-400"
                    placeholder={placeholder}
                    disabled={loadingVocab}
                />
            </div>
            {error && (
                <div className="flex items-center text-red-500 text-sm mt-1">
                    <AlertCircle className="w-4 h-4 mr-1"/>
                    <span>{error}</span>
                </div>
            )}
        </div>
    );
};

export default GenericRegexInput;