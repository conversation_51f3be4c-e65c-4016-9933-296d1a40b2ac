// File: src/components/FileUploader.jsx
import React, {useRef, useState} from 'react';
import {FileText, Upload, X} from 'lucide-react';

/**
 * FileUploader Component
 *
 * A drag-and-drop file uploader with directory path input support.
 * Supports multiple file selection and provides visual feedback during drag operations.
 *
 * @param {Function} onFilesChange - Callback function called when files are added/removed
 */
const FileUploader = ({onFilesChange}) => {
    const [isDragging, setIsDragging] = useState(false);
    const [files, setFiles] = useState([]);
    const [dirPath, setDirPath] = useState('');
    const fileInputRef = useRef(null);

    /**
     * Handle drag enter event - starts drag state
     */
    const handleDragEnter = (e) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDragging(true);
    };

    /**
     * Handle drag leave event - ends drag state
     */
    const handleDragLeave = (e) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDragging(false);
    };

    /**
     * Handle drag over event - maintains drag state and sets drop effect
     */
    const handleDragOver = (e) => {
        e.preventDefault();
        e.stopPropagation();
        e.dataTransfer.dropEffect = 'copy';
        setIsDragging(true);
    };

    /**
     * Handle file drop event - processes dropped files
     */
    const handleDrop = (e) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDragging(false);

        const droppedFiles = [...e.dataTransfer.files];
        handleFiles(droppedFiles);
    };

    /**
     * Process and add new files to the file list
     * Limited to single file for log analysis
     * @param {File[]} newFiles - Array of File objects to add
     */
    const handleFiles = (newFiles) => {
        if (newFiles.length === 0) return;

        // Enforce single file limit
        if (newFiles.length > 1) {
            alert("Please select only one file for analysis. Multiple files will be supported in future versions.");
            return;
        }

        const file = newFiles[0];

        // Check file size (5GB limit)
        const maxSize = 5 * 1024 * 1024 * 1024; // 5GB in bytes
        if (file.size > maxSize) {
            alert(`File size (${(file.size / 1024 / 1024 / 1024).toFixed(1)}GB) exceeds the 5GB limit. Please select a smaller file.`);
            return;
        }

        // Replace existing file with new one
        const updatedFiles = [file];
        setFiles(updatedFiles);
        onFilesChange(updatedFiles);
    };

    /**
     * Trigger file input dialog when upload area is clicked
     */
    const handleClick = () => {
        fileInputRef.current?.click();
    };

    /**
     * Handle file selection from input dialog
     */
    const handleInputChange = (e) => {
        const selectedFiles = [...e.target.files];
        handleFiles(selectedFiles);
    };

    /**
     * Remove a specific file from the file list
     * @param {File} fileToRemove - File object to remove
     */
    const handleRemoveFile = (fileToRemove) => {
        const updatedFiles = files.filter(file => file !== fileToRemove);
        setFiles(updatedFiles);
        onFilesChange(updatedFiles);
    };

    /**
     * Handle directory path input - simulates file retrieval from directory
     * Currently creates a placeholder file for demo purposes
     */
    const handleDirectoryAdd = () => {
        if (!dirPath.trim()) return;
        // Simulate file retrieval with a placeholder file.
        const simulatedFile = {name: `Simulated file from ${dirPath}`};
        const updatedFiles = [...files, simulatedFile];
        setFiles(updatedFiles);
        onFilesChange(updatedFiles);
        setDirPath('');
    };

    return (
        <div className="flex items-center flex-col">
            <div className="w-full rounded-3xl p-1">
                {/* Main drag-and-drop upload area */}
                <div
                    onClick={handleClick}
                    onDragEnter={handleDragEnter}
                    onDragOver={handleDragOver}
                    onDragLeave={handleDragLeave}
                    onDrop={handleDrop}
                    className={`
            border-2 border-dashed rounded-2xl
            p-8 transition-colors duration-200
            flex flex-col items-center justify-center
            cursor-pointer bg-sky-100/25
            ${isDragging ? 'border-sky-500 bg-sky-100' : 'border-gray-400/60 hover:border-sky-800/50'}
          `}
                >
                    <input
                        type="file"
                        ref={fileInputRef}
                        onChange={handleInputChange}
                        className="hidden"
                    />
                    <div className={`mb-2 p-2 rounded-xl ${isDragging ? 'animate-wobble' : ''}`}>
                        <Upload className="w-8 h-8 text-sky-500"/>
                    </div>
                    <p className={`
            text-sm text-gray-500 text-center
            transition-all duration-100
            ${isDragging ? 'font-medium text-gray-500' : ''}
          `}>
                        Drag & Drop your file here, or click to select a file.
                    </p>
                    <div
                        className="mt-3 flex items-center space-x-2 px-3 py-1.5 bg-sky-100/60 border border-sky-800/15 rounded-lg shadow-sm">
            <span className="text-sm text-gray-600/90 font-medium">
             <span className='font-semibold'> Maximum file size:</span>  5 GB per file
            </span>
                    </div>
                </div>

                {/* Directory Path Input Section - Alternative file selection method */}
                <div className="mt-4">
                    <p className="text-sm text-gray-600 mb-1">Or enter a directory path:</p>
                    <div className="flex">
                        <input
                            type="text"
                            value={dirPath}
                            onChange={(e) => setDirPath(e.target.value)}
                            placeholder='e.g., file://///BOSCH.COM/...'
                            className="w-full border-y border-l border-gray-400 rounded-l-lg p-2 focus:outline-none focus:border-sky-600"
                        />
                        <button
                            onClick={handleDirectoryAdd}
                            className="bg-sky-500 text-white rounded-r-lg px-4  hover:bg-sky-600 focus:outline-none border-y border-r border-gray-300"
                        >
                            <div className="flex flex-col items-center">
                                <span className="text-xs font-medium leading-tight">Fetch</span>
                                <span className="text-xs font-medium leading-tight">Files</span>
                            </div>
                        </button>
                    </div>
                </div>

                {/* Uploaded Files List - Shows selected files with remove option */}
                {files.length > 0 && (
                    <div className="mt-6 w-full">
                        <ul className="grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                            {files.map((file, index) => (
                                <li
                                    key={index}
                                    className="flex items-center py-2 px-3 bg-white rounded-lg shadow-sm border border-gray-400 hover:border-sky-500 hover:shadow transition-all duration-200 group cursor-pointer"
                                >
                                    <div className="p-1 mr-1 transition-colors duration-200">
                                        <FileText
                                            className="w-4 h-4 text-gray-500 group-hover:text-sky-500 transition-colors duration-200"/>
                                    </div>
                                    <span
                                        className="text-sm text-gray-700 group-hover:text-gray-900 transition-colors duration-200 truncate max-w-md flex-1">
                    {file.name}
                  </span>
                                    <button
                                        onClick={() => handleRemoveFile(file)}
                                        className="ml-auto text-gray-500 group-hover:text-red-500 transition-colors duration-200"
                                    >
                                        <X className="w-4 h-4"/>
                                    </button>
                                </li>
                            ))}
                        </ul>
                    </div>
                )}
            </div>
        </div>
    );
};

export default FileUploader;