// File: src/components/MarkdownRenderer.jsx

import React from 'react';

/**
 * MarkdownRenderer
 * Renders basic markdown elements into React nodes
 * Props:
 *  - content: markdown text string
 *  - className: optional additional class names
 */
const MarkdownRenderer = ({content, className = ""}) => {
    // Parse markdown line by line into React elements
    const parseMarkdown = (text) => {
        if (!text) return [];
        const lines = text.split('\n');
        const elements = [];

        lines.forEach((line, index) => {
            // Header level three
            if (line.startsWith('### ')) {
                elements.push(
                    <h3 key={index} className="text-base font-semibold text-gray-700 mb-2 mt-3 first:mt-0">
                        {line.replace('### ', '')}
                    </h3>
                );
            }
            // Header level two
            else if (line.startsWith('## ')) {
                elements.push(
                    <h2 key={index} className="text-lg font-semibold text-gray-700 mb-2 mt-3 first:mt-0">
                        {line.replace('## ', '')}
                    </h2>
                );
            }
            // Header level one
            else if (line.startsWith('# ')) {
                elements.push(
                    <h1 key={index} className="text-lg font-bold text-gray-700 mb-2 mt-3 first:mt-0">
                        {line.replace('# ', '')}
                    </h1>
                );
            }
            // Bold text with **text**
            else if (line.includes('**')) {
                const parts = line.split(/(\*\*.*?\*\*)/g);
                const formattedParts = parts.map((part, partIndex) => {
                    if (part.startsWith('**') && part.endsWith('**')) {
                        return (
                            <strong key={partIndex} className="font-semibold text-gray-700">
                                {part.slice(2, -2)}
                            </strong>
                        );
                    }
                    return part;
                });
                elements.push(
                    <p key={index} className="mb-2 leading-relaxed">
                        {formattedParts}
                    </p>
                );
            }
            // Inline code with `code`
            else if (line.includes('`')) {
                const parts = line.split(/(`.*?`)/g);
                const formattedParts = parts.map((part, partIndex) => {
                    if (part.startsWith('`') && part.endsWith('`')) {
                        return (
                            <code key={partIndex}
                                  className="bg-gray-50 px-1.5 py-0.5 rounded text-sm font-mono text-gray-700">
                                {part.slice(1, -1)}
                            </code>
                        );
                    }
                    return part;
                });
                elements.push(
                    <p key={index} className="mb-2 leading-relaxed">
                        {formattedParts}
                    </p>
                );
            }
            // Empty line renders spacing
            else if (line.trim() === '') {
                elements.push(<div key={index} className="h-2"/>);
            }
            // Fallback paragraph
            else {
                elements.push(
                    <p key={index} className="mb-2 leading-relaxed">
                        {line}
                    </p>
                );
            }
        });

        return elements;
    };

    return (
        <div className={`markdown-content ${className}`}>
            {parseMarkdown(content)}
        </div>
    );
};

export default MarkdownRenderer;