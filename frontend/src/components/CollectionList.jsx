// src/components/CollectionList.jsx

import React, {useEffect, useMemo, useState} from 'react';
import {useNavigate} from 'react-router-dom';
import {ChevronDown, ChevronLeft, ChevronRight, ChevronUp, PlusCircle} from 'lucide-react';
import {getCollections} from '../services/api';

const CollectionList = () => {
    const [currentPage, setCurrentPage] = useState(1);
    const [sortConfig, setSortConfig] = useState({key: null, direction: 'asc'});
    const [collections, setCollections] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const rowsPerPage = 3;
    const navigate = useNavigate();

    // Fetch collections data from API
    useEffect(() => {
        const fetchCollections = async () => {
            try {
                setLoading(true);
                const response = await getCollections();
                setCollections(response);
                setError(null);
            } catch (err) {
                console.error('Failed to fetch collections:', err);
                setError('Failed to load collections. Please try again.');
                setCollections([]);
            } finally {
                setLoading(false);
            }
        };

        fetchCollections();
    }, []);

    const sortData = (data, sortConfig) => {
        if (!sortConfig.key) return data;

        return [...data].sort((a, b) => {
            if (a[sortConfig.key] < b[sortConfig.key]) {
                return sortConfig.direction === 'asc' ? -1 : 1;
            }
            if (a[sortConfig.key] > b[sortConfig.key]) {
                return sortConfig.direction === 'asc' ? 1 : -1;
            }
            return 0;
        });
    };

    const sortedData = useMemo(() => {
        return sortData(collections, sortConfig);
    }, [collections, sortConfig]);

    const totalPages = Math.ceil(sortedData.length / rowsPerPage);

    const currentData = sortedData.slice(
        (currentPage - 1) * rowsPerPage,
        currentPage * rowsPerPage
    );

    const handleSort = (key) => {
        setSortConfig(prevSort => ({
            key,
            direction: prevSort.key === key && prevSort.direction === 'asc' ? 'desc' : 'asc'
        }));
    };

    const handlePageChange = (page) => {
        if (page >= 1 && page <= totalPages) {
            setCurrentPage(page);
        }
    };

    const handleRowClick = (id) => {
        navigate(`/knowledge-collection/${id}`);
    };

    const handleCreateNewCollection = () => {
        navigate('/knowledge-collection/new', {state: {isEditMode: true}});
    };

    const columns = [
        {key: 'name', label: 'Name'},
        {key: 'owner', label: 'Owner'},
        {key: 'privacy', label: 'Private/Shared'}
    ];

    // Loading state
    if (loading) {
        return (
            <div className="my-4 w-full max-w-6xl space-y-6">
                <div className="bg-white rounded-xl overflow-hidden ring-1 ring-offset-2 ring-gray-300 p-8">
                    <div className="text-center text-gray-500">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-sky-500 mx-auto mb-4"></div>
                        Loading collections...
                    </div>
                </div>
            </div>
        );
    }

    // Error state
    if (error) {
        return (
            <div className="my-4 w-full max-w-6xl space-y-6">
                <div className="bg-white rounded-xl overflow-hidden ring-1 ring-offset-2 ring-gray-300 p-8">
                    <div className="text-center text-red-600">
                        <div className="mb-4">⚠️</div>
                        <div>{error}</div>
                        <button
                            onClick={() => window.location.reload()}
                            className="mt-4 px-4 py-2 bg-sky-500 text-white rounded-lg hover:bg-sky-600 transition-colors"
                        >
                            Retry
                        </button>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="my-4 w-full max-w-6xl space-y-6">
            <div className="bg-white rounded-xl overflow-hidden ring-1 ring-offset-2 ring-gray-300">
                <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                        <tr>
                            {columns.map(column => (
                                <th
                                    key={column.key}
                                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                                    onClick={() => handleSort(column.key)}
                                >
                                    <div className="flex items-center space-x-1">
                                        <span>{column.label}</span>
                                        <div className="flex flex-col">
                                            <ChevronUp
                                                className={`h-3 w-3 ${sortConfig.key === column.key && sortConfig.direction === 'asc' ? 'text-sky-500' : 'text-gray-400'}`}
                                            />
                                            <ChevronDown
                                                className={`h-3 w-3 ${sortConfig.key === column.key && sortConfig.direction === 'desc' ? 'text-sky-500' : 'text-gray-400'}`}
                                            />
                                        </div>
                                    </div>
                                </th>
                            ))}
                        </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                        {currentData.map((collection, index) => (
                            <tr
                                key={index}
                                className="hover:bg-sky-100/60 transition-colors cursor-pointer"
                                onClick={() => handleRowClick(collection.id)}
                            >
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{collection.name}</td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{collection.owner}</td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{collection.privacy}</td>
                            </tr>
                        ))}
                        {/* Create New Collection Row */}
                        <tr
                            className="hover:bg-sky-100/60 transition-colors cursor-pointer"
                            onClick={handleCreateNewCollection}
                        >
                            <td colSpan={3} className="p-1 py-2 whitespace-nowrap text-sm text-gray-900">
                                <div
                                    className="flex items-center justify-center py-1   text-sky-600  transition-colors font-medium  w-full">
                                    <PlusCircle className="w-5 h-5 mr-2"/>
                                    <span>Create new Collection</span>
                                </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div className="flex justify-center">
                <nav className="relative z-0 inline-flex rounded-xl shadow-sm overflow-hidden">
                    <button
                        onClick={() => handlePageChange(currentPage - 1)}
                        disabled={currentPage === 1}
                        className="relative inline-flex items-center px-3 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:bg-gray-100 disabled:cursor-not-allowed first:rounded-l-xl"
                    >
                        <ChevronLeft className="h-5 w-5"/>
                    </button>

                    {[...Array(totalPages)].map((_, index) => (
                        <button
                            key={index}
                            onClick={() => handlePageChange(index + 1)}
                            className={`relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium
                ${currentPage === index + 1
                                ? 'z-10 bg-sky-50 border-sky-500 text-sky-600'
                                : 'bg-white text-gray-500 hover:bg-gray-50'
                            }`}
                        >
                            {index + 1}
                        </button>
                    ))}

                    <button
                        onClick={() => handlePageChange(currentPage + 1)}
                        disabled={currentPage === totalPages}
                        className="relative inline-flex items-center px-3 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:bg-gray-100 disabled:cursor-not-allowed last:rounded-r-xl"
                    >
                        <ChevronRight className="h-5 w-5"/>
                    </button>
                </nav>
            </div>
        </div>
    );
};

export default CollectionList;