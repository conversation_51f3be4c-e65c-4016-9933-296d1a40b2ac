// src/components/MessageFeedback.jsx
import React, {useCallback, useEffect, useRef, useState} from 'react';
import {ThumbsDown, ThumbsUp} from 'lucide-react';

/**
 * MessageFeedback
 * Provides thumbs up/down and optional comment input for feedback.
 * Props:
 *  - onFeedbackSubmit: callback with { type, text }
 */
const MessageFeedback = ({onFeedbackSubmit}) => {
    const [feedbackType, setFeedbackType] = useState(null);
    const [showTextarea, setShowTextarea] = useState(false);
    const [feedbackText, setFeedbackText] = useState('');
    const [showThankYou, setShowThankYou] = useState(false);
    const [hoverThumbsUp, setHoverThumbsUp] = useState(false);
    const [hoverThumbsDown, setHoverThumbsDown] = useState(false);
    const [isSubmitted, setIsSubmitted] = useState(false);

    const textareaRef = useRef(null);
    const containerRef = useRef(null);

    const showSuccessMessage = useCallback(() => {
        setShowThankYou(true);
        setTimeout(() => {
            setShowThankYou(false);
        }, 2000);
    }, []);

    const handleSubmit = useCallback(() => {
        if (!feedbackType) return;

        onFeedbackSubmit({type: feedbackType, text: feedbackText});
        showSuccessMessage();
        setShowTextarea(false);
        setIsSubmitted(true);
    }, [feedbackType, feedbackText, onFeedbackSubmit, showSuccessMessage]);

    // Handle clicks outside the component
    useEffect(() => {
        function handleClickOutside(event) {
            if (containerRef.current &&
                !containerRef.current.contains(event.target) &&
                showTextarea &&
                feedbackType) {
                handleSubmit();
            }
        }

        document.addEventListener("mousedown", handleClickOutside);
        return () => {
            document.removeEventListener("mousedown", handleClickOutside);
        };
    }, [showTextarea, feedbackType, handleSubmit]);

    // Auto-focus textarea when it appears
    useEffect(() => {
        if (showTextarea && textareaRef.current) {
            textareaRef.current.focus();
        }
    }, [showTextarea]);

    const handleThumbClick = (type) => {
        // If already submitted, don't do anything
        if (isSubmitted) return;

        // If clicking the same thumb type that's already selected, just submit
        if (feedbackType === type && !showTextarea) {
            onFeedbackSubmit({type, text: ''});
            showSuccessMessage();
            return;
        }

        // If the other thumb was selected before, submit the previous choice
        // before showing the textarea for the new choice
        if (feedbackType && feedbackType !== type) {
            onFeedbackSubmit({type: feedbackType, text: feedbackText});
            setFeedbackText('');
        }

        setFeedbackType(type);
        setShowTextarea(true);
    };


    const handleCancel = () => {
        setShowTextarea(false);
        setFeedbackText('');
    };

    const handleKeyDown = (e) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            handleSubmit();
        }
        if (e.key === 'Escape') {
            handleCancel();
        }
    };

    return (
        <div ref={containerRef}
             className={`relative flex flex-col gap-1 rounded-lg ${isSubmitted ? 'bg-transparent' : 'bg-white/40 backdrop-blur-md border border-gray-400/65 shadow-sm'} transition-all duration-300 ${isSubmitted ? 'p-0' : 'p-2'} overflow-visible`}>
            {/* Thank you message */}
            {showThankYou && (
                <div
                    className="absolute -top-9 left-1/2 transform -translate-x-1/2 bg-green-50/95 text-green-700 py-2 px-4 rounded-lg text-sm font-medium shadow-md border border-green-200/30 whitespace-nowrap z-10 opacity-100 transition-opacity duration-300">
                    Thank you for your feedback!
                </div>
            )}

            {/* Main feedback interface */}
            <div
                className={`flex items-center justify-end gap-3 mx-2  ${isSubmitted ? '-mt-2' : 'pt-1'} transition-all duration-300`}>
                <p className={`text-gray-700 text-sm font-medium m-0 mr-1 ${isSubmitted ? 'opacity-0 w-0 overflow-hidden' : 'opacity-100'} transition-all duration-300`}>
                    Was this helpful?
                </p>
                <button
                    className={`flex items-center justify-center p-1 rounded-full transition-all duration-200 ${feedbackType === 'like' ? 'bg-transparent' : 'bg-transparent'} ${hoverThumbsUp && !isSubmitted ? 'scale-110' : feedbackType === 'like' ? 'scale-105' : 'scale-100'}`}
                    onClick={() => handleThumbClick('like')}
                    onMouseEnter={() => setHoverThumbsUp(true)}
                    onMouseLeave={() => setHoverThumbsUp(false)}
                    aria-label="Thumbs up"
                >
                    <ThumbsUp
                        size={19}
                        className={feedbackType === 'like' ? 'text-sky-700 fill-sky-300' : 'text-gray-600'}
                        strokeWidth={feedbackType === 'like' ? 2.5 : 2}
                        fill={feedbackType === 'like' ? '#3b82f6' : 'transparent'}
                    />
                </button>
                <button
                    className={`flex items-center justify-center p-1 rounded-full transition-all duration-200 ${feedbackType === 'dislike' ? 'bg-transparent' : 'bg-transparent'} ${hoverThumbsDown && !isSubmitted ? 'scale-110' : feedbackType === 'dislike' ? 'scale-105' : 'scale-100'}`}
                    onClick={() => handleThumbClick('dislike')}
                    onMouseEnter={() => setHoverThumbsDown(true)}
                    onMouseLeave={() => setHoverThumbsDown(false)}
                    aria-label="Thumbs down"
                >
                    <ThumbsDown
                        size={19}
                        className={feedbackType === 'dislike' ? 'text-red-700 fill-red-300' : 'text-gray-600'}
                        strokeWidth={feedbackType === 'dislike' ? 2.5 : 2}
                        fill={feedbackType === 'dislike' ? '#ef4444' : 'transparent'}
                    />
                </button>
            </div>

            {/* Textarea section */}
            <div
                className={`overflow-hidden transition-all duration-300 ease-in-out flex flex-col gap-2 ${showTextarea ? 'max-h-32 opacity-100' : 'max-h-0 opacity-0'}`}>
                <p className="text-xs text-gray-600 m-0 ml-2">
                    {feedbackType === 'like' ? 'What did you find helpful?' : 'How can we improve?'}
                </p>
                <textarea
                    ref={textareaRef}
                    className="w-full p-3 rounded-lg border border-gray-300 resize-none text-sm min-h-20 outline-none transition bg-gray-50/80 focus:border-blue-400  "
                    value={feedbackText}
                    onChange={(e) => setFeedbackText(e.target.value)}
                    onKeyDown={handleKeyDown}
                    placeholder={
                        feedbackType === 'like'
                            ? 'Tell us what was useful (optional)'
                            : 'Tell us how we can do better (optional)'
                    }
                />
            </div>
        </div>
    );
};

export default MessageFeedback;
