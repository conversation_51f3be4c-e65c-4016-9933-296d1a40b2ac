// File: src/components/MethodSelector.jsx

import React, {useEffect, useRef, useState} from 'react';
import {ChevronDown, XCircle} from 'lucide-react';

/**
 * BarIndicator
 * Visualizes method complexity as colored bars.
 * Props:
 *  - level: integer from 1 to 3
 */
const BarIndicator = ({level, className = ''}) => {
    // three bars of increasing height
    return (
        <div className={`flex items-end space-x-0.5 ${className}`} title={`Complexity: ${level}/3`}>
            {[1, 2, 3].map(bar => (
                <div
                    key={bar}
                    className={`w-1 rounded-sm transition-colors ${bar <= level ? 'bg-current' : 'bg-gray-300'}`}
                    style={{height: `${bar * 6}px`}}
                />
            ))}
        </div>
    );
};

/**
 * MethodSelector
 * Custom dropdown for choosing analysis method.
 * Props:
 *  - knowledgeCollections: array, used to disable neural if none
 *  - selectedMethod: string name of current method
 *  - onMethodSelect: callback with method name
 */
const MethodSelector = ({
                            knowledgeCollections = [],
                            selectedMethod,
                            onMethodSelect
                        }) => {
    const [isOpen, setIsOpen] = useState(false);
    const dropdownRef = useRef(null);

    // Close dropdown if clicked outside
    useEffect(() => {
        const handleOutside = e => {
            if (dropdownRef.current && !dropdownRef.current.contains(e.target)) {
                setIsOpen(false);
            }
        };
        document.addEventListener('mousedown', handleOutside);
        return () => document.removeEventListener('mousedown', handleOutside);
    }, []);

    // Define available methods and disabled state
    const methods = [
        {name: 'Keywords', level: 1, levelText: 'Basic', description: 'Keyword matching', disabled: false},
        {name: 'RegEx', level: 2, levelText: 'Medium', description: 'Regular expression matching', disabled: false},
        {
            name: 'Neural', level: 3, levelText: 'Advanced', description: 'AI based method',
            disabled: knowledgeCollections.length === 0
        }
    ];

    const handleSelect = method => {
        if (!method.disabled) {
            onMethodSelect(method.name);
            setIsOpen(false);
        }
    };

    return (
        <div className="relative w-full" ref={dropdownRef}>
            {/* Trigger button */}
            <button
                type="button"
                onClick={() => setIsOpen(prev => !prev)}
                aria-haspopup="listbox"
                aria-expanded={isOpen}
                className="flex justify-between items-center w-full p-3 border border-gray-400 rounded-lg bg-white hover:border-sky-400 transition-colors"
            >
        <span className={selectedMethod ? 'text-gray-700' : 'text-gray-500'}>
          {selectedMethod || 'Select a method'}
        </span>
                <ChevronDown size={18} className={`text-sky-600 transition-transform ${isOpen ? 'rotate-180' : ''}`}/>
            </button>

            {isOpen && (
                <div
                    className="absolute z-10 w-full bg-white border border-gray-300 rounded-xl shadow-lg overflow-auto">
                    <ul role="listbox">
                        {methods.map(method => {
                            const isSelected = selectedMethod === method.name;
                            return (
                                <li
                                    key={method.name}
                                    role="option"
                                    aria-selected={isSelected}
                                    aria-disabled={method.disabled}
                                    tabIndex={method.disabled ? -1 : 0}
                                    onClick={() => handleSelect(method)}
                                    className={`
                    flex flex-col px-4 py-3 cursor-pointer transition-colors
                    ${isSelected ? 'bg-sky-100 text-sky-700' : ''}
                    ${method.disabled ? 'opacity-60 cursor-not-allowed' : 'hover:bg-sky-50'}
                  `}
                                >
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center space-x-3">
                      <span className={`font-medium ${method.disabled ? 'text-gray-500' : 'text-gray-800'}`}>
                        {method.name}
                      </span>
                                            <div className="flex items-center space-x-2">
                                                <BarIndicator level={method.level}
                                                              className={method.disabled ? 'text-gray-300' : 'text-sky-600'}/>
                                                <span
                                                    className={`text-xs ${method.disabled ? 'text-gray-400' : 'text-gray-500'}`}>
                          {method.levelText}
                        </span>
                                            </div>
                                        </div>
                                        {/* Show cross icon if neural is disabled */}
                                        {method.name === 'Neural' && method.disabled && (
                                            <div className="flex items-center text-xs text-gray-400 space-x-1">
                                                <XCircle size={16}/>
                                                <span>Requires collection</span>
                                            </div>
                                        )}
                                    </div>
                                    <p className={`text-xs mt-0.5 ${method.disabled ? 'text-gray-400' : 'text-gray-500'}`}>
                                        {method.description}
                                    </p>
                                </li>
                            );
                        })}
                    </ul>
                </div>
            )}
        </div>
    );
};

export default MethodSelector;