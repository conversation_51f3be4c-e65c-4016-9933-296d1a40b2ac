// File: src/components/AuthGuard.jsx
import React, {useCallback, useEffect, useState} from 'react';
import {Navigate, useLocation, useNavigate} from 'react-router-dom';
import {useUser} from '../context/UserContext';
import {isAuthenticated, isTokenExpired, logoutUser, validateToken} from '../services/api';

export const AuthGuard = ({children}) => {
    const {user, setUser, logout} = useUser();
    const [loading, setLoading] = useState(true);
    const location = useLocation();
    const navigate = useNavigate();

    useEffect(() => {
        const checkAuth = async () => {
            // If we have a token but no user, try to restore from localStorage
            if (isAuthenticated()) {
                if (!user) {
                    const savedUser = localStorage.getItem('jwt_user');
                    if (savedUser) {
                        setUser(JSON.parse(savedUser));
                    }
                }
            }
            setLoading(false);
        };

        checkAuth();
    }, [user, setUser]);

    // Add a logout handler function
    const handleLogout = useCallback(() => {
        logoutUser(); // Clear localStorage
        logout();     // Clear React state
        navigate('/login', {replace: false});
    }, [logout, navigate]);

    // Auto-logout timer to check token expiration periodically
    useEffect(() => {
        if (!user) return; // Skip if not logged in

        const checkIntervalTime = 5 * 60 * 1000; // 5 minutes
        // Check token expiration every minute
        const tokenCheckInterval = setInterval(() => {
            try {
                // Only check if token is expired without refreshing it
                if (isTokenExpired()) {
                    console.log('Session expired - Auto logout triggered');
                    handleLogout();
                }
            } catch (error) {
                console.error('Token check error:', error);
                handleLogout();
            }
        }, checkIntervalTime);

        return () => clearInterval(tokenCheckInterval);
    }, [user, handleLogout]);

    // Expose the logout function to the window object so it can be called from anywhere
    useEffect(() => {
        window.logoutApp = handleLogout;
        return () => {
            delete window.logoutApp;
        };
    }, [handleLogout]);

    if (loading) {
        return <div className="w-full min-h-screen flex items-center justify-center">
            <div className="animate-pulse text-sky-600 text-xl">Loading...</div>
        </div>;
    }

    if (!user || !isAuthenticated() || !validateToken()) {
        // Save the location they tried to go to for a redirect after login
        return <Navigate to="/login" state={{from: location}} replace={false}/>;
    }

    return children;
};

export default AuthGuard;
