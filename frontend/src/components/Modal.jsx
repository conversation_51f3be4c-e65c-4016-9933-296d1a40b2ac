// File: src/components/Modal.jsx

import React, {useCallback, useEffect, useRef} from 'react';
import {X} from 'lucide-react';

/**
 * Modal
 * Renders a centered overlay with backdrop.
 * Props:
 *  - children: modal content
 *  - onClose: callback when clicking outside or on close button
 *  - maxWidth: tailwind max-width utility
 */
const Modal = ({children, onClose, maxWidth = 'max-w-5xl'}) => {
    const modalRef = useRef();

    // Close if click happens outside the modal container
    const handleClickOutside = useCallback(event => {
        if (modalRef.current && !modalRef.current.contains(event.target)) {
            onClose();
        }
    }, [onClose]);

    useEffect(() => {
        document.addEventListener('mousedown', handleClickOutside);
        // Prevent background scroll
        document.body.style.overflow = 'hidden';
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
            document.body.style.overflow = 'auto';
        };
    }, [handleClickOutside]); // Add handleClickOutside to dependencies

    return (
        <div
            className="fixed inset-0 bg-gray-700 bg-opacity-40 flex items-center justify-center z-50 p-4 overflow-y-auto">
            <div
                ref={modalRef}
                className={`bg-white rounded-2xl shadow-lg p-4 w-full border border-gray-300 relative ${maxWidth}`}
            >
                {/* Close button in top right */}
                <button
                    onClick={onClose}
                    aria-label="Close modal"
                    className="absolute top-4 right-4 text-gray-700 hover:text-gray-800 p-1 rounded-full hover:bg-gray-200 transition-colors"
                >
                    <X size={24}/>
                </button>
                <div className="flex flex-col p-4 pt-2.5">
                    {children}
                </div>
            </div>
        </div>
    );
};

export default Modal;