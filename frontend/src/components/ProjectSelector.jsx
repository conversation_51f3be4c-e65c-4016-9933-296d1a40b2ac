// File: src/components/ProjectSelector.jsx

import React from 'react';
import {Folder, FolderOpen} from 'lucide-react';

/**
 * ProjectSelector
 * Displays a list of projects and allows selection/deselection.
 * Props:
 *  - projects: array of project objects
 *  - selectedProject: current project selection
 *  - onProjectSelect: callback when a project is clicked
 *  - loading: boolean for placeholder state
 */
const ProjectSelector = ({
                             projects = [],
                             selectedProject,
                             onProjectSelect,
                             loading = false
                         }) => {

    // Toggle selection: deselect if same, else select new
    const handleProjectClick = (project) => {
        if (selectedProject?.id === project.id) {
            onProjectSelect(null);
        } else {
            onProjectSelect(project);
        }
    };

    return (
        <div className="space-y-4">
            {loading ? (
                // Show placeholders while loading
                <div className="space-y-3">
                    {[1, 2, 3].map(i => (
                        <div key={i} className="h-16 bg-gray-100 rounded-lg animate-pulse"/>
                    ))}
                </div>
            ) : (
                <div className="space-y-2">
                    {projects.length === 0 ? (
                        // No projects message
                        <div
                            className="flex items-center justify-center h-16 rounded-lg bg-sky-50 border border-gray-200">
                            <span className="text-gray-500">No projects available</span>
                        </div>
                    ) : (
                        // List each project as a selectable card
                        projects.map(project => {
                            const isSelected = selectedProject?.id === project.id;
                            return (
                                <div
                                    key={project.id}
                                    role="button"
                                    aria-pressed={isSelected}
                                    onClick={() => handleProjectClick(project)}
                                    onKeyPress={e => {
                                        if (e.key === 'Enter' || e.key === ' ') {
                                            handleProjectClick(project);
                                        }
                                    }}
                                    tabIndex={0}
                                    className={`
                    flex items-center p-3 rounded-lg cursor-pointer
                    transition-all transform
                    ${isSelected
                                        ? 'bg-sky-100 border border-sky-500'
                                        : 'bg-sky-50/90 border border-gray-400/65 shadow-sm hover:bg-sky-100/65'}
                  `}
                                >
                                    {/* Icon switches based on selection */}
                                    <div className="p-2 rounded-lg mr-3 text-sky-700">
                                        {isSelected ? <FolderOpen size={24}/> : <Folder size={24}/>}
                                    </div>
                                    <div className="flex-1 min-w-0">
                                        <h4 className="font-medium text-gray-900 truncate">{project.name}</h4>
                                        <p className="text-sm text-gray-500 truncate">
                                            {project.description || 'No description'}
                                        </p>
                                    </div>
                                </div>
                            );
                        })
                    )}
                </div>
            )}
        </div>
    );
};

export default ProjectSelector;