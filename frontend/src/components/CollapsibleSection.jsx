import React from 'react';
import {Check, ChevronDown, ChevronUp, RotateCcw} from 'lucide-react';

/**
 * CollapsibleSection Component
 *
 * A section that can be collapsed/expanded with visual state indicators.
 * Shows a checkmark and summary when collapsed with selected content.
 *
 * @param {boolean} isCollapsed - Current collapsed state
 * @param {Function} toggleCollapse - Function to toggle collapsed state
 * @param {string} selectedItem - Summary text displayed when collapsed
 * @param {ReactNode} infoSection - InfoSection component to display
 * @param {boolean} showCollapseButton - Whether to show collapse button when expanded
 * @param {string} collapseButtonLabel - Label for the collapse button (default: "Done")
 * @param {Function} onReset - Optional reset function to show reset button
 * @param {ReactNode} children - Content to show when expanded
 */
const CollapsibleSection = ({
                                isCollapsed,
                                toggleCollapse,
                                selectedItem,
                                infoSection,
                                showCollapseButton = false,
                                collapseButtonLabel = "Done",
                                onReset,
                                children,
                            }) => {
    const isDone = isCollapsed && selectedItem;

    return (
        <section className="bg-gray-50 rounded-xl border border-gray-400/60 transition-shadow shadow-sm mb-2">
            <div className="p-2">
                <div className="flex justify-between items-center px-2">
                    {/* Collapsed summary with checkmark */}
                    {isDone ? (
                        <div className="flex items-center">
                            <div className="mr-3 flex-shrink-0">
                                <div className="text-green-600 p-1.5 rounded-full bg-white border border-gray-400">
                                    <Check size={22} className='stroke-[2.5]'/>
                                </div>
                            </div>
                            <div>
                                <h3 className="text-lg font-medium text-gray-600 flex items-center">
                                    {infoSection.props.title}
                                </h3>
                                <p className="text-sm text-gray-600">
                                    Selected: <span className="font-medium">{selectedItem}</span>
                                </p>
                            </div>
                        </div>
                    ) : (
                        infoSection
                    )}

                    {/* Collapse/Expand icon */}
                    <button
                        onClick={toggleCollapse}
                        className="text-gray-600 hover:bg-gray-100 p-2 rounded-full transition-colors "
                        aria-label={isCollapsed ? "Expand section" : "Collapse section"}
                        type="button"
                    >
                        {isCollapsed ? <ChevronDown size={20}/> : <ChevronUp size={20}/>}
                    </button>
                </div>

                {/* Section content */}
                {!isCollapsed && (
                    <div className="p-3 pt-0">
                        {children}
                        {showCollapseButton && (
                            <div className="flex justify-between mt-4">
                                {/* Reset button (left side) */}
                                {onReset && (
                                    <button
                                        onClick={onReset}
                                        className="inline-flex items-center px-4 py-2 bg-white border border-gray-400 rounded-lg shadow-sm text-gray-600 font-medium hover:bg-red-50 hover:border-red-400 hover:text-red-700 transition-colors focus:outline-none focus:ring-2 focus:ring-red-200"
                                        type="button"
                                    >
                                        <RotateCcw size={16} className="mr-1.5"/>
                                        Reset to Defaults
                                    </button>
                                )}

                                {/* Save button (right side) */}
                                <button
                                    onClick={toggleCollapse}
                                    className="inline-flex items-center px-4 py-2 bg-white border border-gray-500 rounded-lg shadow-sm text-gray-700 font-semibold hover:bg-green-50 transition-colors focus:outline-none focus:ring-2 focus:ring-green-200 ml-auto"
                                    type="button"
                                >
                                    <Check size={18} className="mr-1.5 text-green-700 stroke-[2.5]"/>
                                    {collapseButtonLabel}
                                </button>
                            </div>
                        )}
                    </div>
                )}
            </div>
        </section>
    );
};

export default CollapsibleSection;