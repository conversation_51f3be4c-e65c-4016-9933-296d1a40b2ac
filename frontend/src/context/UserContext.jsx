// File: src/context/UserContext.jsx
import React, {createContext, useContext, useState} from 'react';

// Create context
const UserContext = createContext(null);

// Hook for consuming the user context
export const useUser = () => useContext(UserContext);

export const UserProvider = ({children}) => {
    // Initialize user state from localStorage if available
    const [user, setUser] = useState(() => {
        const savedUser = localStorage.getItem('jwt_user');
        return savedUser ? JSON.parse(savedUser) : null;
    });

    // Optionally, add a logout function for future use
    const logout = () => setUser(null);

    return (
        <UserContext.Provider value={{user, setUser, logout}}>
            {children}
        </UserContext.Provider>
    );
};