// File: src/pages/KnowledgeCollectionView.jsx
import React, {useEffect, useState} from 'react';
import {useLocation, useNavigate, useParams} from 'react-router-dom';
import api from '../services/api';
import KnutGPTLogo from '../assets/knutgpt-logo.svg';
import {BarChart, Check, Edit, FileText, Grid, List, Search, X} from 'lucide-react';

const KnowledgeCollectionView = () => {
    const {id} = useParams();
    const navigate = useNavigate();
    const location = useLocation();
    const isEditModeInitial = location.state?.isEditMode || false;

    // State for collection data fetched from API
    const [collection, setCollection] = useState({});
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    const [isEditMode, setIsEditMode] = useState(isEditModeInitial);
    const [formData, setFormData] = useState({});
    const [activeTab, setActiveTab] = useState('collectionInfo');
    const [searchQuery, setSearchQuery] = useState('');

    // Fetch collection data from API
    useEffect(() => {
        const fetchCollection = async () => {
            if (id === 'new') {
                setIsEditMode(true);
                setFormData({
                    name: '',
                    description: '',
                    owner: '',
                    privacy: 'Private',
                    createdDate: new Date().toLocaleDateString(),
                    size: '0 KB',
                    writePermissions: ''
                });
                setCollection({});
                setLoading(false);
                return;
            }

            try {
                setLoading(true);
                setError(null);
                const collectionData = await api.getCollection(parseInt(id));
                setCollection(collectionData);
                setFormData({...collectionData});
            } catch (err) {
                console.error('Error fetching collection:', err);
                setError(err.message || 'Failed to fetch collection data');
            } finally {
                setLoading(false);
            }
        };

        fetchCollection();
    }, [id]);

    useEffect(() => {
        // Update formData when collection data changes (only for existing collections)
        if (id !== 'new' && collection && Object.keys(collection).length > 0) {
            setFormData({...collection});
        }
    }, [collection, id]);

    const handleInputChange = (e) => {
        const {name, value} = e.target;
        setFormData({...formData, [name]: value});
    };

    const handleSave = () => {
        console.log('Saving changes...', formData);
        setIsEditMode(false);
    };

    const handleCancel = () => {
        if (id === 'new') {
            navigate('/knowledge-collection');
        } else {
            setFormData({...collection});
            setIsEditMode(false);
        }
    };

    // Redesigned Collection Info using a grid layout
    const CollectionInfoTab = () => (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {[
                {label: 'Status', value: 'Active'},
                {label: 'Optimizer Status', value: 'Running'},
                {label: 'Indexed Vectors Count', value: '12345'},
                {label: 'Points Count', value: '6789'},
                {label: 'Segments Count', value: '10'},
                {label: 'Config', value: 'Default Config'},
                {label: 'Payload Schema', value: 'Schema Details...'}
            ].map((item, idx) => (
                <div key={idx} className="p-4 border rounded-lg bg-white shadow-sm">
                    <label className="block text-gray-700 font-medium mb-1">{item.label}</label>
                    <p className="text-gray-600">{item.value}</p>
                </div>
            ))}
        </div>
    );

    const PointsTab = () => (
        <div className="space-y-3">
            <div className="p-4 border rounded-lg hover:shadow-md cursor-pointer border-gray-200 transition-shadow">
                <h3 className="font-semibold text-gray-800">Point Item 1</h3>
                <p className="text-sm text-gray-500">Details about point item 1...</p>
            </div>
            <div className="p-4 border rounded-lg hover:shadow-md cursor-pointer border-gray-200 transition-shadow">
                <h3 className="font-semibold text-gray-800">Point Item 2</h3>
                <p className="text-sm text-gray-500">Details about point item 2...</p>
            </div>
        </div>
    );

    const SnapshotsTab = () => (
        <div className="py-10 text-center text-lg text-gray-600">
            Snapshots Placeholder
        </div>
    );

    const VisualizeTab = () => (
        <div className="py-10 text-center text-lg text-gray-600">
            Visualize Placeholder
        </div>
    );

    return (
        <div className="py-4 pb-2 bg-gray-50 min-h-screen">
            <div className="max-w-6xl mx-auto px-4 pt-4">
                <div className="bg-white rounded-3xl shadow-lg p-8 border border-gray-200">
                    {/* Loading State */}
                    {loading && (
                        <div className="flex items-center justify-center py-12">
                            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-sky-500"></div>
                            <span className="ml-3 text-gray-600">Loading collection data...</span>
                        </div>
                    )}

                    {/* Error State */}
                    {error && !loading && (
                        <div className="flex items-center justify-center py-12">
                            <div className="text-center">
                                <div className="text-red-500 text-xl mb-2">⚠️</div>
                                <p className="text-red-600 font-medium">Error loading collection</p>
                                <p className="text-gray-500 text-sm mt-1">{error}</p>
                                <button
                                    onClick={() => window.location.reload()}
                                    className="mt-4 px-4 py-2 bg-sky-500 text-white rounded-lg hover:bg-sky-600 transition-colors"
                                >
                                    Retry
                                </button>
                            </div>
                        </div>
                    )}

                    {/* Main Content - only show when not loading and no error */}
                    {!loading && !error && (
                        <>
                            {/* Header Section */}
                            <div className="pb-4 flex flex-col md:flex-row md:items-center md:justify-between">
                                <div className="flex items-center space-x-4">
                                    <div className="relative">
                                        <img
                                            src={KnutGPTLogo}
                                            alt="KnutGPT Logo"
                                            className="w-16 h-16"
                                        />
                                        <div className="absolute -top-2 -right-2">
                                            {isEditMode ? (
                                                <span
                                                    className="flex items-center justify-center w-6 h-6 bg-amber-500 text-white rounded-full shadow-md">
                      <Edit className="w-3 h-3"/>
                    </span>
                                            ) : (
                                                <span
                                                    className="flex items-center justify-center w-6 h-6 bg-green-500 text-white rounded-full shadow-md">
                      <Check className="w-3 h-3"/>
                    </span>
                                            )}
                                        </div>
                                    </div>
                                    <div>
                                        <div className="flex flex-col sm:flex-row sm:items-center">
                                            <h1 className="text-2xl font-bold text-gray-700">
                                                KnutGPT
                                            </h1>
                                            {isEditMode ? (
                                                <input
                                                    type="text"
                                                    name="name"
                                                    value={formData.name}
                                                    onChange={handleInputChange}
                                                    className="ml-0 sm:ml-4 mt-2 sm:mt-0 text-lg text-gray-700 border border-gray-300 rounded-md px-2 py-1 focus:outline-none focus:ring-2 focus:ring-sky-500"
                                                    aria-label="Collection name edit"
                                                />
                                            ) : (
                                                <span className="ml-2 text-lg text-gray-500">
                      / {id === 'new' ? 'New Collection' : collection.name}
                    </span>
                                            )}
                                        </div>
                                        <p className="text-sm text-gray-500 mt-1">Knowledge Collection Details</p>
                                    </div>
                                </div>
                                <div className="mt-4 md:mt-0">
                                    {/* Redesigned badge as a rounded square with two text lines */}
                                    <div
                                        className={`${isEditMode ? 'bg-amber-500' : 'bg-green-500'} rounded-lg px-3 py-2 text-white text-center`}>
                <span className="block text-sm font-semibold leading-tight">
                  {isEditMode ? 'Editing' : 'Viewing'}
                </span>
                                        <span className="block text-sm font-semibold leading-tight">
                  Mode
                </span>
                                    </div>
                                </div>
                            </div>

                            {/* Search Bar */}
                            <div className="border-b border-gray-200 px-6 py-3">
                                <div className="relative">
                                    <div
                                        className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <Search className="h-5 w-5 text-gray-400"/>
                                    </div>
                                    <input
                                        type="text"
                                        className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-sky-500 focus:border-sky-500 sm:text-sm"
                                        placeholder="Search points..."
                                        value={searchQuery}
                                        onChange={(e) => setSearchQuery(e.target.value)}
                                        aria-label="Search points"
                                    />
                                </div>
                            </div>

                            {/* Tabs Navigation */}
                            <div className="px-6 pt-4 border-b border-gray-200">
                                <nav role="tablist" className="flex w-full">
                                    <button
                                        role="tab"
                                        aria-selected={activeTab === 'collectionInfo'}
                                        className={`flex-1 text-center pb-4 px-2 border-b-2 font-medium text-sm focus:outline-none ${
                                            activeTab === 'collectionInfo'
                                                ? 'border-sky-500 text-sky-600'
                                                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                        }`}
                                        onClick={() => setActiveTab('collectionInfo')}
                                    >
                                        <List className="w-4 h-4 inline mr-1"/> Collection Info
                                    </button>
                                    <button
                                        role="tab"
                                        aria-selected={activeTab === 'points'}
                                        className={`flex-1 text-center pb-4 px-2 border-b-2 font-medium text-sm focus:outline-none ${
                                            activeTab === 'points'
                                                ? 'border-sky-500 text-sky-600'
                                                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                        }`}
                                        onClick={() => setActiveTab('points')}
                                    >
                                        <FileText className="w-4 h-4 inline mr-1"/> Points
                                    </button>
                                    <button
                                        role="tab"
                                        aria-selected={activeTab === 'snapshots'}
                                        className={`flex-1 text-center pb-4 px-2 border-b-2 font-medium text-sm focus:outline-none ${
                                            activeTab === 'snapshots'
                                                ? 'border-sky-500 text-sky-600'
                                                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                        }`}
                                        onClick={() => setActiveTab('snapshots')}
                                    >
                                        <Grid className="w-4 h-4 inline mr-1"/> Snapshots
                                    </button>
                                    <button
                                        role="tab"
                                        aria-selected={activeTab === 'visualize'}
                                        className={`flex-1 text-center pb-4 px-2 border-b-2 font-medium text-sm focus:outline-none ${
                                            activeTab === 'visualize'
                                                ? 'border-sky-500 text-sky-600'
                                                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                        }`}
                                        onClick={() => setActiveTab('visualize')}
                                    >
                                        <BarChart className="w-4 h-4 inline mr-1"/> Visualize
                                    </button>
                                </nav>
                            </div>

                            {/* Tab Content - directly attached to the menu */}
                            <div className="p-6 border border-gray-200 rounded-md bg-gray-50 shadow-sm">
                                {activeTab === 'collectionInfo' && <CollectionInfoTab/>}
                                {activeTab === 'points' && <PointsTab/>}
                                {activeTab === 'snapshots' && <SnapshotsTab/>}
                                {activeTab === 'visualize' && <VisualizeTab/>}
                            </div>

                            {/* Footer with action buttons */}
                            <div className="flex justify-end space-x-4 mt-6">
                                {isEditMode ? (
                                    <>
                                        <button
                                            onClick={handleCancel}
                                            className="py-2 px-4 border border-gray-300 text-gray-600 rounded-xl hover:bg-gray-50 transition-colors font-medium flex items-center"
                                        >
                                            <X className="w-4 h-4 mr-2"/>
                                            Cancel
                                        </button>
                                        <button
                                            onClick={handleSave}
                                            className="py-2 px-4 bg-green-500 text-white rounded-xl hover:bg-green-600 transition-colors font-medium flex items-center"
                                        >
                                            <Check className="w-4 h-4 mr-2"/>
                                            Save
                                        </button>
                                    </>
                                ) : (
                                    <button
                                        onClick={() => setIsEditMode(true)}
                                        className="py-2 px-6 bg-sky-500 text-white rounded-xl hover:bg-sky-600 transition-colors font-semibold flex items-center"
                                    >
                                        <Edit className="w-4 h-4 mr-2"/>
                                        Modify
                                    </button>
                                )}
                            </div>
                        </>
                    )}
                </div>
            </div>
        </div>
    );
};

export default KnowledgeCollectionView;