// File: src/pages/AboutKnutGPTPage.jsx
import React from 'react';
import {useNavigate} from 'react-router-dom';
import {ArrowLeft, CheckCircle, Search, Settings, Shield, Target, Users, Wrench, Zap} from 'lucide-react';
import knutgptLogo from '../assets/knutgpt-logo.svg';
import logAnalysisLayers from '../assets/log_analysis_layers.png';

const AboutKnutGPTPage = () => {
    const navigate = useNavigate();

    const features = [
        {
            icon: <Search className="w-6 h-6 text-sky-600"/>,
            title: "Log Analysis",
            description: "Analyzes log files from various sources with AI-powered precision"
        },
        {
            icon: <Target className="w-6 h-6 text-sky-600"/>,
            title: "Anomaly Detection",
            description: "Detects anomalies and error patterns in real-time"
        },
        {
            icon: <Zap className="w-6 h-6 text-sky-600"/>,
            title: "Root Cause Analysis",
            description: "Locates error root causes for faster debugging and resolution"
        },
        {
            icon: <CheckCircle className="w-6 h-6 text-sky-600"/>,
            title: "Smart Solutions",
            description: "Explains issues and suggests effective, actionable solutions"
        }
    ];

    const userTypes = [
        {
            icon: <Users className="w-5 h-5 text-amber-500"/>,
            title: "Testers",
            description: "Gain insights without needing a full developer setup"
        },
        {
            icon: <Wrench className="w-5 h-5 text-amber-500"/>,
            title: "Developers",
            description: "Quickly start local builds and receive continuous feedback"
        },
        {
            icon: <Settings className="w-5 h-5 text-amber-500"/>,
            title: "Project Managers",
            description: "Understand and summarize comprehensive logs effortlessly"
        },
        {
            icon: <Shield className="w-5 h-5 text-amber-500"/>,
            title: "Quality Engineers",
            description: "Identify, triage, and resolve anomalies effectively"
        }
    ];

    return (
        <div className="min-h-screen bg-gray-50">
            {/* Fixed Header */}
            <div className="">
                <div className="max-w-6xl mx-auto px-4 py-6">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                            <img src={knutgptLogo} alt="KnutGPT Logo" className="w-12 h-12"/>
                            <div>
                                <h1 className="text-2xl font-bold text-gray-700">About KnutGPT</h1>
                                <p className="text-gray-500">AI Powered Log Analyzer</p>
                            </div>
                        </div>
                        <button
                            onClick={() => navigate('/')}
                            className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors flex items-center space-x-2"
                        >
                            <ArrowLeft className="w-4 h-4"/>
                            <span>Return to Home Page</span>
                        </button>
                    </div>
                </div>
            </div>

            {/* Main Content */}
            <div className="max-w-7xl mx-auto px-4 py-8 space-y-8">

                {/* Introduction Card */}
                <div className=" p-8 ">
                    <div className="text-center space-y-4">
            <span className="inline-block text-amber-500 font-semibold text-2xl">
              Welcome to KnutGPT
            </span>
                        <h2 className="text-3xl font-bold text-gray-700">
                            Your AI Assistant in Log Analysis
                        </h2>
                        <p className="text-gray-600 text-lg text-left">
                            KnutGPT is an AI powered engineering assistant that increases software development
                            efficiency.
                            It analyzes log files, detects anomalies and error patterns, locates error root causes,
                            and suggests actionable solutions.
                        </p>
                    </div>
                </div>

                {/* Two Column Layout */}
                <div className="grid grid-cols-1 lg:grid-cols-5 gap-2">
                    {/* Left Column - Introduction and Features */}
                    <div className="space-y-6 col-span-2">
                        {/* Features Grid */}
                        <div className="grid grid-cols-1 gap-4">
                            {features.map((feature, index) => (
                                <div key={index}
                                     className="bg-white rounded-2xl shadow-lg p-4 border border-gray-200 hover:shadow-xl transition-all duration-300 hover:scale-105">
                                    <div className="flex items-start space-x-4">
                                        <div className="p-1">
                                            {feature.icon}
                                        </div>
                                        <div className="flex-1">
                                            <h3 className="text-lg font-semibold text-gray-700 mb-0.5">{feature.title}</h3>
                                            <p className="text-gray-600">{feature.description}</p>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                    {/* Right Column - Combined Visual and Superpower Card */}
                    <div className="col-span-3 ">
                        {/* Visual Section */}
                        <div className="flex justify-center">
                            <img
                                src={logAnalysisLayers}
                                alt="Log analysis layers diagram"
                                className="max-w-full h-auto"
                            />
                        </div>
                    </div>
                </div>

                {/* Superpower Section */}
                <div
                    className="bg-gradient-to-br from-amber-50 to-orange-50 rounded-2xl p-6 border border-amber-500/30">
                    <div className="text-center space-y-4">
                        <div className="flex justify-center">
                            <div className="">
                                <Zap className="w-8 h-8 text-amber-500"/>
                            </div>
                        </div>
                        <h3 className="text-2xl font-bold text-gray-700">
                            A New Superpower
                        </h3>
                        <p className="text-gray-600 text-left">
                            With KnutGPT, you gain an extraordinary ability to filter out the noise of irrelevant data.
                            It transforms complex logs into clear, actionable insights, making it easier to pinpoint
                            issues
                            and start resolving them immediately.
                        </p>
                    </div>
                </div>

                {/* User Types Card */}
                <div className="bg-white rounded-3xl shadow-lg p-8 border border-gray-200">
                    <div className="text-center mb-8">
                        <div className="flex justify-center mb-4">
                            <div className="">
                                <Users className="w-8 h-8 text-amber-500"/>
                            </div>
                        </div>
                        <h3 className="text-2xl font-bold text-gray-700">
                            For Everyone
                        </h3>
                        <p className="text-gray-600 mt-2">
                            KnutGPT is designed to help professionals across all roles
                        </p>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        {userTypes.map((userType, index) => (
                            <div key={index}
                                 className="flex items-start space-x-4 p-4 bg-amber-50 rounded-xl hover:bg-amber-100 transition-colors">
                                <div className="p-1">
                                    {userType.icon}
                                </div>
                                <div className="flex-1">
                                    <h4 className="font-semibold text-gray-700 text-lg">{userType.title}</h4>
                                    <p className="text-gray-600 mt-1">{userType.description}</p>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>

                {/* Call to Action Card */}
                <div className=" p-6">
                    <div className="text-center space-y-6">
                        <h3 className="text-2xl font-bold text-gray-700">
                            Ready to Get Started?
                        </h3>
                        <p className="text-gray-600 text-lg">
                            Experience the power of AI-driven log analysis today
                        </p>
                        <div className="flex justify-center space-x-4">
                            <button
                                onClick={() => navigate('/analysis')}
                                className="px-8 py-4 bg-sky-600 text-white font-semibold text-lg rounded-xl hover:bg-sky-700 transition-all duration-300 transform hover:scale-105 flex items-center space-x-2 shadow-lg"
                            >
                                <Search className="w-6 h-6"/>
                                <span>Try Log Analysis</span>
                            </button>
                            <button
                                onClick={() => navigate('/train')}
                                className="px-8 py-4 bg-gray-600 text-white font-semibold text-lg rounded-xl hover:bg-gray-700 transition-all duration-300 transform hover:scale-105 flex items-center space-x-2 shadow-lg"
                            >
                                <Settings className="w-6 h-6"/>
                                <span>Train KnutGPT</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default AboutKnutGPTPage;