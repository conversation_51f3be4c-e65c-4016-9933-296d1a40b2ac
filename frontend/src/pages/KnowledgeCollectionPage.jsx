// src/pages/KnowledgeCollectionPage.jsx

import React from 'react';
import CollectionList from '../components/CollectionList';
import knutgptLogo from '../assets/knutgpt-logo.svg';
import {Settings} from 'lucide-react';

const KnowledgeCollectionPage = () => {
    return (
        <div className="py-4">
            <div className="max-w-3xl mx-auto pt-2">
                {/* Main Content Card */}
                <div className="bg-white rounded-3xl shadow-md p-8 border border-gray-200">
                    {/* New Header Design */}
                    <div className="pb-4">
                        {/* Header content */}
                        <div className="flex items-start space-x-4">
                            {/* Logo */}
                            <img
                                src={knutgptLogo}
                                alt="LogAnalyzer Logo"
                                className="w-16 h-16"
                            />
                            {/* Title and subtitle */}
                            <div className="flex-1 pt-3">
                                <h1 className="text-xl font-bold text-gray-700 leading-none">
                                    KnutGPT
                                </h1>
                                <p className="text-xl text-gray-500">
                                    Manage knowledge collections
                                </p>
                            </div>
                        </div>
                        {/* Info Section */}
                        <div className="bg-sky-100/60 border border-gray-400/40 p-4 rounded-xl mb-5 mt-3">
                            <div className="flex items-center space-x-2">
                                <Settings className="w-5 h-5 text-gray-600"/>
                                <h2 className="text-lg font-medium text-gray-700">Knowledge Collections</h2>
                            </div>
                            <p className="text-gray-600 mt-1 ">
                                A knowledge collection is where the log lines of past training sessions are stored. This
                                is how KnutGPT recognizes log lines from the past.
                                Typically each project and source code branch has its unique dataset, to avoid false
                                assumptions.
                            </p>
                        </div>
                        {/* Top border accent */}
                        <div
                            className="h-[3px] w-full bg-gradient-to-r from-gray-400/80 to-gray-400/80 rounded-full my-3 opacity-40"></div>
                    </div>

                    <div className="space-y-6 pb-8">
                        <CollectionList/>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default KnowledgeCollectionPage;