// File: src/pages/LoginPage.jsx
import React, {useState} from 'react';
import {CheckCircle, Eye, EyeOff} from 'lucide-react';
import {useNavigate} from 'react-router-dom';
import {AnimatePresence, motion} from 'framer-motion';
import knutgptLogo from '../assets/knutgpt-logo.svg';
import {useUser} from '../context/UserContext';
import {loginUser} from '../services/api';

const LoginPage = () => {
    const [showPassword, setShowPassword] = useState(false);
    const [form, setForm] = useState({username: '', password: ''});
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');
    const [success, setSuccess] = useState(false);
    const navigate = useNavigate();
    const {setUser} = useUser();

    const handleLogin = async (e) => {
        e.preventDefault();
        setLoading(true);
        setSuccess(false);
        setError('');
        try {
            const user = await loginUser(form.username, form.password);
            setUser(user);

            // Show success UI
            setSuccess(true);
            setLoading(false);

            // After a delay, redirect to home
            setTimeout(() => {
                navigate('/', {replace: false});
            }, 1200);
        } catch (err) {
            setError(err.message || 'Incorrect username or password');
            setLoading(false);
        }
    };

    return (
        <div className="w-full min-h-screen flex items-center justify-center">
            <div
                className="bg-white rounded-3xl w-full max-w-4xl flex overflow-hidden shadow-xl border border-gray-200">
                {/* Left side with KnutGPT logo */}
                <div
                    className="w-1/2 bg-gradient-to-br from-gray-200/80 to-slate-200/80 p-8 flex items-center justify-center border-r border-gray-300">
                    <img src={knutgptLogo} alt="KnutGPT Logo" className="w-72 h-72"/>
                </div>

                {/* Right side with login form */}
                <div className="w-1/2 p-10 flex flex-col justify-center">
                    <div className="mb-6">
                        <h1 className="text-3xl font-bold mb-1.5 text-gray-700">Welcome to KnutGPT</h1>
                        <p className="text-gray-600 ml-0.5 text-lg">Please enter your login details</p>
                    </div>

                    <form className="space-y-6 relative" onSubmit={handleLogin} autoComplete="off">
                        <div>
                            <input
                                type="text"
                                autoComplete="username"
                                placeholder="Email"
                                className="w-full px-4 py-3 border-b border-gray-300 focus:border-sky-600 outline-none transition-colors"
                                value={form.username}
                                onChange={e => setForm(f => ({...f, username: e.target.value}))}
                                disabled={loading || success}
                                required
                            />
                        </div>
                        <div className="relative">
                            <input
                                type={showPassword ? "text" : "password"}
                                autoComplete="current-password"
                                placeholder="Password"
                                className="w-full px-4 py-3 border-b border-gray-300 focus:border-sky-600 outline-none transition-colors pr-12"
                                value={form.password}
                                onChange={e => setForm(f => ({...f, password: e.target.value}))}
                                disabled={loading || success}
                                required
                            />
                            <button
                                type="button"
                                onClick={() => setShowPassword(p => !p)}
                                className="absolute right-4 top-1/2 -translate-y-1/2 text-gray-500"
                                tabIndex={-1}
                                aria-label={showPassword ? "Hide password" : "Show password"}
                            >
                                {showPassword ? <EyeOff size={20}/> : <Eye size={20}/>}
                            </button>
                        </div>

                        {/* Error Message */}
                        <AnimatePresence>
                            {error && !success && (
                                <motion.div
                                    className="text-red-600 font-medium text-center mt-3"
                                    initial={{opacity: 0, y: 12}}
                                    animate={{opacity: 1, y: 0}}
                                    exit={{opacity: 0, y: 12}}
                                    role="alert"
                                >
                                    {error}
                                </motion.div>
                            )}
                        </AnimatePresence>

                        <div className="flex flex-col gap-4 relative">
                            <button
                                type="submit"
                                className={`w-full py-3 rounded-lg transition-colors font-semibold 
                  ${
                                    loading || success
                                        ? 'bg-sky-400 text-white cursor-not-allowed'
                                        : 'bg-sky-600 text-white hover:bg-sky-700'
                                }
                `}
                                disabled={loading || success}
                            >
                                {loading ? 'Logging in...' : (success ? 'Success!' : 'Log in with Account')}
                            </button>
                            <button
                                type="button"
                                onClick={handleLogin}
                                className="w-full bg-gray-200 border border-gray-300 py-3 rounded-lg flex items-center justify-center space-x-2 hover:bg-gray-300 transition-colors"
                            >
                                <span>Log in with Bosch SSO</span>
                            </button>

                            {/* Success Animation - Button Section Overlay */}
                            <AnimatePresence>
                                {success && (
                                    <motion.div
                                        className="absolute inset-0 flex flex-col items-center justify-center bg-white backdrop-blur-md rounded-lg z-10 "
                                        initial={{opacity: 0, scale: 0.95, y: 10}}
                                        animate={{opacity: 1, scale: 1, y: 0}}
                                        exit={{opacity: 0, scale: 0.95, y: 10}}
                                        transition={{duration: 0.4, type: "spring"}}
                                        role="status"
                                        aria-live="polite"
                                    >
                                        <motion.div
                                            initial={{scale: 0}}
                                            animate={{scale: 1.15}}
                                            transition={{type: "spring", stiffness: 200, damping: 12, delay: 0.02}}
                                        >
                                            <CheckCircle className="text-green-500" size={40}/>
                                        </motion.div>
                                        <div className="text-green-600 mt-2 text-lg font-semibold">
                                            Login successful!
                                        </div>
                                        <motion.div
                                            initial={{opacity: 0, y: 10}}
                                            animate={{opacity: 1, y: 0}}
                                            transition={{delay: 0.35}}
                                            className="text-gray-500 text-sm mt-1"
                                        >
                                            Redirecting...
                                        </motion.div>
                                    </motion.div>
                                )}
                            </AnimatePresence>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    );
};

export default LoginPage;
