// File: src/pages/JobPage.jsx
import React, {useCallback, useEffect, useRef, useState} from 'react';
import {useLocation, useNavigate, useParams} from 'react-router-dom';
import {AlertCircle, CheckCircle, Download, ExternalLink, FileCode, FileText, FileX, Undo2, Upload} from 'lucide-react';
import LoadingComponent from '../components/LoadingComponent';
import SessionTooltip from '../components/SessionTooltip';
import CustomChatBubble from '../components/CustomChatBubble';
import knutgptLogo from '../assets/knutgpt-logo.svg';
import Modal from '../components/Modal';

// Use only the real API service
import api, {uploadJobFile} from '../services/api';

const safe = (val, fallback = '') =>
    val || fallback || <span className="text-gray-300">—</span>;

const JobPage = () => {
    const {id} = useParams();
    const navigate = useNavigate();
    const location = useLocation();

    const [session, setSession] = useState(location.state?.job || null);
    const [loading, setLoading] = useState(!session);
    const [error, setError] = useState('');

    // Backend-driven session status
    const [sessionStatus, setSessionStatus] = useState({
        status: 'uploading',
        percentage: 0,
        message: 'Initializing...',
        resultHTML: null,
        resultSummary: null
    });

    // For result summaries
    const [summaryText, setSummaryText] = useState('');

    // Modal states
    const [showPreviewModal, setShowPreviewModal] = useState(false);
    const [previewFileName, setPreviewFileName] = useState('');
    const [fileContent, setFileContent] = useState('');
    const [isLoadingPreview, setIsLoadingPreview] = useState(false);

    const statusPollingRef = useRef(null);

    // Upload state management
    const fileToUpload = location.state?.fileToUpload;
    const [uploadStatus, setUploadStatus] = useState('pending'); // pending, uploading, completed, failed
    const [uploadProgress, setUploadProgress] = useState(0);
    const [uploadError, setUploadError] = useState('');
    const [retryCount, setRetryCount] = useState(0);
    const maxRetries = 3;

    // Debug logs
    console.log('JobPage - location.state:', location.state);
    console.log('JobPage - session object:', session);
    console.log('JobPage - sessionStatus:', sessionStatus);

    // Load job data on mount
    useEffect(() => {
        let ignore = false;

        async function fetchJob() {
            setLoading(true);
            try {
                const fetched = await api.getJob(id);
                if (!ignore && fetched) {
                    setSession(fetched);
                    setLoading(false);
                }
            } catch (err) {
                if (!ignore) {
                    setError('Could not load job details.');
                    setLoading(false);
                }
            }
        }

        if (!session && id) {
            fetchJob();
        } else if (session) {
            setLoading(false);
        }

        return () => {
            ignore = true;
        };
    }, [id, session]);

    // Start polling job status when job is available
    useEffect(() => {
        if (!session || !id) return;

        const initializeJobStatus = async () => {
            // Only start polling if the job is not already completed
            if (session.SessionStatus === 'Complete' || session.status === 'completed') {
                // Job is already completed, set final status
                setSessionStatus({
                    status: 'completed',
                    percentage: 100,
                    message: 'Job completed successfully',
                    resultHTML: session.resultHTML,
                    resultSummary: session.resultSummary || null
                });

                // Load summary for completed jobs
                if (!summaryText) {
                    // First try to get job-specific summary from status
                    try {
                        const statusResponse = await api.getJobStatus(id);
                        if (statusResponse && statusResponse.resultSummary) {
                            setSummaryText(statusResponse.resultSummary);
                        } else {
                            // Fallback to generic summary
                            const summaryType = session.sessionType === 'train' ? 'train' : 'analysis';
                            const summaryResponse = await api.getSummaryMessage(summaryType);
                            if (summaryResponse && summaryResponse.result) {
                                setSummaryText(summaryResponse.result);
                            } else {
                                setSummaryText('No summary available for this job.');
                            }
                        }
                    } catch (error) {
                        console.error('Error fetching summary:', error);
                        setSummaryText('No summary available for this job.');
                    }
                }

                return;
            }

            const pollJobStatus = async () => {
                // Don't poll if upload failed - no point in checking status
                if (uploadStatus === 'failed') {
                    if (statusPollingRef.current) {
                        clearInterval(statusPollingRef.current);
                        statusPollingRef.current = null;
                    }
                    return;
                }

                // Don't poll if file needs to be uploaded first
                if (fileToUpload && uploadStatus !== 'completed') {
                    return;
                }

                try {
                    const statusResponse = await api.getJobStatus(id);
                    console.log('Job status response:', statusResponse); // Debug log

                    setSessionStatus(statusResponse);

                    // If completed or failed, stop polling and fetch summary if needed
                    if (statusResponse.status === 'completed' || statusResponse.status === 'failed') {
                        if (statusPollingRef.current) {
                            clearInterval(statusPollingRef.current);
                            statusPollingRef.current = null;
                        }

                        // Use the resultSummary from the status response
                        if (statusResponse.status === 'completed') {
                            if (statusResponse.resultSummary) {
                                setSummaryText(statusResponse.resultSummary);
                            } else {
                                // Fallback: try to get generic summary if no job-specific summary
                                const summaryType = session.sessionType === 'train' ? 'train' : 'analysis';
                                try {
                                    const summaryResponse = await api.getSummaryMessage(summaryType);
                                    if (summaryResponse && summaryResponse.result) {
                                        setSummaryText(summaryResponse.result);
                                    } else {
                                        setSummaryText('No summary available for this job.');
                                    }
                                } catch (error) {
                                    console.error('Error fetching fallback summary:', error);
                                    setSummaryText('No summary available for this job.');
                                }
                            }
                        }
                    }
                } catch (error) {
                    console.error('Error polling job status:', error);
                    // Continue polling even if there's an error, but reduce frequency
                    // to avoid overwhelming the server
                }
            };

            // Start polling immediately
            pollJobStatus();

            // Set up interval polling every 2 seconds
            statusPollingRef.current = setInterval(pollJobStatus, 2000);
        };

        initializeJobStatus();

        return () => {
            if (statusPollingRef.current) {
                clearInterval(statusPollingRef.current);
                statusPollingRef.current = null;
            }
        };
    }, [session, id, summaryText, uploadStatus, fileToUpload]);

    const startFileUpload = useCallback(async () => {
        const jobId = session?.sessionId || session?.jobId || id;
        if (!fileToUpload || !jobId) {
            setUploadError('Missing file or job information');
            setUploadStatus('failed');
            return;
        }

        setUploadStatus('uploading');
        setUploadProgress(0);
        setUploadError('');

        // Set upload timeout (5 minutes for large files)
        const timeoutDuration = 5 * 60 * 1000; // 5 minutes
        let uploadTimeout;
        let uploadXhr;

        uploadTimeout = setTimeout(() => {
            if (uploadXhr) {
                uploadXhr.abort();
            }
            handleUploadError('Upload timeout. Please try again with a smaller file or check your connection.');
        }, timeoutDuration);

        try {
            console.log(`Starting upload of ${fileToUpload.name} (${fileToUpload.size} bytes)`);

            uploadXhr = await uploadJobFile(
                jobId,
                fileToUpload,
                // Progress callback
                (progress, loaded, total) => {
                    setUploadProgress(progress);
                    console.log(`Upload progress: ${progress}%`);
                },
                // Success callback
                (response) => {
                    clearTimeout(uploadTimeout);
                    setUploadStatus('completed');
                    setUploadProgress(100);
                    console.log('Upload completed:', response);
                    setRetryCount(0); // Reset retry count on success
                },
                // Error callback
                (errorMessage) => {
                    clearTimeout(uploadTimeout);
                    handleUploadError(errorMessage);
                }
            );

        } catch (error) {
            clearTimeout(uploadTimeout);
            handleUploadError('Failed to start upload. Please try again.');
        }
    }, [fileToUpload, session, setUploadStatus, setUploadProgress, setUploadError, setRetryCount, id]);

    // File upload functionality with robust error handling
    useEffect(() => {
        const jobId = session?.sessionId || session?.jobId || id;
        if (fileToUpload && uploadStatus === 'pending' && jobId) {
            startFileUpload();
        }
    }, [fileToUpload, uploadStatus, session, startFileUpload, id]);

    const handleUploadError = (errorMessage) => {
        console.error('Upload error:', errorMessage);
        setUploadError(errorMessage);
        setUploadStatus('failed');
        setUploadProgress(0);

        // Stop status polling when upload fails
        if (statusPollingRef.current) {
            clearInterval(statusPollingRef.current);
            statusPollingRef.current = null;
        }
    };

    const retryUpload = () => {
        if (retryCount < maxRetries) {
            setRetryCount(prev => prev + 1);
            setUploadStatus('pending');
            setUploadError('');
            console.log(`Retrying upload (attempt ${retryCount + 1}/${maxRetries})`);
        } else {
            setUploadError('Maximum retry attempts reached. Please refresh the page and try again.');
        }
    };

    const handleReturnToHome = () => navigate('/');

    const handleDownloadHtml = () => {
        const html = sessionStatus.resultHTML || session?.resultHTML || 'KnutGPT_visual_log.html';
        const link = document.createElement('a');
        link.href = `/${html}`;
        link.download = html;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    const handleFeedbackSubmit = feedback => console.log('Feedback received:', feedback);

    const handlePreviewFile = (fileName) => {
        setPreviewFileName(fileName);
        setIsLoadingPreview(true);
        setShowPreviewModal(true);
        setTimeout(() => {
            setFileContent(`# ${fileName}\n(Marked-up preview placeholder)`);
            setIsLoadingPreview(false);
        }, 420);
    };

    const renderSelectedFiles = () => {
        const fileNames = session?.logFile
            ? String(session.logFile).split(',').map(f => f.trim()).filter(Boolean)
            : [];

        if (!fileNames.length) {
            return (
                <div className="flex items-center justify-center py-3">
                    <FileX className="w-5 h-5 text-gray-400 mr-2"/>
                    <p className="text-gray-500 italic">No files uploaded</p>
                </div>
            );
        }

        return (
            <div className="space-y-2">
                {fileNames.map((file, idx) => (
                    <div
                        key={idx}
                        className="bg-white rounded-lg border border-gray-400/70 overflow-hidden shadow-sm"
                    >
                        <div className="flex items-center p-2">
                            <div className="flex items-center gap-2 flex-1">
                                <div className="mx-1">
                                    <FileText className="w-5 h-5 text-gray-700 stroke-[1.5]"/>
                                </div>
                                <span className="text-base text-gray-700">{file}</span>
                                <button
                                    onClick={() => handlePreviewFile(file)}
                                    className="mr-3 flex items-center p-1.5 text-sky-600 hover:text-sky-700 hover:bg-sky-50 rounded transition-colors"
                                    title="Open file in new tab"
                                >
                                    Open
                                    <ExternalLink className="w-4 h-4 ml-1.5"/>
                                </button>
                            </div>
                        </div>
                    </div>
                ))}
            </div>
        );
    };

    const getFormattedContent = (content, fileName) => {
        const ext = fileName.split('.').pop()?.toLowerCase();
        if (ext === 'json') {
            try {
                return JSON.stringify(JSON.parse(content), null, 2);
            } catch (_) {
                return content;
            }
        }
        return content;
    };

    if (loading) {
        return (
            <div className="min-h-screen bg-white flex items-center justify-center">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-sky-500"/>
                <span className="ml-3 text-gray-500">Loading job...</span>
            </div>
        );
    }

    if (error) {
        return (
            <div className="min-h-screen bg-white flex items-center justify-center">
                <div className="text-red-500 text-center">
                    <p className="text-lg font-medium">{error}</p>
                    <button
                        onClick={handleReturnToHome}
                        className="mt-4 px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded transition-colors"
                    >
                        Return to Home
                    </button>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-[90vh] flex items-center justify-center py-4">
            <div className="max-w-4xl w-full px-4">
                <div className="bg-white rounded-3xl shadow-2xl p-8 border border-gray-300">
                    <div className="pb-4">
                        <div className="flex items-start justify-between space-x-4">
                            <div className="flex items-start space-x-4">
                                <img src={knutgptLogo} alt="KnutGPT Logo" className="w-16 h-16"/>
                                <div className="flex-1 pt-3 relative">
                                    <h1 className="text-xl font-semibold text-gray-700 leading-none">KnutGPT</h1>
                                    <div>
                                        <SessionTooltip
                                            project={session?.project}
                                            method={session?.method}
                                            sessionType={session?.sessionType}
                                            session={session}
                                        >
                                            {(session?.sessionType === 'analyze' ? 'Analysis' : 'Training') + ' Job #' + safe(id)}
                                        </SessionTooltip>
                                    </div>
                                </div>
                            </div>
                            <div className="pt-3">
                                <h2 className="text-lg font-semibold text-gray-700">
                                    State: <span className="font-medium text-sky-700">
                    {sessionStatus.status === 'uploading' && 'Uploading'}
                                    {sessionStatus.status === 'uploaded' && 'Uploaded'}
                                    {sessionStatus.status === 'waiting-for-exec' && 'Waiting for Exec'}
                                    {sessionStatus.status === 'analyzing' && 'Analyzing'}
                                    {sessionStatus.status === 'training' && 'Training'}
                                    {sessionStatus.status === 'processing' && 'Processing'}
                                    {sessionStatus.status === 'completed' && 'Completed'}
                                    {sessionStatus.status === 'failed' && 'Failed'}
                                    {!['uploading', 'uploaded', 'waiting-for-exec', 'analyzing', 'training', 'processing', 'completed', 'failed'].includes(sessionStatus.status) && 'Unknown'}
                  </span>
                                </h2>
                            </div>
                        </div>
                        <div className="h-[1px] w-full bg-gray-300/90  rounded-full mt-3 mb-2 "/>
                    </div>

                    <div className="space-y-6">
                        {/* Upload Status - Show when file needs to be uploaded */}
                        {fileToUpload && uploadStatus !== 'completed' && (
                            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                                <div className="flex items-center mb-4">
                                    <Upload className="w-6 h-6 text-blue-600 mr-3"/>
                                    <h3 className="text-lg font-semibold text-blue-800">
                                        {uploadStatus === 'uploading' ? 'Uploading File...' :
                                            uploadStatus === 'failed' ? 'Upload Failed' : 'Preparing Upload...'}
                                    </h3>
                                </div>

                                {uploadStatus === 'uploading' && (
                                    <div className="space-y-3">
                                        <div className="flex justify-between text-sm text-blue-700">
                                            <span>Uploading {fileToUpload.name}</span>
                                            <span>{uploadProgress}%</span>
                                        </div>
                                        <div className="w-full bg-blue-200 rounded-full h-3">
                                            <div
                                                className="bg-blue-600 h-3 rounded-full transition-all duration-300"
                                                style={{width: `${uploadProgress}%`}}
                                            />
                                        </div>
                                        <p className="text-sm text-blue-600">
                                            Please keep this page open while the file uploads...
                                        </p>
                                    </div>
                                )}

                                {uploadStatus === 'failed' && (
                                    <div className="space-y-3">
                                        <div className="flex items-center text-red-600">
                                            <AlertCircle className="w-5 h-5 mr-2"/>
                                            <span className="font-medium">Upload Error</span>
                                        </div>
                                        <p className="text-red-700">{uploadError}</p>
                                        {retryCount < maxRetries && (
                                            <button
                                                onClick={retryUpload}
                                                className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700 transition-colors"
                                            >
                                                Retry Upload ({retryCount + 1}/{maxRetries})
                                            </button>
                                        )}
                                        {retryCount >= maxRetries && (
                                            <div className="text-red-600">
                                                <p>Maximum retries reached. Please:</p>
                                                <ul className="list-disc list-inside mt-2 space-y-1">
                                                    <li>Check your internet connection</li>
                                                    <li>Try a smaller file if possible</li>
                                                    <li>Refresh the page and start over</li>
                                                </ul>
                                            </div>
                                        )}
                                    </div>
                                )}

                                {uploadStatus === 'pending' && (
                                    <div className="flex items-center text-blue-600">
                                        <div
                                            className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600 mr-3"/>
                                        <span>Preparing to upload {fileToUpload.name}...</span>
                                    </div>
                                )}
                            </div>
                        )}

                        {/* Analysis Progress - Show after upload completes or if no file to upload */}
                        {(!fileToUpload || uploadStatus === 'completed') && sessionStatus.status !== 'completed' && sessionStatus.status !== 'failed' && (
                            <LoadingComponent
                                percentage={sessionStatus.percentage}
                                label={
                                    sessionStatus.status === 'uploading' ? 'Processing' :
                                        sessionStatus.status === 'waiting-for-exec' ? 'Processing' :
                                            sessionStatus.status === 'training' ? 'Training' :
                                                sessionStatus.status === 'analyzing' ? 'Analyzing' :
                                                    'Processing'
                                }
                                method={session?.sessionType === 'analyze' ? session?.method : ''}
                                status={sessionStatus.status}
                                message={sessionStatus.message}
                            />
                        )}

                        {/* Upload Success Message */}
                        {uploadStatus === 'completed' && sessionStatus.status !== 'completed' && (
                            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                                <div className="flex items-center text-green-800">
                                    <CheckCircle className="w-5 h-5 mr-2"/>
                                    <span className="font-medium">
                                        File uploaded successfully! {session?.sessionType === 'train' ? 'Training' : 'Analysis'} starting...
                                    </span>
                                </div>
                            </div>
                        )}

                        {/* Completed State */}
                        {(() => {
                            console.log('Completed state check:', {
                                'sessionStatus.status': sessionStatus.status,
                                'session?.sessionType': session?.sessionType,
                                'condition result': sessionStatus.status === 'completed' && session?.sessionType === 'analyze'
                            });
                            return null;
                        })()}
                        {sessionStatus.status === 'completed' && session?.sessionType === 'analyze' && (
                            <>
                                {/* 1. Input Log Files and Visual Result in Two Columns */}
                                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                    {/* Left Column - Input Log Files */}
                                    <div>
                                        <p className="text-base font-medium text-gray-600 mb-1.5 pl-1">Input Log
                                            Files</p>
                                        <div
                                            className="bg-gray-100/70 backdrop-blur-sm p-1.5 rounded-xl border border-gray-300">
                                            {renderSelectedFiles()}
                                        </div>
                                    </div>

                                    {/* Right Column - Visual Result */}
                                    <div>
                                        <p className="text-base font-medium text-gray-600 mb-1.5 pl-1">Visual Result</p>
                                        <div className="bg-gray-100/70 rounded-xl border border-gray-300 p-1.5">
                                            <div className="bg-white rounded-lg border border-gray-400/70 shadow-sm">
                                                <div className="flex items-center p-2">
                                                    <div className="flex items-center gap-1 flex-1">
                                                        <div className="mx-1">
                                                            <FileCode className="w-5 h-5 text-gray-700 stroke-[1.5]"/>
                                                        </div>
                                                        <span className="text-base font-medium text-gray-700">
                              {safe(sessionStatus.resultHTML || session?.resultHTML, 'KnutGPT_visual_log.html')}
                            </span>
                                                    </div>
                                                    <a
                                                        href={`/${sessionStatus.resultHTML || session?.resultHTML || 'KnutGPT_visual_log.html'}`}
                                                        target="_blank"
                                                        rel="noopener noreferrer"
                                                        className="mr-3 p-1.5 rounded transition-colors flex items-center text-sky-600 hover:text-sky-700 hover:bg-sky-50"
                                                        title="Open visual result in new tab"
                                                    >
                                                        Open
                                                        <ExternalLink className="ml-1.5 w-4 h-4"/>
                                                    </a>
                                                    <button
                                                        onClick={handleDownloadHtml}
                                                        className="p-2 text-gray-700 hover:text-gray-800 hover:bg-gray-100 rounded transition-colors"
                                                        aria-label="Download HTML"
                                                    >
                                                        <Download className="w-5 h-5"/>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                {/* Analysis Summary Section */}
                                <div className="relative">
                                    <p className="text-base font-medium text-gray-600 mb-2 mt-1 pl-1">
                                        Analysis Summary
                                    </p>
                                    <CustomChatBubble
                                        enableEffects={false}
                                        analysisResult={summaryText || 'No summary available for this job.'}
                                        message=""
                                        typingSpeed={1}
                                        onFeedbackSubmit={handleFeedbackSubmit}
                                        onComplete={() => {
                                        }}
                                        showFeedback={true}
                                    />
                                </div>

                                <div className="flex justify-end pt-2 pr-4">
                                    <img src={knutgptLogo} alt="KnutGPT Logo" className="h-14 w-auto"/>
                                </div>
                            </>
                        )}

                        {/* Training Job Completed */}
                        {sessionStatus.status === 'completed' && session?.sessionType === 'train' && (
                            <>
                                {/* Input Log Files for Training */}
                                <div>
                                    <p className="text-base font-medium text-gray-600 mb-1.5 pl-1">Input Log Files</p>
                                    <div
                                        className="bg-gray-100/70 backdrop-blur-sm p-1.5 rounded-xl border border-gray-300">
                                        {renderSelectedFiles()}
                                    </div>
                                </div>

                                {/* Training Summary */}
                                <div className="relative">
                                    <p className="text-base font-medium text-gray-600 mb-1.5 pl-1">
                                        Training Summary
                                    </p>
                                    <CustomChatBubble
                                        analysisResult={summaryText || 'No summary available for this job.'}
                                        message=""
                                        typingSpeed={3}
                                        onFeedbackSubmit={handleFeedbackSubmit}
                                        onComplete={() => {
                                        }}
                                        showFeedback={false}
                                    />
                                </div>
                                <div className="flex justify-end pt-2 pr-4">
                                    <img src={knutgptLogo} alt="KnutGPT Logo" className="h-14 w-auto"/>
                                </div>
                            </>
                        )}
                    </div>

                    {/*Return to Home button (active in every job-state)*/}
                    <div className="mt-8 mb-4 flex justify-center">
                        <button
                            onClick={handleReturnToHome}
                            className="py-4 px-9 bg-gray-50 hover:bg-gray-100/90 text-gray-600 hover:text-gray-700 border-2 border-gray-400 font-semibold text-lg rounded-xl transition-all duration-100 transform hover:scale-[1.02] flex items-center space-x-2 shadow-lg"
                        >
                            <Undo2 className="w-5 h-5"/>
                            <span>Return to Home Page</span>
                        </button>
                    </div>
                </div>
            </div>

            {/* File Preview Modal */}
            {showPreviewModal && (
                <Modal onClose={() => setShowPreviewModal(false)}>
                    <h2 className="text-xl font-medium mb-3 ml-1 text-gray-700">
                        Preview: <span className="font-normal">{previewFileName}</span>
                    </h2>
                    {isLoadingPreview ? (
                        <div className="flex justify-center items-center py-8">
                            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-sky-500"/>
                            <span className="ml-3 text-gray-500">Loading file…</span>
                        </div>
                    ) : (
                        <pre
                            className="font-mono text-sm bg-gray-100 border border-gray-300 text-gray-900 p-4 rounded-lg overflow-y-auto min-h-[50vh] max-h-[calc(100vh-120px)]">
              {getFormattedContent(fileContent, previewFileName)}
            </pre>
                    )}
                </Modal>
            )}
        </div>
    );
};

export default JobPage;
