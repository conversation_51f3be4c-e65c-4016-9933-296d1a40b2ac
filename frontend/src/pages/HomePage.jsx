// File: src/pages/HomePage.jsx
import React, {useState} from 'react';
import {useNavigate} from 'react-router-dom';
import knutgptLogo from '../assets/knutgpt-logo.svg';
import RecentJobs from '../components/RecentJobs';
import {Eye, EyeOff, Info, Search, Settings} from 'lucide-react';
import AnimatedChatBubble from '../components/AnimatedChatBubble';

const HomePage = () => {
    const navigate = useNavigate();
    const [showRecentJobs, setShowRecentJobs] = useState(true);

    const toggleRecentJobs = () => {
        setShowRecentJobs(!showRecentJobs);
    };

    return (
        <div className="flex flex-col items-center justify-center min-h-100vh">
            <div className="w-full max-w-3xl flex flex-col justify-end" style={{minHeight: "200px"}}>
                <AnimatedChatBubble
                    message="Welcome to KnutGPT! I am an AI powered engineering assistant that increases software development efficiency. I can analyze logs, detect anomalies, and suggest solutions. "
                    tailPosition={20.5}
                    typingSpeed={2}
                />
            </div>

            {/* Hero Section */}
            <div className="flex flex-col items-center my-3 mt-5">
                <div className="flex items-center justify-center space-x-8">
                    <img
                        src={knutgptLogo}
                        alt="Logo"
                        style={{width: 165}}
                        className="my-2 mt-4"
                    />
                    <div className="mr-4">
                        <h1 className="text-7xl font-bold text-gray-700 my-2">KnutGPT</h1>
                        <p className="text-4xl text-gray-500 font-medium">AI Powered Log Analyzer</p>
                    </div>
                </div>
            </div>

            {/* Buttons Section */}
            <div className="flex space-x-5 py-6 duration-150 ease-in-out">
                <button
                    onClick={() => navigate('/analysis')}
                    className="px-9 py-4 bg-sky-600 text-white font-semibold text-lg rounded-xl transition-transform transform hover:scale-[1.04] flex items-center space-x-2 shadow-lg"
                >
                    <Search className="w-6 h-6"/>
                    <span>Analyze Logs</span>
                </button>
                <button
                    onClick={() => navigate('/train')}
                    className="px-9 py-4 bg-sky-600 text-white font-semibold text-lg rounded-xl transition-transform transform hover:scale-[1.04] flex items-center space-x-2 shadow-lg"
                >
                    <Settings className="w-6 h-6"/>
                    <span>Train me</span>
                </button>
                <button
                    onClick={() => navigate('/about')}
                    className="px-9 py-4 bg-sky-600 text-white font-semibold text-lg rounded-xl transition-transform transform hover:scale-[1.04] flex items-center space-x-2 shadow-lg"
                >
                    <Info className="w-6 h-6"/>
                    <span>About KnutGPT</span>
                </button>
            </div>

            {/* Recent Jobs Section (with the Toggle Button) */}
            <div className="flex flex-col items-center pt-2">
                <button
                    onClick={toggleRecentJobs}
                    className="px-2 py-1 mb-1 text-gray-700 font-medium text-base rounded-xl transition-transform transform hover:scale-[1.04] flex items-center space-x-2 duration-150"
                >
                    {showRecentJobs ? (
                        <>
                            <EyeOff className="w-5 h-5 text-sky-600"/>
                            <span>Hide Recent Jobs</span>
                        </>
                    ) : (
                        <>
                            <Eye className="w-5 h-5 text-sky-600"/>
                            <span>Show Recent Jobs</span>
                        </>
                    )}
                </button>
                {showRecentJobs && (
                    <div className="w-full transition-all duration-300 ease-in-out">
                        <RecentJobs/>
                    </div>
                )}
            </div>
        </div>
    );
};

export default HomePage;