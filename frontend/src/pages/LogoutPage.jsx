// File: src/pages/LogoutPage.jsx
import React, {useEffect} from 'react';
import {Navigate} from 'react-router-dom';
import {useUser} from '../context/UserContext';
import {logoutUser} from '../services/api';

/**
 * Logout page that automatically logs out the user and redirects to login
 * This provides a dedicated route for logging out
 */
const LogoutPage = () => {
    const {logout} = useUser();

    useEffect(() => {
        // Clear both localStorage and React state
        logoutUser();
        logout();
    }, [logout]);

    // Immediately redirect to login page
    return <Navigate to="/login" replace={false}/>;
};

export default LogoutPage;
