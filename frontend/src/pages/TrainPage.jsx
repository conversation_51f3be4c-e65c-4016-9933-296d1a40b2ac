// File: src/pages/TrainPage.jsx

import React, {useEffect, useState} from 'react';
import {useNavigate} from 'react-router-dom';
import {Info, Play, Settings, Undo2} from 'lucide-react';

import FileUploader from '../components/FileUploader';
import ProjectSelector from '../components/ProjectSelector';
import KnowledgeCollectionSelector from '../components/KnowledgeCollectionSelector';
import InfoSection from '../components/InfoSection';
import CollapsibleSection from '../components/CollapsibleSection';
import Modal from '../components/Modal';

import knutgptLogo from '../assets/knutgpt-logo.svg';
import api from '../services/api';
import {useUser} from '../context/UserContext';

const TrainPage = () => {
    const navigate = useNavigate();
    const {user} = useUser();

    const [projects, setProjects] = useState([]);
    const [projectsLoading, setProjectsLoading] = useState(true);
    const [selectedProject, setSelectedProject] = useState(null);
    const [knowledgeCollections, setKnowledgeCollections] = useState([]);
    const [collectionsLoading, setCollectionsLoading] = useState(false);
    const [selectedCollection, setSelectedCollection] = useState(null);
    const [uploadedFiles, setUploadedFiles] = useState([]);

    const [isProjectSectionCollapsed, setIsProjectSectionCollapsed] = useState(false);
    const [isCollectionSectionCollapsed, setIsCollectionSectionCollapsed] = useState(false);
    const [showInfoModal, setShowInfoModal] = useState(false);

    // Fetch user-specific projects once
    useEffect(() => {
        const fetchProjects = async () => {
            setProjectsLoading(true);
            try {
                const data = await api.getUserProjects(user?.id);
                setProjects(data);
            } catch (err) {
                setProjects([]);
            }
            setProjectsLoading(false);
        };
        fetchProjects();
        // eslint-disable-next-line
    }, [user]);

    // Fetch collections for the selected project
    useEffect(() => {
        const fetchCollections = async () => {
            if (selectedProject) {
                setCollectionsLoading(true);
                try {
                    const colls = await api.getProjectKnowledgeCollections(selectedProject.id);
                    setKnowledgeCollections(colls);
                } catch {
                    setKnowledgeCollections([]);
                }
                setCollectionsLoading(false);
            } else {
                setKnowledgeCollections([]);
            }
        };
        fetchCollections();
    }, [selectedProject]);

    const handleStartTraining = async () => {
        // Validation
        if (!uploadedFiles || uploadedFiles.length === 0) {
            alert('Please select a file for training.');
            return;
        }

        if (!selectedProject) {
            alert('Please select a project.');
            return;
        }

        if (!selectedCollection) {
            alert('Please select a knowledge collection for training.');
            return;
        }

        const sessionId = crypto.randomUUID(); // Generate standard UUID with dashes

        // Build job object WITHOUT file content - files will be uploaded separately
        const job = {
            sessionId: sessionId, // Backend expects sessionId
            userId: user?.id || 'User-x',
            project: selectedProject.name,
            project_id: selectedProject.id, // Include project ID for backend lookup
            sessionType: 'train', // Backend expects sessionType
            method: 'neural', // Training always uses neural method
            logFile: uploadedFiles[0].name, // Single file name only
            knowledgeCollection: selectedCollection?.name || '',
        };

        // Create job without files
        try {
            const response = await api.createJob(job);
            console.log('Training job created:', response);

            // Navigate to job page with file to upload
            navigate(`/job/${sessionId}`, {
                state: {
                    job: {
                        ...job,
                        sessionId: response.sessionId, // Ensure we have the returned sessionId
                        jobId: response.sessionId,     // Also set jobId for compatibility
                        status: response.status
                    },
                    fileToUpload: uploadedFiles[0] // Pass File object for upload
                }
            });
        } catch (error) {
            console.error('Failed to create training job:', error);
            alert('Failed to create training job. Please try again.');
        }
    };

    const handleProjectSelect = (project) => {
        if (project) {
            setSelectedProject(project);
            setSelectedCollection(null);
            setUploadedFiles([]);
            setIsProjectSectionCollapsed(true);
            setIsCollectionSectionCollapsed(false);
        } else {
            setSelectedProject(null);
            setIsProjectSectionCollapsed(false);
        }
    };

    const handleCollectionSelect = (collection) => {
        if (collection) {
            setSelectedCollection(collection);
            setIsCollectionSectionCollapsed(true);
        } else {
            setSelectedCollection(null);
            setIsCollectionSectionCollapsed(false);
        }
    };

    const handleManageCollections = () => {
        navigate('/knowledge-collections');
    };

    const toggleProjectSection = () => setIsProjectSectionCollapsed(prev => !prev);
    const toggleCollectionSection = () => setIsCollectionSectionCollapsed(prev => !prev);

    const isStartDisabled = !selectedProject || !selectedCollection || uploadedFiles.length === 0;

    // Show uploader only if the collection is selected
    const showUploader = selectedProject && selectedCollection && isCollectionSectionCollapsed;

    return (
        <div className="min-h-[90vh] flex items-center justify-center py-4">
            <div className="max-w-4xl mx-auto container">
                <div
                    className="bg-white rounded-3xl shadow-2xl p-8 border border-gray-300 transition-shadow duration-200">

                    {/* Header Section */}
                    <div
                        className="flex flex-col sm:flex-row items-start sm:items-center gap-4 border-b border-gray-300 mb-6 pb-4">
                        <div className="flex p-1.5 bg-gray-100 rounded-2xl border-2 border-gray-200">
                            <img src={knutgptLogo} alt="KnutGPT Logo" className="w-14 h-14"/>
                        </div>
                        <div className="flex-1 min-w-0">
                            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                                {/* Titles */}
                                <div>
                                    <h1 className="text-[1.4rem] font-semibold text-gray-700 leading-tight">
                                        Training Knut
                                    </h1>
                                    <p className="text-lg flex items-center text-gray-500">
                                        Setup & Configuration
                                    </p>
                                </div>
                                {/* Training Info Button */}
                                <button
                                    onClick={() => setShowInfoModal(true)}
                                    className="p-2 rounded-full hover:bg-gray-100 text-sky-600 transition-colors"
                                    aria-label="More information"
                                >
                                    <Info className="w-6 h-6"/>
                                </button>
                            </div>
                        </div>
                    </div>

                    {/* Main Config Sections */}
                    <div className="space-y-5 p-3">

                        {/* Project Selection Section */}
                        <CollapsibleSection
                            isCollapsed={isProjectSectionCollapsed}
                            toggleCollapse={toggleProjectSection}
                            selectedItem={selectedProject?.name || selectedProject}
                            infoSection={
                                <InfoSection
                                    title="Project Selection"
                                    description="Select a project for training."
                                    extendedDescription="Choose the project you wish to train. Each project holds distinct data configurations and training environments."
                                />
                            }
                        >
                            <ProjectSelector
                                onProjectSelect={handleProjectSelect}
                                selectedProject={selectedProject}
                                projects={projects}
                                loading={projectsLoading}
                            />
                        </CollapsibleSection>

                        {/* Knowledge Collection Selection Section */}
                        {selectedProject && (
                            <CollapsibleSection
                                isCollapsed={isCollectionSectionCollapsed}
                                toggleCollapse={toggleCollectionSection}
                                selectedItem={selectedCollection?.name || selectedCollection}
                                infoSection={
                                    <InfoSection
                                        title="Knowledge Collection"
                                        description="Select a knowledge collection for training."
                                        extendedDescription="A knowledge collection stores log lines from previous sessions for quick reference. Training will enhance this collection with new patterns and insights."
                                    >
                                        <button
                                            onClick={handleManageCollections}
                                            className="flex items-center text-sm px-2 py-1 rounded-full bg-white text-sky-800 border border-sky-700 hover:bg-sky-100 transition-colors"
                                        >
                                            <Settings className="w-4 h-4 mr-1"/>
                                            Manage Collections
                                        </button>
                                    </InfoSection>
                                }
                            >
                                <KnowledgeCollectionSelector
                                    selectedProject={selectedProject}
                                    selectedCollection={selectedCollection}
                                    onCollectionSelect={handleCollectionSelect}
                                    collections={knowledgeCollections}
                                    loading={collectionsLoading}
                                />
                            </CollapsibleSection>
                        )}

                        {/* File Uploader Section */}
                        {showUploader && (
                            <section className="bg-gray-50 rounded-xl border border-gray-300 overflow-hidden">
                                <div className="p-2">
                                    <div className="px-2">
                                        <InfoSection
                                            title="File Uploader"
                                            description="Upload training files."
                                            extendedDescription="Select the files to be used for training. These files will help improve the model's ability to recognize patterns and anomalies in future log analysis."
                                        />
                                    </div>
                                    <div className="p-4 pt-0">
                                        <FileUploader onFilesChange={setUploadedFiles}/>
                                    </div>
                                </div>
                            </section>
                        )}

                        {/* Bottom Button Section */}
                        <div className="pt-5 grid grid-cols-1 md:grid-cols-2 gap-4">
                            {/* Return to Home Button */}
                            <div className="flex justify-center">
                                <button
                                    onClick={() => navigate('/')}
                                    className="py-4 px-9 bg-gray-50 hover:bg-gray-100/90 text-gray-600 hover:text-gray-700 border-2 border-gray-400 font-semibold text-lg rounded-xl transition-all duration-100 transform hover:scale-[1.02] flex items-center space-x-2 shadow-lg"
                                >
                                    <Undo2 className="w-5 h-5"/>
                                    <span>Return to Home Page</span>
                                </button>
                            </div>
                            {/* Start Training Button */}
                            <div className="flex justify-center">
                                <button
                                    onClick={handleStartTraining}
                                    className={`py-4 px-9 text-lg rounded-xl font-semibold shadow-lg 
                    ${
                                        isStartDisabled
                                            ? 'bg-gray-200/70 text-gray-400 cursor-not-allowed'
                                            : ' bg-sky-600 text-white font-semibold text-lg rounded-xl transition-all duration-200 transform hover:scale-[1.02] flex items-center space-x-2 shadow-lg'
                                    }`}
                                    disabled={isStartDisabled}
                                    aria-label="Start Training"
                                >
                                    {!isStartDisabled && <Play className="w-5 h-5"/>}
                                    <span>{isStartDisabled ? 'Complete Configuration' : 'Start Training'}</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Info Modal */}
            {showInfoModal && (
                <Modal onClose={() => setShowInfoModal(false)}>
                    <h2 className="text-xl font-semibold mb-3 text-gray-700">Model Training Overview</h2>
                    <p className="text-gray-600 mb-4">
                        Train KnutGPT's neural models with your log data to improve anomaly detection and pattern
                        recognition.
                    </p>
                    <ul className="list-disc list-inside text-gray-600 space-y-1">
                        <li>Upload training log files to enhance model performance.</li>
                        <li>Training updates the selected knowledge collection with new patterns.</li>
                        <li>Improved models provide better analysis results for future sessions.</li>
                    </ul>
                </Modal>
            )}
        </div>
    );
};

export default TrainPage;