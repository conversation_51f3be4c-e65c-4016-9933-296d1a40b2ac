// File: src/pages/AnalysisPage.jsx

import React, {useCallback, useEffect, useState} from 'react';
import {useLocation, useNavigate} from 'react-router-dom';
import {ChevronDown, ChevronUp, Info, Play, Settings, Undo2} from 'lucide-react';

import FileUploader from '../components/FileUploader';
import ProjectSelector from '../components/ProjectSelector';
import MethodSelector from '../components/MethodSelector';
import KnowledgeCollectionSelector from '../components/KnowledgeCollectionSelector';
import GenericKeywordInput from '../components/GenericKeywordInput';
import GenericRegexInput from '../components/GenericRegexInput';
import InfoSection from '../components/InfoSection';
import CollapsibleSection from '../components/CollapsibleSection';
import Modal from '../components/Modal';
import LoadingModal from '../components/LoadingModal';

import knutgptLogo from '../assets/knutgpt-logo.svg';
import api from '../services/api';
import {useUser} from '../context/UserContext';

const AnalysisPage = () => {
    const navigate = useNavigate();
    const location = useLocation();
    const {user} = useUser();
    const presetProject = location.state?.selectedProject || null;

    const [projects, setProjects] = useState([]);
    const [projectsLoading, setProjectsLoading] = useState(true);
    const [selectedProject, setSelectedProject] = useState(presetProject || null);
    const [knowledgeCollections, setKnowledgeCollections] = useState([]);
    const [collectionsLoading, setCollectionsLoading] = useState(false);

    const [selectedMethod, setSelectedMethod] = useState('');
    const [selectedCollection, setSelectedCollection] = useState(null);

    const [errorKeywords, setErrorKeywords] = useState([]);
    const [falsePositiveKeywords, setFalsePositiveKeywords] = useState([]);
    const [hintsKeywords, setHintsKeywords] = useState([]);
    const [successKeywords, setSuccessKeywords] = useState([]);

    const [errorRegexes, setErrorRegexes] = useState([]);
    const [falseRegexes, setFalseRegexes] = useState([]);
    const [hintsRegexes, setHintsRegexes] = useState([]);
    const [successRegexes, setSuccessRegexes] = useState([]);

    const [advancedOptions, setAdvancedOptions] = useState(false);
    const [uploadedFiles, setUploadedFiles] = useState([]);
    const [isProjectSectionCollapsed, setIsProjectSectionCollapsed] = useState(!!presetProject);
    const [isMethodSectionCollapsed, setIsMethodSectionCollapsed] = useState(false);
    const [isCollectionSectionCollapsed, setIsCollectionSectionCollapsed] = useState(false);
    const [isRegExSectionCollapsed, setIsRegExSectionCollapsed] = useState(false);
    const [isKeywordsSectionCollapsed, setIsKeywordsSectionCollapsed] = useState(false);

    const [showInfoModal, setShowInfoModal] = useState(false);
    const [isResetting, setIsResetting] = useState(false);
    const [isResettingRegex, setIsResettingRegex] = useState(false);

    // Fetch user-specific projects once
    useEffect(() => {
        const fetchProjects = async () => {
            setProjectsLoading(true);
            try {
                const data = await api.getUserProjects(user?.id);
                setProjects(data);
            } catch (err) {
                setProjects([]);
            }
            setProjectsLoading(false);
        };
        fetchProjects();
        // eslint-disable-next-line
    }, [user]);

    // Fetch collections for the selected project
    useEffect(() => {
        const fetchCollections = async () => {
            if (selectedProject) {
                setCollectionsLoading(true);
                try {
                    const colls = await api.getProjectKnowledgeCollections(selectedProject.id);
                    console.log('Fetched Collections:', colls);
                    setKnowledgeCollections([]);
                    // Set collections if we have project or user collections
                    if (colls.project_collections.length > 0 || colls.user_collections.length > 0) {
                        setKnowledgeCollections(colls);
                    }
                } catch {
                    setKnowledgeCollections([]);
                }
                setCollectionsLoading(false);
            } else {
                setKnowledgeCollections([]);
            }
        };
        fetchCollections();
    }, [selectedProject]);

    useEffect(() => {
        if (presetProject) {
            handleProjectSelect(presetProject);
        }
        // eslint-disable-next-line
    }, [presetProject]);

    const handleStartAnalysis = async () => {
        // Validation
        if (!uploadedFiles || uploadedFiles.length === 0) {
            alert('Please select a file for analysis.');
            return;
        }

        if (!selectedProject) {
            alert('Please select a project.');
            return;
        }

        if (!selectedMethod) {
            alert('Please select an analysis method.');
            return;
        }

        if (selectedMethod === 'Neural' && !selectedCollection) {
            alert('Please select a knowledge collection for neural analysis.');
            return;
        }

        const sessionId = crypto.randomUUID(); // Generate standard UUID with dashes

        // Build job object WITHOUT file content - files will be uploaded separately
        const job = {
            sessionId: sessionId, // Backend expects sessionId
            userId: user?.id || 'User-x',
            project: selectedProject.name,
            project_id: selectedProject.id, // Include project ID for backend lookup
            sessionType: 'analyze', // Backend expects sessionType
            method:
                selectedMethod === 'Neural'
                    ? 'neural'
                    : selectedMethod === 'Keywords'
                        ? 'keywords'
                        : selectedMethod === 'RegEx'
                            ? 'regex'
                            : String(selectedMethod || '').toLowerCase(),
            logFile: uploadedFiles[0].name, // Single file name only
            knowledgeCollection: selectedMethod === 'Neural' ? selectedCollection?.name : '',
            errorKeywords: selectedMethod === 'Keywords' ? errorKeywords : [],
            falsePositiveKeywords: selectedMethod === 'Keywords' ? falsePositiveKeywords : [],
            hintsKeywords: selectedMethod === 'Keywords' ? hintsKeywords : [],
            successKeywords: selectedMethod === 'Keywords' ? successKeywords : [],
            errorRegexes: selectedMethod === 'RegEx' ? errorRegexes : [],
            falseRegexes: selectedMethod === 'RegEx' ? falseRegexes : [],
            hintsRegexes: selectedMethod === 'RegEx' ? hintsRegexes : [],
            successRegexes: selectedMethod === 'RegEx' ? successRegexes : [],
        };

        // Create job without files
        try {
            const response = await api.createJob(job);
            console.log('Job created:', response);

            // Navigate to job page with file to upload
            navigate(`/job/${sessionId}`, {
                state: {
                    job: {
                        ...job,
                        sessionId: response.sessionId, // Ensure we have the returned sessionId
                        jobId: response.sessionId,     // Also set jobId for compatibility
                        status: response.status
                    },
                    fileToUpload: uploadedFiles[0] // Pass File object for upload
                }
            });
        } catch (error) {
            console.error('Failed to create job:', error);
            alert('Failed to create analysis session. Please try again.');
        }
    };

    const handleProjectSelect = proj => {
        if (proj) {
            setSelectedProject(proj);
            setSelectedMethod('');
            setSelectedCollection(null);
            setErrorKeywords([]);
            setFalsePositiveKeywords([]);
            setHintsKeywords([]);
            setSuccessKeywords([]);
            setErrorRegexes([]);
            setFalseRegexes([]);
            setHintsRegexes([]);
            setSuccessRegexes([]);
            setAdvancedOptions(false);
            setUploadedFiles([]);
            setIsProjectSectionCollapsed(true);
            setIsMethodSectionCollapsed(false);
            setIsCollectionSectionCollapsed(false);
        } else {
            setSelectedProject(null);
            setIsProjectSectionCollapsed(false);
        }
    };

    const handleMethodSelect = method => {
        setSelectedMethod(method);
        setIsMethodSectionCollapsed(true);
    };

    const handleCollectionSelect = coll => {
        if (coll) {
            setSelectedCollection(coll);
            setIsCollectionSectionCollapsed(true);
        } else {
            setSelectedCollection(null);
            setIsCollectionSectionCollapsed(false);
        }
    };

    const handleManageCollections = () => navigate('/knowledge-collections');

    const toggleProjectSection = () => setIsProjectSectionCollapsed(prev => !prev);
    const toggleMethodSection = () => setIsMethodSectionCollapsed(prev => !prev);
    const toggleCollectionSection = () => setIsCollectionSectionCollapsed(prev => !prev);
    const toggleAdvancedOptions = () => setAdvancedOptions(prev => !prev);
    const toggleKeywordsSection = () => setIsKeywordsSectionCollapsed(prev => !prev);
    const toggleRegExSection = () => setIsRegExSectionCollapsed(prev => !prev);

    const isStartDisabled =
        !selectedProject ||
        !selectedMethod ||
        (selectedMethod === 'Neural' && !selectedCollection) ||
        uploadedFiles.length === 0;

    // Show uploader only if the user has "done" the relevant section
    const showUploader =
        selectedProject &&
        (
            (selectedMethod === 'Neural' && isCollectionSectionCollapsed) ||
            (selectedMethod === 'Keywords' && isKeywordsSectionCollapsed) ||
            (selectedMethod === 'RegEx' && isRegExSectionCollapsed)
        );

    // Memoized fetch functions for vocabularies to prevent infinite loops
    const fetchKeywordVocab = useCallback((variant) => () => api.fetchKeywordVocabulary(variant), []);
    const fetchRegexVocab = useCallback((variant) => () => api.fetchRegexVocabulary(variant), []);

    // Save all keyword vocabularies
    const saveKeywordVocabularies = async () => {
        try {
            await Promise.all([
                api.saveKeywordVocabulary('error', errorKeywords),
                api.saveKeywordVocabulary('false_positive', falsePositiveKeywords),
                api.saveKeywordVocabulary('hints', hintsKeywords),
                api.saveKeywordVocabulary('success', successKeywords)
            ]);
        } catch (error) {
            console.error('Error saving keyword vocabularies:', error);
        }
    };

    // Reset all keyword vocabularies to defaults
    const resetKeywordVocabularies = async () => {
        setIsResetting(true);
        try {
            // Reset backend (clear user customizations)
            await api.resetKeywordVocabulary('error');
            await api.resetKeywordVocabulary('false_positive');
            await api.resetKeywordVocabulary('hints');
            await api.resetKeywordVocabulary('success');

            // Fetch fresh defaults
            const errorDefaults = await api.fetchKeywordVocabulary('error');
            const falsePositiveDefaults = await api.fetchKeywordVocabulary('false_positive');
            const hintsDefaults = await api.fetchKeywordVocabulary('hints');
            const successDefaults = await api.fetchKeywordVocabulary('success');

            // Update UI state
            setErrorKeywords(errorDefaults);
            setFalsePositiveKeywords(falsePositiveDefaults);
            setHintsKeywords(hintsDefaults);
            setSuccessKeywords(successDefaults);

            // Keep modal visible a bit longer to show the beautiful animation
            await new Promise(resolve => setTimeout(resolve, 800));

        } catch (error) {
            console.error('Error resetting keyword vocabularies:', error);
        } finally {
            setIsResetting(false);
        }
    };

    // Save all regex vocabularies
    const saveRegexVocabularies = async () => {
        try {
            await Promise.all([
                api.saveRegexVocabulary('error', errorRegexes),
                api.saveRegexVocabulary('false_positive', falseRegexes),
                api.saveRegexVocabulary('hints', hintsRegexes),
                api.saveRegexVocabulary('success', successRegexes)
            ]);
        } catch (error) {
            console.error('Error saving regex vocabularies:', error);
        }
    };

    // Reset all regex vocabularies to defaults
    const resetRegexVocabularies = async () => {
        setIsResettingRegex(true);
        try {
            // Reset backend (clear user customizations)
            await api.resetRegexVocabulary('error');
            await api.resetRegexVocabulary('false_positive');
            await api.resetRegexVocabulary('hints');
            await api.resetRegexVocabulary('success');

            // Fetch fresh defaults
            const errorDefaults = await api.fetchRegexVocabulary('error');
            const falsePositiveDefaults = await api.fetchRegexVocabulary('false_positive');
            const hintsDefaults = await api.fetchRegexVocabulary('hints');
            const successDefaults = await api.fetchRegexVocabulary('success');

            // Update UI state
            setErrorRegexes(errorDefaults);
            setFalseRegexes(falsePositiveDefaults);
            setHintsRegexes(hintsDefaults);
            setSuccessRegexes(successDefaults);

            // Keep modal visible longer to show the beautiful animation
            await new Promise(resolve => setTimeout(resolve, 1200));

        } catch (error) {
            console.error('Error resetting regex vocabularies:', error);
        } finally {
            setIsResettingRegex(false);
        }
    };

    // Enhanced toggle functions that handle saving
    const toggleKeywordsSectionWithSave = () => {
        if (!isKeywordsSectionCollapsed) {
            // Collapsing (Save button clicked)
            saveKeywordVocabularies();
        }
        toggleKeywordsSection();
    };

    const toggleRegexSectionWithSave = () => {
        if (!isRegExSectionCollapsed) {
            // Collapsing (Save button clicked)
            saveRegexVocabularies();
        }
        toggleRegExSection();
    };

    return (
        <div className="min-h-[90vh] flex items-center justify-center py-4">
            <div className="max-w-4xl mx-auto container">
                <div className="bg-white rounded-3xl shadow-2xl p-8 border transition-shadow duration-200">

                    {/* Header Section */}
                    <div
                        className="flex flex-col sm:flex-row items-start sm:items-center gap-4 border-b border-gray-300 mb-6 pb-4">
                        <div className="flex p-1.5 bg-gray-100 rounded-2xl border-2 border-gray-200">
                            <img src={knutgptLogo} alt="KnutGPT Logo" className="w-14 h-14"/>
                        </div>
                        <div className="flex-1 min-w-0">
                            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                                {/* Titles */}
                                <div>
                                    <h1 className="text-[1.4rem] font-semibold text-gray-700 leading-tight">
                                        Log Analysis
                                    </h1>
                                    <p className="text-lg flex items-center text-gray-500">
                                        Setup & Configuration
                                    </p>
                                </div>
                                {/* Info Button */}
                                <button
                                    onClick={() => setShowInfoModal(true)}
                                    className="p-2 rounded-full hover:bg-gray-100 text-sky-600 transition-colors"
                                    aria-label="More information"
                                >
                                    <Info className="w-6 h-6"/>
                                </button>
                            </div>
                        </div>
                    </div>

                    {/* Main Config Sections */}
                    <div className="space-y-5 p-3">

                        {/* Project Selection Section */}
                        <CollapsibleSection
                            isCollapsed={isProjectSectionCollapsed}
                            toggleCollapse={toggleProjectSection}
                            selectedItem={selectedProject?.name || selectedProject}
                            infoSection={
                                <InfoSection
                                    title="Project Selection"
                                    description="Select a project to analyze."
                                    extendedDescription="Here you can select the project you want to analyze. Each project contains its own set of data and configurations."
                                />
                            }
                        >
                            <ProjectSelector
                                onProjectSelect={handleProjectSelect}
                                selectedProject={selectedProject}
                                projects={projects}
                                loading={projectsLoading}
                            />
                        </CollapsibleSection>

                        {/* Method Selection Section */}
                        {selectedProject && (
                            <CollapsibleSection
                                isCollapsed={isMethodSectionCollapsed}
                                toggleCollapse={toggleMethodSection}
                                selectedItem={selectedMethod?.name || selectedMethod}
                                infoSection={
                                    <InfoSection
                                        title="Analysis Method"
                                        description="Choose an analysis method."
                                        extendedDescription="Select the method you want to use for analyzing the project. Different methods offer different ways of analyzing the logfiles."
                                        listItems={[
                                            'Neural: Neural search relying on a trained vector space.',
                                            'Keywords: Simple binary keyword match.',
                                            'RegEx: Match based on regular expressions with advanced options.',
                                        ]}
                                    />
                                }
                            >
                                <MethodSelector
                                    knowledgeCollections={knowledgeCollections}
                                    selectedMethod={selectedMethod}
                                    onMethodSelect={handleMethodSelect}
                                />
                            </CollapsibleSection>
                        )}

                        {/* Knowledge Collection Selection Section */}
                        {selectedProject && selectedMethod === 'Neural' && (
                            <CollapsibleSection
                                isCollapsed={isCollectionSectionCollapsed}
                                toggleCollapse={toggleCollectionSection}
                                selectedItem={selectedCollection?.name || selectedCollection}
                                infoSection={<InfoSection
                                    title="Knowledge Collection"
                                    description="Select a knowledge collection."
                                    extendedDescription="A knowledge collection stores log lines from previous sessions for quick reference."
                                >
                                    <button
                                        onClick={handleManageCollections}
                                        className=" flex items-center text-sm px-2 py-1 rounded-full bg-white text-sky-800 border border-sky-700 hover:bg-sky-100 transition-colors"
                                    >
                                        <Settings className="w-4 h-4 mr-1"/>
                                        Manage Collections
                                    </button>
                                </InfoSection>
                                }
                            >
                                <KnowledgeCollectionSelector
                                    selectedProject={selectedProject}
                                    selectedCollection={selectedCollection}
                                    onCollectionSelect={handleCollectionSelect}
                                    collections={knowledgeCollections}
                                    loading={collectionsLoading}
                                />
                            </CollapsibleSection>
                        )}

                        {/* Keywords Selection Section */}
                        {selectedProject && selectedMethod === 'Keywords' && (
                            <CollapsibleSection
                                isCollapsed={isKeywordsSectionCollapsed}
                                toggleCollapse={toggleKeywordsSectionWithSave}
                                selectedItem={errorKeywords.length > 0 ? `${errorKeywords.length} error keywords` : null}
                                infoSection={
                                    <InfoSection
                                        title="Analysis Keywords"
                                        description="Enter keywords for analysis."
                                        extendedDescription="Enter keywords and press Enter to add them. In advanced mode, you can specify different types such as false positive, hints and success keywords."
                                    />
                                }
                                showCollapseButton
                                collapseButtonLabel="Save"
                                onReset={resetKeywordVocabularies}
                            >
                                <div className="p-1">
                                    {/* Error Keywords (defaultly visible) */}
                                    <div className="mb-4">
                                        <label className="block text-sm font-semibold text-gray-600 mb-1.5 ml-1">
                                            Keywords: <span className="text-red-800 font-medium ml-0.5">Error</span>
                                        </label>
                                        <GenericKeywordInput
                                            keywords={errorKeywords}
                                            setKeywords={setErrorKeywords}
                                            placeholder="Enter analysis keyword and press Enter"
                                            variant="error"
                                            fetchVocabulary={fetchKeywordVocab('error')}
                                        />
                                    </div>

                                    {/* Advanced Options Toggle */}
                                    <button
                                        onClick={toggleAdvancedOptions}
                                        className="py-2 px-1 rounded-lg flex items-center bg-transparent hover:bg-gray-100 transition-all duration-100 text-sky-700 hover:text-sky-900 focus:outline-none"
                                    >
                                        {advancedOptions ? (
                                            <>
                                                <span>Hide Advanced Options</span>
                                                <ChevronUp size={18} className="ml-1"/>
                                            </>
                                        ) : (
                                            <>
                                                <span>Show Advanced Options</span>
                                                <ChevronDown size={18} className="ml-1"/>
                                            </>
                                        )}
                                    </button>
                                    {advancedOptions && (
                                        <>
                                            {/* False Positive */}
                                            <div className="mt-4 mb-4">
                                                <label
                                                    className="block text-sm font-semibold text-gray-600 mb-1.5 ml-1">
                                                    Keywords: <span className="text-amber-700 font-medium ml-0.5">False Positive</span>
                                                </label>
                                                <GenericKeywordInput
                                                    keywords={falsePositiveKeywords}
                                                    setKeywords={setFalsePositiveKeywords}
                                                    placeholder="Enter false positive keyword and press Enter"
                                                    variant="falsePositive"
                                                    fetchVocabulary={fetchKeywordVocab('false_positive')}
                                                />
                                            </div>
                                            <div className="mb-4">
                                                <label
                                                    className="block text-sm font-semibold text-gray-600 mb-1.5 ml-1">
                                                    Keywords: <span
                                                    className="text-cyan-700 font-medium ml-0.5">Hints</span>
                                                </label>
                                                <GenericKeywordInput
                                                    keywords={hintsKeywords}
                                                    setKeywords={setHintsKeywords}
                                                    placeholder="Enter hints keyword and press Enter"
                                                    variant="hints"
                                                    fetchVocabulary={fetchKeywordVocab('hints')}
                                                />
                                            </div>
                                            <div>
                                                <label
                                                    className="block text-sm font-semibold text-gray-600 mb-1.5 ml-1">
                                                    Keywords: <span
                                                    className="text-green-700 font-medium ml-0.5">Success</span>
                                                </label>
                                                <GenericKeywordInput
                                                    keywords={successKeywords}
                                                    setKeywords={setSuccessKeywords}
                                                    placeholder="Enter success keyword and press Enter"
                                                    variant="success"
                                                    fetchVocabulary={fetchKeywordVocab('success')}
                                                />
                                            </div>
                                        </>
                                    )}
                                </div>
                            </CollapsibleSection>
                        )}

                        {/* RegEx Selection Section */}
                        {selectedProject && selectedMethod === 'RegEx' && (
                            <CollapsibleSection
                                isCollapsed={isRegExSectionCollapsed}
                                toggleCollapse={toggleRegexSectionWithSave}
                                selectedItem={errorRegexes.length > 0 ? `${errorRegexes.length} error regexes` : null}
                                infoSection={
                                    <InfoSection
                                        title="Regular Expressions"
                                        description="Enter custom regular expressions patterns for analysis to match and search for these."
                                        extendedDescription="Provide valid JavaScript regex syntax (without enclosing slashes)."
                                    />
                                }
                                showCollapseButton
                                collapseButtonLabel="Save"
                                onReset={resetRegexVocabularies}
                            >
                                <div className="p-1">
                                    {/* Error Regexes (defaultly visible) */}
                                    <div className="mb-4">
                                        <label className="block text-sm font-semibold text-gray-600 mb-1.5 ml-1">
                                            RegEx Patterns: <span className="text-red-800 ml-0.5">Error</span>
                                        </label>
                                        <GenericRegexInput
                                            regexes={errorRegexes}
                                            setRegexes={setErrorRegexes}
                                            placeholder="Enter error regex and press Enter"
                                            variant="error"
                                            fetchVocabulary={fetchRegexVocab('error')}
                                        />
                                    </div>
                                    {/* Advanced Options Toggle */}
                                    <button
                                        onClick={toggleAdvancedOptions}
                                        className="py-2 px-1 rounded-lg flex items-center bg-transparent hover:bg-gray-100 transition-all duration-100 text-sky-700 hover:text-sky-900 focus:outline-none"
                                    >
                                        {advancedOptions ? (
                                            <>
                                                <span>Hide Advanced Options</span>
                                                <ChevronUp size={18} className="ml-1"/>
                                            </>
                                        ) : (
                                            <>
                                                <span>Show Advanced Options</span>
                                                <ChevronDown size={18} className="ml-1"/>
                                            </>
                                        )}
                                    </button>
                                    {advancedOptions && (
                                        <>
                                            {/* False Positive Regexes */}
                                            <div className="mt-4 mb-4">
                                                <label
                                                    className="block text-sm font-semibold text-gray-600 mb-1.5 ml-1">
                                                    RegEx Patterns: <span className="text-amber-700 font-medium ml-0.5">False Positive</span>
                                                </label>
                                                <GenericRegexInput
                                                    regexes={falseRegexes}
                                                    setRegexes={setFalseRegexes}
                                                    placeholder="Enter false positive regex and press Enter"
                                                    variant="falsePositive"
                                                    fetchVocabulary={fetchRegexVocab('false_positive')}
                                                />
                                            </div>
                                            <div className="mb-4">
                                                <label
                                                    className="block text-sm font-semibold text-gray-600 mb-1.5 ml-1">
                                                    RegEx Patterns: <span
                                                    className="text-cyan-700 font-medium ml-0.5">Hints</span>
                                                </label>
                                                <GenericRegexInput
                                                    regexes={hintsRegexes}
                                                    setRegexes={setHintsRegexes}
                                                    placeholder="Enter hints regex and press Enter"
                                                    variant="hints"
                                                    fetchVocabulary={fetchRegexVocab('hints')}
                                                />
                                            </div>
                                            <div>
                                                <label
                                                    className="block text-sm font-semibold text-gray-600 mb-1.5 ml-1">
                                                    RegEx Patterns: <span
                                                    className="text-green-700 font-medium ml-0.5">Success</span>
                                                </label>
                                                <GenericRegexInput
                                                    regexes={successRegexes}
                                                    setRegexes={setSuccessRegexes}
                                                    placeholder="Enter success regex and press Enter"
                                                    variant="success"
                                                    fetchVocabulary={fetchRegexVocab('success')}
                                                />
                                            </div>
                                        </>
                                    )}
                                </div>
                            </CollapsibleSection>
                        )}

                        {/* File Uploader Section */}
                        {showUploader && (
                            <section className="bg-gray-50 rounded-xl border border-gray-300 overflow-hidden">
                                <div className="p-2">
                                    <div className="px-2">
                                        <InfoSection
                                            title="File Uploader"
                                            description="Select a file for analysis"
                                            extendedDescription="Select the files to be analyzed based on your configuration."
                                        />
                                    </div>
                                    <div className="p-4 pt-0">
                                        <FileUploader onFilesChange={setUploadedFiles}/>
                                    </div>
                                </div>
                            </section>
                        )}

                        {/* Bottom Button Section */}
                        <div className="pt-5 grid grid-cols-1 md:grid-cols-2 gap-2">
                            {/* Return to Home Button */}
                            <div className="flex justify-center">
                                <button
                                    onClick={() => navigate('/')}
                                    className="py-4 px-9 bg-gray-50 hover:bg-gray-100/90 text-gray-600 hover:text-gray-700 border-2 border-gray-400 font-semibold text-lg rounded-xl transition-all duration-100 transform hover:scale-[1.02] flex items-center space-x-2 shadow-lg"
                                >
                                    <Undo2 className="w-5 h-5"/>
                                    <span>Return to Home Page</span>
                                </button>
                            </div>
                            {/* Start Analysis Button */}
                            <div className="flex justify-center">
                                <button
                                    onClick={handleStartAnalysis}
                                    className={`py-4 px-9 text-lg rounded-xl font-semibold shadow-lg 
                    ${
                                        isStartDisabled
                                            ? 'bg-gray-200/70 text-gray-400 cursor-not-allowed'
                                            : ' bg-sky-600 text-white font-semibold text-lg rounded-xl transition-all duration-100 transform hover:scale-[1.02] flex items-center space-x-2 shadow-lg'
                                    }`}
                                    disabled={isStartDisabled}
                                    aria-label="Start Analysis"
                                >
                                    {!isStartDisabled && <Play className="w-5 h-5"/>}
                                    <span>{isStartDisabled ? 'Complete Configuration' : 'Start Analysis'}</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Info Modal */}
            {showInfoModal && (
                <Modal onClose={() => setShowInfoModal(false)}>
                    <div className="min-h-[50vh] flex flex-col text-lg">
                        <h2 className="text-2xl font-semibold mb-4 text-gray-700">Log Analyser Overview</h2>
                        <p className="text-gray-600 mb-4">
                            The primary goal is anomaly detection in log files, using both basic and AI/ML techniques.
                        </p>
                        <ul className="list-disc list-inside text-gray-600 space-y-1">
                            <li>Analyse logs using various methods and get results files.</li>
                            <li>Real-time and batch log analysis with summaries.</li>
                            <li>Generate portable HTML visualizations and LLM prompts for human-friendly summaries.</li>
                        </ul>
                    </div>
                </Modal>
            )}

            {/* Beautiful Loading Modals */}
            <LoadingModal
                isOpen={isResetting}
                message="Resetting Keywords"
                subMessage="Clearing customizations and loading defaults..."
            />

            <LoadingModal
                isOpen={isResettingRegex}
                message="Resetting RegEx Patterns"
                subMessage="Clearing customizations and loading defaults..."
            />
        </div>
    );
};

export default AnalysisPage;