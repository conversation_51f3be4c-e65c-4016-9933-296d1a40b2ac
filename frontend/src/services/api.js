// File: src/services/api.js
// Centralized API service for KnutGPT frontend
import SHA256 from 'crypto-js/sha256';
import HEX from 'crypto-js/enc-hex';

const API_BASE = process.env.REACT_APP_API_URL || 'https://localhost:5123/api';
const AUTH_TOKEN_KEY = 'jwt_token';
const USER_KEY = 'jwt_user';

/**
 * Helper function for unified error handling and JSON parsing
 * @param {Response} response - The fetch response object
 * @returns {Promise<Object>} Parsed JSON response
 * @throws {Error} Throws error with parsed error message if response is not ok
 * @example
 * // Success response format:
 * { data: {...}, status: "success" }
 * // Error response format:
 * { error: "Error message", status: "error" }
 */
async function handleResponse(response) {
    if (!response.ok) {
        const errorText = await response.text();
        let message = 'Unknown API error';
        try {
            message = JSON.parse(errorText).error || message;
        } catch (_) {
            message = errorText || message;
        }
        throw new Error(message);
    }
    return await response.json();
}

/**
 * Get auth headers for API requests
 */
export function getAuthHeaders() {
    const token = localStorage.getItem(AUTH_TOKEN_KEY);
    return token ? {'Authorization': `Bearer ${token}`} : {};
}

// USER

/**
 * Authenticates a user with username and password
 * @param {string} username - User's email/username
 * @param {string} password - User's password
 * @returns {Promise<{id: number, username: string, role: string, projects: Array<number>}>} User object without password
 * @throws {Error} Throws error if the request fails or credentials are invalid
 * @example
 * // POST /api/login
 * // Input: username = "<EMAIL>", password = "password123"
 * // Request Body:
 * {
 *   "username": "<EMAIL>",
 *   "password": "password123"
 * }
 * // Response:
 * {
 *   "id": 1,
 *   "username": "<EMAIL>",
 *   "role": "user",
 *   "projects": [1, 2]
 * }
 */
export async function loginUser(username, password) {
    try {
        // Call the JWT auth endpoint
        const response = await fetch(`${API_BASE}/login`, {
            method: 'POST',
            headers: {'Content-Type': 'application/json', ...getAuthHeaders()},
            body: JSON.stringify({
                username,
                password: SHA256(password).toString(HEX),
            }),
        });

        if (!response.ok) {
            throw new Error('Login failed. Please check your credentials.');
        }

        const data = await response.json();

        // Store tokens and user info
        localStorage.setItem(AUTH_TOKEN_KEY, data.access_token);
        localStorage.setItem(USER_KEY, JSON.stringify(data.user));

        return data.user;
    } catch (error) {
        console.error('Login error:', error);
        throw error;
    }
}

/**
 * Log out the current user
 */
export function logoutUser() {
    localStorage.removeItem(AUTH_TOKEN_KEY);
    localStorage.removeItem(USER_KEY);
}

/**
 * Check if user is authenticated
 * @returns {boolean} True if user has valid auth token
 */
export function isAuthenticated() {
    return !!localStorage.getItem(AUTH_TOKEN_KEY);
}

/**
 * Validate the authentication token and refresh it to extend session
 * @returns {boolean} True if token is valid
 */
export function validateToken() {
    const token = localStorage.getItem(AUTH_TOKEN_KEY);
    if (!token) return false;

    try {
        // Import jwt-decode
        const {jwtDecode} = require('jwt-decode');

        // Decode the token
        const decodedToken = jwtDecode(token);

        // Check if token has expired
        const currentTime = Date.now() / 1000;
        if (decodedToken.exp && decodedToken.exp < currentTime) {
            console.log('Token has expired');
            logoutUser();
            return false;
        }

        // Always refresh token on validation to extend session lifetime for active users
        refreshToken().catch(error => {
            console.error('Failed to refresh token:', error);
        });

        return true;
    } catch (error) {
        console.error('Token validation error:', error);
        logoutUser();
        return false;
    }
}

/**
 * Retrieves information about the current user
 * @returns {Promise<Array<{id: number, username: string, role: string, projects: Array<number>}>>} Array of user objects
 * @throws {Error} Throws error if the request fails
 * @example
 * // GET /api/user
 * // Response:
 * [
 *   {
 *     "id": 1,
 *     "username": "<EMAIL>",
 *     "role": "user",
 *     "projects": [1, 2]
 *   },
 *   {
 *     "id": 2,
 *     "username": "<EMAIL>",
 *     "role": "maintainer",
 *     "projects": [1, 2, 3, 4]
 *   }
 * ]
 */
export const getCurrentUser = async () => {
    return handleResponse(await fetch(`${API_BASE}/user`, {
        headers: getAuthHeaders()
    }));
};

// PROJECTS

/**
 * Retrieves a list of all projects
 * @returns {Promise<Array<{id: number, name: string, description: string}>>} Array of project objects
 * @throws {Error} Throws error if the request fails
 * @example
 * // GET /api/projects
 * // Response:
 * [
 *   {
 *     "id": 1,
 *     "name": "Titan",
 *     "description": "Titan Embedded Project"
 *   },
 *   {
 *     "id": 2,
 *     "name": "Kodiak",
 *     "description": "Kodiak Car System"
 *   }
 * ]
 */
export const listProjects = async () => {
    return handleResponse(await fetch(`${API_BASE}/projects`, {
        headers: getAuthHeaders()
    }));
};

/**
 * Retrieves details for a specific project
 * @param {number} projectId - The ID of the project to retrieve
 * @returns {Promise<{id: number, name: string, description: string}>} Project object with details
 * @throws {Error} Throws error if the request fails
 * @example
 * // GET /api/projects/1
 * // Input: projectId = 1
 * // Response:
 * {
 *   "id": 1,
 *   "name": "Titan",
 *   "description": "Titan Embedded Project"
 * }
 */
export const getProject = async (projectId) => {
    return handleResponse(await fetch(`${API_BASE}/projects/${projectId}`, {
        headers: getAuthHeaders()
    }));
};

/**
 * Retrieves collections associated with a specific project
 * @param {number} projectId - The ID of the project
 * @returns {Promise<Array<{id: number, project_id: number, name: string, last_updated: string, expiration: string, owner_user_id: number|null}>>} Array of collection objects
 * @throws {Error} Throws error if the request fails
 * @example
 * // GET /api/projects/1/collections
 * // Input: projectId = 1
 * // Response:
 * [
 *   {
 *     "id": 1,
 *     "project_id": 1,
 *     "name": "General_Collection_Titan",
 *     "last_updated": "2024-05-25T14:48:00Z",
 *     "expiration": "2025-05-25T00:00:00Z",
 *     "owner_user_id": null
 *   },
 *   {
 *     "id": 2,
 *     "project_id": 1,
 *     "name": "Custom_Collection_Titan_user_1",
 *     "last_updated": "2024-05-21T09:17:00Z",
 *     "expiration": "2025-09-01T00:00:00Z",
 *     "owner_user_id": 1
 *   }
 * ]
 */
export const getProjectCollections = async (projectId) => {
    return handleResponse(await fetch(`${API_BASE}/projects/${projectId}/collections`, {
        headers: getAuthHeaders()
    }));
};

// KNOWLEDGE COLLECTIONS

/**
 * Retrieves details for a specific knowledge collection
 * @param {number} collectionId - The ID of the collection to retrieve
 * @returns {Promise<{id: number, project_id: number, name: string, last_updated: string, expiration: string, owner_user_id: number|null}>} Collection object with details
 * @throws {Error} Throws error if the request fails
 * @example
 * // GET /api/collections/1
 * // Input: collectionId = 1
 * // Response:
 * {
 *   "id": 1,
 *   "project_id": 1,
 *   "name": "General_Collection_Titan",
 *   "last_updated": "2024-05-25T14:48:00Z",
 *   "expiration": "2025-05-25T00:00:00Z",
 *   "owner_user_id": null
 * }
 */
export const getCollection = async (collectionId) => {
    return handleResponse(await fetch(`${API_BASE}/collections/${collectionId}`, {
        headers: getAuthHeaders()
    }));
};

/**
 * Retrieves all knowledge collections
 * @returns {Promise<Array<{id: number, name: string, description: string, owner: string, privacy: string, createdDate: string, size: string}>>} Array of collection objects
 * @throws {Error} Throws error if the request fails
 * @example
 * // GET /api/collections
 * const collections = await getCollections();
 * // Returns: [
 * //   {
 * //     "id": 1,
 * //     "name": "General_Collection_Titan",
 * //     "description": "General collection for project Titan",
 * //     "owner": "System",
 * //     "privacy": "Shared",
 * //     "createdDate": "2024-01-15",
 * //     "size": "2.3 MB"
 * //   }
 * // ]
 */
export const getCollections = async () => {
    return handleResponse(await fetch(`${API_BASE}/collections`, {
        headers: getAuthHeaders()
    }));
};

// JOBS

/**
 * Retrieves details for a specific session (analyze or training session)
 * @param {number} sessionId - The ID of the session to retrieve
 * @returns {Promise<{id: number, sessionId: string, userId: number, sessionType: 'analyze'|'train', projectId: number, project: string, method: string, SessionStatus: string, knowledgeCollectionId: number, knowledgeCollection: string, logFile: Array<string>, resultMessage: string, resultHTML: string, expire: string, messages?: Array}>} Session object with details (analyze or training session)
 * @throws {Error} Throws error if the request fails
 * @example
 * // GET /api/sessions/1
 * // Input: sessionId = 1
 * // Response: Detailed session object (analyze session)
 * {
 *   "id": 1,
 *   "sessionId": "101",
 *   "userId": 1,
 *   "sessionType": "analyze",
 *   "projectId": 1,
 *   "project": "Titan",
 *   "method": "Neural",
 *   "SessionStatus": "Complete",
 *   "knowledgeCollectionId": 1,
 *   "knowledgeCollection": "General Titan Collection",
 *   "logFile": ["system.log"],
 *   "resultMessage": "Found error on line 123...",
 *   "resultHTML": "URL-HTML-Link...",
 *   "expire": "Date and time when session expires",
 *   "messages": []
 * }
 *
 * // Response: Detailed session object (training session)
 * {
 *   "id": 2,
 *   "sessionId": "102",
 *   "userId": 1,
 *   "sessionType": "train",
 *   "projectId": 2,
 *   "project": "Kodiak",
 *   "method": "n/a",
 *   "SessionStatus": "Complete",
 *   "logFile": ["app.log"],
 *   "resultMessage": "Successful training message...",
 *   "resultHTML": "no HTML link for training sessions",
 *   "expire": "Date and time when session expires"
 * }
 */
export const getJob = async (jobId) => {
    return handleResponse(await fetch(`${API_BASE}/jobs/${jobId}`, {
        headers: getAuthHeaders()
    }));
};

/**
 * Gets the current status of a session with real-time progression
 * @param {string} sessionId - The ID of the session to check status for
 * @returns {Promise<{sessionId: string, status: string, percentage?: number, message: string, resultHTML?: string, resultSummary?: string}>} Session status object
 * @throws {Error} Throws error if the request fails
 * @example
 * // GET /api/sessions/S101/status
 * // Input: sessionId = "S101"
 * // Response (uploading):
 * {
 *   "sessionId": "S101",
 *   "status": "uploading",
 *   "percentage": 45,
 *   "message": "Uploading files... 45%"
 * }
 * // Response (completed):
 * {
 *   "sessionId": "S101",
 *   "status": "completed",
 *   "percentage": 100,
 *   "message": "Session completed successfully",
 *   "resultHTML": "S101-visual-log.html",
 *   "resultSummary": null
 * }
 */
export const getJobStatus = async (jobId) => {
    const url = `${API_BASE}/jobs/${jobId}/status`;
    return handleResponse(await fetch(url, {
        headers: getAuthHeaders()
    }));
};

/**
 * Gets real-time job progress from progress file
 * @param {string} jobId - Job ID to get progress for
 * @returns {Promise<{status: string, progress: number, lines_processed: number, total_lines: number, message: string, updated: string}>} Real-time progress information
 * @throws {Error} Throws error if the request fails
 * @example
 * // GET /api/jobs/{jobId}/progress
 * // Returns:
 * {
 *   "status": "analyzing",
 *   "progress": 45,
 *   "lines_processed": 4500,
 *   "total_lines": 10000,
 *   "message": "Processing... 45%",
 *   "updated": "2025-08-05T10:52:53.504419+00:00"
 * }
 */
export const getJobProgress = async (jobId) => {
    const url = `${API_BASE}/jobs/${jobId}/progress`;
    return handleResponse(await fetch(url, {
        headers: getAuthHeaders()
    }));
};

/**
 * Retrieves a list of sessions (analyze or training sessions), optionally filtered by user ID
 * @param {number} [userId] - Optional user ID to filter sessions
 * @returns {Promise<Array<{id: number, sessionId: string, userId: number, project: string, sessionType: 'analyze'|'train', method: string, knowledgeCollection: string, logFile: string, status: string, resultHTML: string, expire: string, resultSummary: string}>>} Array of session objects (analyze or training sessions)
 * @throws {Error} Throws error if the request fails
 * @example
 * // GET /api/sessions or /api/sessions?user_id=1
 * // Input: userId = 1 (optional)
 * // Response: Array of sessions (both analyze and training sessions)
 * [
 *   {
 *     "id": 1,
 *     "sessionId": "S101",
 *     "userId": 1,
 *     "project": "Titan",
 *     "sessionType": "analyze",
 *     "method": "neural",
 *     "knowledgeCollection": "General_Collection_Titan",
 *     "logFile": "system.log",
 *     "status": "completed",
 *     "resultHTML": "S101-visual-log.html",
 *     "expire": "2025-05-20T17:41:00Z",
 *     "resultSummary": "..."
 *   }
 * ]
 */
export const listJobs = async (userId) => {
    const url = userId
        ? `${API_BASE}/jobs?user_id=${userId}`
        : `${API_BASE}/jobs`;
    return handleResponse(await fetch(url, {
        headers: getAuthHeaders()
    }));
};

/**
 * Sends a message to a specific session (analyze or training session)
 * @param {number} sessionId - The ID of the session to send message to
 * @param {string} text - The message text to send
 * @returns {Promise<{reply: string}>} Response object containing bot reply
 * @throws {Error} Throws error if the request fails
 * @example
 * // POST /api/sessions/1/message
 * // Input: sessionId = 1, text = "What caused this error?"
 * // Request Body:
 * {
 *   "text": "What caused this error?"
 * }
 * // Response:
 * {
 *   "reply": "Echo: What caused this error?"
 * }
 */
export const postJobMessage = async (jobId, text) => {
    return handleResponse(
        await fetch(`${API_BASE}/jobs/${jobId}/message`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                ...getAuthHeaders()
            },
            body: JSON.stringify({text}),
        })
    );
};

/**
 * Upload file for a job session with progress tracking
 * Note: This returns a Promise that resolves when upload starts, not when it completes
 * Use XMLHttpRequest directly for progress tracking in the calling component
 * @param {string} sessionId - The session ID
 * @param {File} file - The file to upload
 * @param {Function} onProgress - Progress callback function
 * @param {Function} onComplete - Completion callback function
 * @param {Function} onError - Error callback function
 * @returns {Promise<XMLHttpRequest>} XMLHttpRequest object for upload control
 */
export const uploadJobFile = (jobId, file, onProgress, onComplete, onError) => {
    return new Promise((resolve, reject) => {
        const formData = new FormData();
        formData.append('logFile', file);

        const xhr = new XMLHttpRequest();

        // Track upload progress
        xhr.upload.onprogress = (event) => {
            if (event.lengthComputable && onProgress) {
                const progress = Math.round((event.loaded / event.total) * 100);
                onProgress(progress, event.loaded, event.total);
            }
        };

        // Handle successful upload
        xhr.onload = () => {
            if (xhr.status === 200) {
                try {
                    const response = JSON.parse(xhr.responseText);
                    if (onComplete) onComplete(response);
                } catch (error) {
                    if (onError) onError('Invalid server response');
                }
            } else {
                let errorMessage = 'Upload failed';
                try {
                    const errorResponse = JSON.parse(xhr.responseText);
                    errorMessage = errorResponse.error || errorMessage;
                } catch (e) {
                    // Use default error message
                }
                if (onError) onError(errorMessage);
            }
        };

        // Handle network errors
        xhr.onerror = () => {
            if (onError) onError('Network error during upload');
        };

        // Handle upload abort
        xhr.onabort = () => {
            if (onError) onError('Upload was cancelled');
        };

        // Start the upload
        xhr.open('POST', `${API_BASE}/jobs/${jobId}/upload`);

        // Add authorization headers using the same method as other API calls
        const authHeaders = getAuthHeaders();
        Object.keys(authHeaders).forEach(key => {
            xhr.setRequestHeader(key, authHeaders[key]);
        });

        xhr.send(formData);
        resolve(xhr); // Return xhr for abort control if needed
    });
};

/**
 * Retrieves a summary message of a specific type
 * @param {string} type - The type of summary message to retrieve (values: "analysis", "train")
 * @returns {Promise<{result: string}>} Summary message object
 * @throws {Error} Throws error if the request fails
 * @example
 * // GET /api/summary-message?type=analysis
 * // Input: type = "analysis"
 * // Response:
 * {
 *   "result": "### Root Cause Analysis\\n\\nThe root cause is around **line #1392**..."
 * }
 *
 * // GET /api/summary-message?type=train
 * // Input: type = "train"
 * // Response:
 * {
 *   "result": "### Training Summary\\n\\n**Status:** Training complete..."
 * }
 */
export const getSummaryMessage = async (type) => {
    const url = `${API_BASE}/summary-message?type=${encodeURIComponent(type)}`;
    return handleResponse(await fetch(url, {
        headers: getAuthHeaders()
    }));
};

/**
 * Creates a new session (analyze or train) with the provided session data
 * @param {Object} sessionData - Complete session configuration object
 * @param {string} sessionData.sessionId - Unique session identifier
 * @param {string} sessionData.userId - User ID who created the session
 * @param {string} sessionData.project - Project name
 * @param {string} sessionData.sessionType - Type of session ('analyze' or 'train')
 * @param {string} sessionData.method - Analysis method ('neural', 'keywords', 'regex')
 * @param {string} sessionData.logFile - Comma-separated list of log file names
 * @param {Array<{name: string, content: string}>} sessionData.logFiles - Array of file objects with name and content
 * @param {string} [sessionData.knowledgeCollection] - Knowledge collection name (for neural method)
 * @param {Array<string>} [sessionData.errorKeywords] - Error keywords (for keywords method)
 * @param {Array<string>} [sessionData.falsePositiveKeywords] - False positive keywords (for keywords method)
 * @param {Array<string>} [sessionData.hintsKeywords] - Hints keywords (for keywords method)
 * @param {Array<string>} [sessionData.successKeywords] - Success keywords (for keywords method)
 * @param {Array<string>} [sessionData.errorRegexes] - Error regexes (for regex method)
 * @param {Array<string>} [sessionData.falseRegexes] - False positive regexes (for regex method)
 * @param {Array<string>} [sessionData.hintsRegexes] - Hints regexes (for regex method)
 * @param {Array<string>} [sessionData.successRegexes] - Success regexes (for regex method)
 * @returns {Promise<{sessionId: string, status: string, message: string}>} Session creation response
 * @throws {Error} Throws error if the request fails
 * @example
 * // POST /api/sessions/start
 * // Input: sessionData = {
 * //   sessionId: "123",
 * //   userId: "user-1",
 * //   project: "Titan",
 * //   sessionType: "analyze",
 * //   method: "neural",
 * //   logFile: "system.log",
 * //   logFiles: [{name: "system.log", content: "log content..."}],
 * //   knowledgeCollection: "General_Collection_Titan"
 * // }
 * // Response:
 * {
 *   "sessionId": "123",
 *   "status": "created",
 *   "message": "Session 123 created successfully"
 * }
 */
export const createJob = async (jobData) => {
    const url = `${API_BASE}/jobs/start`;
    return handleResponse(await fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            ...getAuthHeaders()
        },
        body: JSON.stringify(jobData),
    }));
};

/**
 * Retrieves the current status of a session (analyze or training session)
 * @param {string} sessionId - The ID of the session to check status for
 * @param {string} [sessionType] - Optional session type (analyze or train)
 * @param {string} [method] - Optional method (neural, keywords, regex for analyze sessions)
 * @returns {Promise<{sessionId: string, status: string, percentage?: number, message: string, resultHTML?: string, resultSummary?: string}>} Session status object
 * @throws {Error} Throws error if the request fails
 * @example
 * // GET /api/user-projects?user_id=1
 * // Input: userId = 1
 * // Response:
 * [
 *   {
 *     "id": 1,
 *     "name": "Titan",
 *     "description": "Titan Embedded Project"
 *   },
 *   {
 *     "id": 2,
 *     "name": "Kodiak",
 *     "description": "Kodiak Car System"
 *   }
 * ]
 */
export const getUserProjects = async (userId) => {
    const url = userId
        ? `${API_BASE}/user-projects?user_id=${userId}`
        : `${API_BASE}/user-projects`;
    return handleResponse(await fetch(url, {
        headers: getAuthHeaders()
    }));
};

/**
 * Retrieves knowledge collections associated with a specific project
 * @param {number} projectId - The ID of the project
 * @returns {Promise<Array<{id: number, project_id: number, name: string, last_updated: string, expiration: string, owner_user_id: number|null}>>} Array of knowledge collection objects
 * @throws {Error} Throws error if the request fails
 * @example
 * // GET /api/projects/1/collections
 * // Input: projectId = 1
 * // Response:
 * [
 *   {
 *     "id": 1,
 *     "project_id": 1,
 *     "name": "General_Collection_Titan",
 *     "last_updated": "2024-05-25T14:48:00Z",
 *     "expiration": "2025-05-25T00:00:00Z",
 *     "owner_user_id": null
 *   }
 * ]
 */
export const getProjectKnowledgeCollections = async (projectId) => {
    return handleResponse(await fetch(`${API_BASE}/projects/${projectId}/collections`, {
        headers: getAuthHeaders()
    }));
};

/**
 * Retrieves keyword vocabulary for a specific variant
 * @param {string} [variant='error'] - The variant type (values: 'error', 'false_positive', 'hints', 'success')
 * @returns {Promise<Array<string>>} Array of keyword strings
 * @throws {Error} Throws error if the request fails
 * @example
 * // GET /api/vocab/keywords/error
 * // Input: variant = "error"
 * // Response:
 * [
 *   "abort",
 *   "bad",
 *   "break",
 *   "cannot open",
 *   "corrupt",
 *   "crash",
 *   "critical",
 *   "error",
 *   "exception",
 *   "fail",
 *   "failure",
 *   "fatal"
 * ]
 *
 * // Common usage pattern in AnalysisPage:
 * const fetchKeywordVocab = (variant) => () => api.fetchKeywordVocabulary(variant);
 * // Pass to components: fetchVocabulary={fetchKeywordVocab('error')}
 */
export const fetchKeywordVocabulary = async (variant = 'error') => {
    // Accepts: 'error', 'false_positive', 'hints', 'success'
    return handleResponse(await fetch(`${API_BASE}/vocab/keywords/${variant}`, {
        headers: getAuthHeaders()
    }));
};

/**
 * Retrieves regex vocabulary for a specific variant
 * @param {string} [variant='error'] - The variant type (values: 'error', 'false_positive', 'hints', 'success')
 * @returns {Promise<Array<string>>} Array of regex pattern strings
 * @throws {Error} Throws error if the request fails
 * @example
 * // GET /api/vocab/regex/error
 * // Input: variant = "error"
 * // Response:
 * [
 *   "\\b(error|exception|failed)\\b",
 *   "\\b(abort|crash|fatal)\\b",
 *   "\\b(cannot|unable|failed)\\s+to\\s+\\w+",
 *   "\\berror\\s*code\\s*:\\s*\\d+"
 * ]
 *
 * // Common usage pattern in AnalysisPage:
 * const fetchRegexVocab = (variant) => () => api.fetchRegexVocabulary(variant);
 * // Pass to components: fetchVocabulary={fetchRegexVocab('error')}
 */
export const fetchRegexVocabulary = async (variant = 'error') => {
    // Accepts: 'error', 'false_positive', 'hints', 'success'
    return handleResponse(await fetch(`${API_BASE}/vocab/regex/${variant}`, {
        headers: getAuthHeaders()
    }));
};

/**
 * Save keyword vocabulary for a specific variant
 * @param {string} variant - The variant type ('error', 'false_positive', 'hints', 'success')
 * @param {Array<string>} keywords - Array of keyword strings to save
 * @returns {Promise<Object>} Success response
 */
export const saveKeywordVocabulary = async (variant, keywords) => {
    return handleResponse(await fetch(`${API_BASE}/vocab/keywords/${variant}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
            ...getAuthHeaders()
        },
        body: JSON.stringify(keywords)
    }));
};

/**
 * Save regex vocabulary for a specific variant
 * @param {string} variant - The variant type ('error', 'false_positive', 'hints', 'success')
 * @param {Array<string>} regexes - Array of regex strings to save
 * @returns {Promise<Object>} Success response
 */
export const saveRegexVocabulary = async (variant, regexes) => {
    return handleResponse(await fetch(`${API_BASE}/vocab/regex/${variant}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
            ...getAuthHeaders()
        },
        body: JSON.stringify(regexes)
    }));
};

/**
 * Reset keyword vocabulary to defaults for a specific variant
 * @param {string} variant - The variant type ('error', 'false_positive', 'hints', 'success')
 * @returns {Promise<Object>} Success response
 */
export const resetKeywordVocabulary = async (variant) => {
    return handleResponse(await fetch(`${API_BASE}/vocab/keywords/${variant}`, {
        method: 'DELETE',
        headers: getAuthHeaders()
    }));
};

/**
 * Reset regex vocabulary to defaults for a specific variant
 * @param {string} variant - The variant type ('error', 'false_positive', 'hints', 'success')
 * @returns {Promise<Object>} Success response
 */
export const resetRegexVocabulary = async (variant) => {
    return handleResponse(await fetch(`${API_BASE}/vocab/regex/${variant}`, {
        method: 'DELETE',
        headers: getAuthHeaders()
    }));
};

/**
 * API service object containing all API functions for grouped imports
 * @type {Object}
 * @property {Function} loginUser - Authenticates user with credentials
 * @property {Function} getCurrentUser - Retrieves current user information
 * @property {Function} listProjects - Lists all projects
 * @property {Function} getProject - Gets specific project details
 * @property {Function} getProjectCollections - Gets collections for a project
 * @property {Function} getCollection - Gets specific collection details
 * @property {Function} listJobs - Lists jobs (optionally filtered by user)
 * @property {Function} getJob - Gets specific job details
 * @property {Function} getJobStatus - Gets job status with real-time progression
 * @property {Function} postJobMessage - Sends message to a job
 * @property {Function} getSummaryMessage - Gets summary message by type
 * @property {Function} createJob - Creates a new job (analyze or train) - POST /api/jobs/start
 * @property {Function} getUserProjects - Gets projects for a user
 * @property {Function} getProjectKnowledgeCollections - Gets knowledge collections for a project
 * @property {Function} fetchKeywordVocabulary - Gets keyword vocabulary by variant
 * @property {Function} fetchRegexVocabulary - Gets regex vocabulary by variant
 */

/**
 * Refreshes the authentication token to extend the session
 * @returns {Promise<boolean>} True if token was refreshed successfully
 */
export async function refreshToken() {
    try {
        const response = await fetch(`${API_BASE}/refresh-token`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                ...getAuthHeaders()
            }
        });

        if (!response.ok) {
            throw new Error('Failed to refresh token');
        }

        const data = await response.json();

        // Update the token in localStorage
        localStorage.setItem(AUTH_TOKEN_KEY, data.access_token);

        console.log('Token refreshed successfully');
        return true;
    } catch (error) {
        console.error('Token refresh error:', error);
        return false;
    }
}

/**
 * Check if token is expired without refreshing it
 * @returns {boolean} True if token is valid (not expired), false otherwise
 */
export function isTokenExpired() {
    const token = localStorage.getItem(AUTH_TOKEN_KEY);
    if (!token) return true; // No token means it's effectively expired

    try {
        // Import jwt-decode
        const {jwtDecode} = require('jwt-decode');

        // Decode the token
        const decodedToken = jwtDecode(token);

        // Check if token has expired
        const currentTime = Date.now() / 1000;
        if (decodedToken.exp && decodedToken.exp < currentTime) {
            console.log('Token has expired');
            return true;
        }

        // Token is still valid
        return false;
    } catch (error) {
        console.error('Token validation error:', error);
        return true; // If we can't validate, assume it's expired for safety
    }
}

const api = {
    loginUser,
    getCurrentUser,
    listProjects,
    getProject,
    getProjectCollections,
    getCollection,
    listJobs,
    getJob,
    getJobStatus,
    getJobProgress,
    postJobMessage,
    getSummaryMessage,
    createJob,
    getUserProjects,
    getProjectKnowledgeCollections,
    fetchKeywordVocabulary,
    fetchRegexVocabulary,
    saveKeywordVocabulary,
    saveRegexVocabulary,
    resetKeywordVocabulary,
    resetRegexVocabulary,
    refreshToken,
    validateToken,
    isTokenExpired,
    isAuthenticated,
};

export default api;
