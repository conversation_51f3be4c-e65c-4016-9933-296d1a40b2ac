# KnutGPT React Frontend

A modern React application for AI-powered log analysis, providing an intuitive interface for analyzing system logs using various methods including keyword search, regex patterns, and neural networks.

## Overview

KnutGPT is a log analysis tool that helps developers and system administrators quickly identify issues, patterns, and insights from system logs. The frontend provides a user-friendly interface for:

- **Log Analysis**: Upload and analyze log files using different methods
- **Training Sessions**: Train custom models on your specific log patterns
- **Knowledge Collections**: Manage and organize analysis patterns and rules
- **Session Management**: Track analysis history and results
- **Project Organization**: Organize work by projects and teams

## Architecture

### Tech Stack
- **React 18** - Modern React with hooks and functional components
- **React Router** - Client-side routing for SPA navigation
- **Tailwind CSS** - Utility-first CSS framework for styling
- **Context API** - State management for user authentication
- **Fetch API** - HTTP client for backend communication

### Project Structure
```
src/
├── components/          # Reusable UI components
├── context/             # React Context providers
│   └── UserContext.jsx  # User authentication state
├── pages/               # Page components (routes)
│   ├── LoginPage.jsx    # User authentication
│   ├── HomePage.jsx     # Dashboard/landing
│   ├── AnalysisPage.jsx # Log analysis interface
│   ├── TrainPage.jsx    # Training interface
│   ├── JobPage.jsx      # Job details/chat
│   └── ...
├── services/           # External service integrations
│   └── api.js          # Backend API client
├── App.jsx             # Main app component with routing
└── index.js            # React app entry point
```

## Key Features

### Authentication & User Management
- Login/logout functionality
- User context management
- Project-based access control

### Analysis Methods
- **Keywords**: Search for specific terms and patterns
- **Regex**: Use regular expressions for complex pattern matching  
- **Neural**: AI-powered analysis using trained models

### Session Types
- **Analyze**: Real-time log analysis with results
- **Train**: Create and train custom analysis models

### UI/UX Features
- Responsive design with Tailwind CSS
- File upload with drag-and-drop
- Real-time status updates
- Interactive result visualization

## Getting Started

### Prerequisites
- Node.js 16+ 
- npm or yarn
- Backend server running (see mock_server/)

### Installation
```bash
# Install dependencies
npm install

# Start development server
npm start

# Build for production
npm run build
```

### Environment Configuration
Create a `.env` file in the root directory:
```env
REACT_APP_API_URL=http://localhost:5001/api
```

## 🔌 API Integration

The frontend communicates with the backend through a centralized API service (`src/services/api.js`):

### Main API Endpoints
- `POST /api/login` - User authentication
- `GET /api/projects` - List user projects
- `GET /api/collections` - Knowledge collections
- `POST /api/sessions/start` - Start analysis/training
- `GET /api/sessions/:id/status` - Real-time session status

### Error Handling
All API calls include centralized error handling with user-friendly error messages and proper HTTP status code handling.

## Styling & Theming

### Tailwind Configuration
- Custom font stack (Noto Sans, Lato, Roboto)
- Extended color palette for KnutGPT branding
- Custom animations (wobble effects)
- Responsive breakpoints

### Font Loading
Optimized Google Fonts loading:
- Preconnect for performance
- Comprehensive font families
- Fallback fonts for reliability

## Pages & Components

### Core Pages
- **LoginPage**: Authentication interface
- **HomePage**: Dashboard with recent sessions and quick actions
- **AnalysisPage**: Main analysis interface with file upload
- **TrainPage**: Training session configuration
- **SessionPage**: Session details with chat interface
- **KnowledgeCollectionPage**: Manage analysis patterns

### Key Components
- File upload with validation
- Progress indicators for long-running operations
- Result visualization and export
- Chat interface for session interaction

## Security Considerations

- Client-side authentication state management
- Secure API communication
- File upload validation
- XSS protection through React's built-in escaping

## Development

### Common Patterns
- Use the `useUser()` hook for authentication state
- Centralize API calls through `api.js`
- Follow Tailwind utility-first approach
- Use React Router for navigation

### Environment Variables
- `REACT_APP_API_URL`: Backend API base URL
- Additional config can be added as needed

## Contributing

1. Follow React best practices
2. Use TypeScript-like prop validation
3. Keep components small and focused
4. Test API integrations thoroughly
5. Follow the existing code style and patterns

## State Management

The app uses React Context for global state:
- **UserContext**: User authentication and profile data
- Local component state for UI interactions
- API response caching where appropriate
