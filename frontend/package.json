{"name": "knut-gpt-app", "version": "0.1.0", "private": true, "dependencies": {"autoprefixer": "^10.4.20", "cross-env": "^7.0.3", "crypto-js": "^4.2.0", "framer-motion": "^6.5.1", "jwt-decode": "^4.0.0", "lucide-react": "^0.260.0", "postcss": "^8.5.2", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.30.1", "react-scripts": "5.0.1", "tailwindcss": "^3.4.17"}, "scripts": {"start": "cross-env PORT=3123 react-scripts start", "start:debug": "cross-env PORT=3123 BROWSER=none GENERATE_SOURCEMAP=true REACT_APP_DEBUG=true react-scripts start --verbose", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11"}}