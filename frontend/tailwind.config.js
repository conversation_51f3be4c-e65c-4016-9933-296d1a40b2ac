module.exports = {
  content: [
    "./src/**/*.{js,jsx,ts,tsx}",
  ],
  theme: {
    extend: {
      fontFamily: {
        sans: [
          'Noto Sans', 
          'Lato', 
          '-apple-system', 
          'BlinkMacSystemFont', 
          '"Segoe UI"', 
          'Roboto', 
          'Oxygen',
          'Ubuntu', 
          'Cantarell', 
          '"Fira Sans"', 
          '"Droid Sans"', 
          '"Helvetica Neue"',
          'sans-serif'
        ],
      },
      keyframes: {
        wobble: {
          '0%, 100%': { transform: 'rotate(-3deg)' },
          '50%': { transform: 'rotate(3deg)' },
        },
      },
      animation: {
        wobble: 'wobble 0.5s ease-in-out infinite',
      },
    },
  },
  plugins: [],
}