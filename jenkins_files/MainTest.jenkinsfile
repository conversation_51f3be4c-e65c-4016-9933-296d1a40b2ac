/* groovylint-disable LineLength */

// Note: Add environment variables used are by default loaded from configs/default.properties
// Some of these are mandatory to specify for jobs, and are not set on default.properties, to avoid missing to set them correctly. These are validated below.
def assertMandatory(varValue, failMessage) {
    assert varValue != null && varValue.length() > 0: "ERROR. Jenkins configuration is missing a mandatory environment variable: " + failMessage
}

assertMandatory(env.DEV_ASSISTANT_BOT_EMAIL, "DEV_ASSISTANT_BOT_EMAIL. Must be set to the email of the <PERSON> user that executes this groovy script to avoid risk of the BOT triggering itself. Example: <EMAIL>")
assertMandatory(env.DEV_ASSISTANT_BOT_TRIGGER_WORD, "DEV_ASSISTANT_BOT_TRIGGER_WORD. Must match the word set in the Jenkins Job configuration 'Comment Added Contains Regular Expression'. Example: @KnutGPT")
assertMandatory(env.DEV_ASSISTANT_KNOWLEDGE_COLLECTION_PREFIX, "DEV_ASSISTANT_KNOWLEDGE_COLLECTION_PREFIX. Name of the default collection for good log lines")

// Load Jenkins library for utilities
library env.DEV_ASSISTANT_JENKINS_SHARED_LIB

import java.util.regex.Matcher
import java.util.regex.Pattern

// Config
def FAKEBUILD_COMMENT_MARKER = "(fake build generated by KnutGPT fakebuild command)"
def VISUAL_LOG_FILE = "KnutGPT_visual_log.html"
def BENCHTEST_FILE = "KnutGPT_benchtest_result.html"
def RESPONSE_FILE = "KnutGPT_response.txt"
def responseMessage = "" // The complete response of the analysis
def DEMO_DFS_FOLDER = "file://bosch.com/DfsRB/DfsSE/loc/LUD/knutgpt/"
def ACCEPTED_TASK_JOB_URLS = ["https://rb-jmaas.de.bosch.com", "https://gitlab-apertispro.boschdevcloud.com", DEMO_DFS_FOLDER]
def JENKINS_URL_IDENTIFYER = "jmaas" // if url contains this keyword, it is a Jenkins server
def ACCEPTED_GROUNDTRUTH_URL = "https://rbcm-gerrit.de.bosch.com/plugins/gitiles/tools/lundops/log-groundtruth-rb-proprietary"
def DEFAULT_GOOD_KNOWLEDGE_COLLECTION = "good_" + env.DEV_ASSISTANT_KNOWLEDGE_COLLECTION_PREFIX + "_default"

// Global variables
def inputCommand = null
def wasTriggeredByGerritComment = false
def response_vote = ""
def task = "analyze"
def taskJobURL = null
def taskLogfileURL = ""
def commit_id
def cause = ''
def upstream_url = ''
def collectionName = null
def reviewChange = null

Pattern urlPattern = Pattern.compile("\\b(https?|ftp|file)://[-a-zA-Z0-9+&@#/%?=~_|!:,.;]*[-a-zA-Z0-9+&@#/%=~_|]")  // Used to extract urls from strings
// TODO: Note, Does it handle case insensitivity? Is it necessary to add ,Pattern.CASE_INSENSITIVE

def COMMAND_LIST =
        "    analyze\n" +
                "    analyze <url> The url to a text file to analyze or train. Must be on one of these locations: " + ACCEPTED_TASK_JOB_URLS.toString().replace("[", "").replace("]", "").replace(",", " or ") + "\n" +
                "    <prompt> Any free text promt to send to KnutGPT.\n" +
                "    feedback helpful yes|no\n" +
                "    feedback comment <free_comment_about_KnutGPT>   To report bugs, request a feature, etc.\n" +
                "    feedback savedtime <in_minutes>                 The time you estimate this specific log analysis saved you\n" +
                "    feedback deletehelpful|deletecomment|deletesavedtime          Deletes your feedback entry from the database\n" +
                "    review    Perform a code review of the gerrit change that the job was invoked from.\n" +
                "    help"

def HELP_MESSAGE =
        "Hej!\n" +
                "I am KnutGPT, your friendly developer assistant.\n" +
                "You can say the following to me:\n\n" +
                COMMAND_LIST + "\n\n" +
                "... or ask me anything you like in free text :)\n\n" +
                "Info: https://knutgpt.bosch.com   (C)2024 Lund Software Acceleration Team"

def FEEDBACK_HELP_MESSAGE =
        "To provide feedback, please post comments with the following format:\n" +
                "   feedback helpful yes|no                         Did the analysis info help you?\n" +
                "   feedback comment <free comment about KnutGPT>   You can report bugs, request a feature, etc.\n" +
                "   feedback savedtime <in_minutes>                 The time you estimate this specific log analysis saved you\n" +
                "   feedback deletehelpful          Deletes your helpful feedback entry from the database\n" +
                "   feedback deletecomment          Deletes your comment entry from the database\n" +
                "   feedback deletesavedtime        Deletes your savedtime entry from the database"

// Log Analyzer methods and their parameters
def analyzerMethodMap = [
        "keywords"       : "--analysis_type keywords",
        "patterns"       : "--analysis_type patterns --match_at 0.8",
        "neural"         : "--analysis_type neural --match_at 0.90",
        "neural_training": "--analysis_type neural --training_mode --match_at 0.98"]
def analyzerRagParameter = "--rag --ground_truth_folder ./log_analyzer/samples/"

properties([
        parameters([
                string(name: 'INPUT_COMMAND', defaultValue: null, description: 'URL or Input command to dev-assistant. Corresponds to the user comment that is provided when posting Gerrit comments ' + env.DEV_ASSISTANT_BOT_TRIGGER_WORD + ' <input_command>.\nCommand list:\n\n' + COMMAND_LIST + '\n\n', trim: true),
                text(name: 'RAW_LOG', defaultValue: '', description: '(Optional) Raw log text. Overrides any URL specified in the INPUT_COMMAND field. Note: text length is limited by Jenkins.', trim: false),
                choice(name: 'LOG_ANALYZER_METHOD', choices: analyzerMethodMap.keySet().join("\n"), description: 'Method to use by Log Analyzer', defaultValue: 'keywords'),
                string(name: 'GOOD_KNOWLEDGE_COLLECTION', defaultValue: DEFAULT_GOOD_KNOWLEDGE_COLLECTION, description: '(Optional) The Vector DB collection of good lines to use for neural analysis. For this Jenkins instance all collections must start with good_' + env.DEV_ASSISTANT_KNOWLEDGE_COLLECTION_PREFIX + '_', trim: true),
        ]),
        buildDiscarder(logRotator(numToKeepStr: '20')),
])

node(env.DEV_ASSISTANT_JENKINS_NODE_LABEL) {
    cleanWs()

    stage('Fetch local build log to inspect potential upstream job') {
        try {
            withCredentials([usernamePassword(credentialsId: env.DEV_ASSISTANT_LOG_DOWNLOAD_CREDENTIALS_ID, usernameVariable: 'USERNAME', passwordVariable: 'PASSWORD')]) {
                echo "${BUILD_URL}"
                sh "curl -u ${USERNAME}:${PASSWORD} ${BUILD_URL}/api/json -o buildlog.json"
                archiveArtifacts artifacts: 'buildlog.json', allowEmptyArchive: true
                def jsonString = readFile 'buildlog.json'
                def jsonObj = readJSON text: jsonString
                println jsonObj
                upstream_job = jsonObj.actions.causes.upstreamUrl
                upstream_job = upstream_job.toString().replace('[', '').replace(']', '')
                upstream_job = upstream_job.replaceAll(/,.*$/, '')

                upstream_buildNbr = jsonObj.actions.causes.upstreamBuild
                upstream_buildNbr = upstream_buildNbr.toString().replace('[', '').replace(']', '')
                upstream_buildNbr = upstream_buildNbr.replaceAll(/,.*$/, '')

                upstream_url = "${env.JENKINS_URL}${upstream_job}${upstream_buildNbr}"
                println upstream_url
                println jsonObj.actions.causes.shortDescription
                cause = jsonObj.actions.causes.shortDescription
                cause = cause.toString().replace('[', '').replace(']', '')
                println cause
                archiveArtifacts artifacts: 'buildlog.json', allowEmptyArchive: true
            }
        } catch (Exception e) {
            println("Failed to download buildlog.json. Note that this is only critical if job was triggered by upstream. Exception: " + e.message)
        }
    }

    stage('Get input command') {
        sh "export"
        // Job triggered by user comment in Gerrit
        if (env.GERRIT_EVENT_ACCOUNT_EMAIL?.trim() && env.GERRIT_EVENT_COMMENT_TEXT != null) {
            // Sanity check if trigger comment was sent by bot. We dont want it to trigger itself and end up in infinite loops!
            if (env.GERRIT_EVENT_ACCOUNT_EMAIL.toLowerCase().trim() == env.DEV_ASSISTANT_BOT_EMAIL.toLowerCase()) {
                currentBuild.result = 'ABORTED'
                error("Job was triggered by Gerrit comment from BOT user " + env.DEV_ASSISTANT_BOT_EMAIL.toLowerCase() + ", aborting to avoid loop!")
            }
            // Sanity check that neccessary Gerrit parameters exist
            if (!params.GERRIT_CHANGE_NUMBER?.trim() || !params.GERRIT_PATCHSET_NUMBER?.trim()) {
                currentBuild.result = 'ABORTED'
                error("Job was triggered by Gerrit comment but not all needed GERRIT parameters have values.")
            }
            println("Job was triggered by comment from user: ${GERRIT_EVENT_ACCOUNT_EMAIL}")
            println "Gerrit event comment text = ${env.GERRIT_EVENT_COMMENT_TEXT}"
            inputCommand = env.GERRIT_EVENT_COMMENT_TEXT
            println "inputCommand from Gerrit event = ${inputCommand}"
            wasTriggeredByGerritComment = true
        } else if (cause.startsWith("Started by upstream project")) {
            println("Job was triggered by an upstream pipeline")
            inputCommand = "analyze ${upstream_url}"
            currentBuild.displayName = upstream_url
        } else if (params.INPUT_COMMAND != null) {
            // Not triggered by user comment. Use the INPUT_COMMAND job parameter as command
            println("Job was triggered manually or by pipeline (Not by Gerrit comment).")
            inputCommand = params.INPUT_COMMAND
        } else {
            currentBuild.result = 'ABORTED'
            error('Job was not triggered by a user comment and INPUT_COMMAND was null. Nothing to do. Aborting.')
        }

        // Trim and remove trigger word
        println("inputCommand before cleaning: " + inputCommand)
        inputCommand.trim()
        if (inputCommand.contains(env.DEV_ASSISTANT_BOT_TRIGGER_WORD)) {
            inputCommand = inputCommand.substring(Math.min((inputCommand.indexOf(env.DEV_ASSISTANT_BOT_TRIGGER_WORD) + env.DEV_ASSISTANT_BOT_TRIGGER_WORD.length()), inputCommand.length()));
        }
        inputCommand = inputCommand.trim()
        if (inputCommand.startsWith(":")) {
            inputCommand -= ":"
        } else if (inputCommand.startsWith(";")) {
            inputCommand -= ";"
        }
        inputCommand = inputCommand.trim()
        currentBuild.displayName = inputCommand ?: "n/a"
    }

    stage('Identify task') {
        hasAcceptedTaskURL = false
        for (acceptedURL in ACCEPTED_TASK_JOB_URLS) {
            if (inputCommand.contains(acceptedURL)) {
                hasAcceptedTaskURL = true
                break
            }
        }
        if (params.RAW_LOG != null && params.RAW_LOG.length() > 0) { // If RAW_LOG, assume the task is to analyze
            task = "analyze"
            currentBuild.displayName = "Analysis of raw text"
        } else if (env.LOG_ANALYZER_EXECUTE_BENCHTEST == "true") {
            task = "benchtest"
            currentBuild.displayName = "benchtest"
        } else if (!inputCommand?.trim() || inputCommand == "help") {
            // empty or null string or user wrote help, just show welcome and help text
            task = "help"
        } else if (inputCommand.startsWith("analyze")) {
            task = "analyze"
        } else if (inputCommand.startsWith("feedback")) {
            task = "feedback"
        } else if (inputCommand.startsWith("fakebuild")) {
            task = "fakebuild"
        } else if (inputCommand.startsWith("review")) {
            task = "review"
        } else if (hasAcceptedTaskURL) { // If just accepted url in input command, also treat as analysis
            task = "analyze"
        } else { // Treat any other text as a general prompt
            task = "prompt"
        }
        println("Identified task: " + task)
    }


    // Set up necessary variables for neural modes and assert knowledge collection name
    if (params.LOG_ANALYZER_METHOD == "neural" || params.LOG_ANALYZER_METHOD == "neural_training") {
        collectionName = params.GOOD_KNOWLEDGE_COLLECTION.toLowerCase()
        if (params.LOG_ANALYZER_METHOD == "neural") {
            if (params.GOOD_KNOWLEDGE_COLLECTION == null || params.GOOD_KNOWLEDGE_COLLECTION.length() == 0) {
                currentBuild.displayName = "invalid input parameters"
                error("GOOD_KNOWLEDGE_COLLECTION parameter must be specified for neural analysis mode.")
            }
            currentBuild.displayName = "Neural analysis of " + params.INPUT_COMMAND
        }
        if (params.LOG_ANALYZER_METHOD == "neural_training") {
            if (params.GOOD_KNOWLEDGE_COLLECTION == null || !params.GOOD_KNOWLEDGE_COLLECTION.startsWith("good_" + env.DEV_ASSISTANT_KNOWLEDGE_COLLECTION_PREFIX + "_")) {
                currentBuild.displayName = "invalid input parameters"
                error("GOOD_KNOWLEDGE_COLLECTION parameter must be specified for neural_training mode. It must start with good_" + env.DEV_ASSISTANT_KNOWLEDGE_COLLECTION_PREFIX + "_")
            }
            currentBuild.displayName = "Training of " + collectionName
        }
        println("Using knowledge collection: " + collectionName)
    }

    // Clone log-analyzer repos if needed
    if (task == "analyze" || task == "prompt" || task == "benchtest" || task == "review") {
        stage('Clone Log Analyzer repositories from Github. Revisions: ' + env.LOG_ANALYZER_GIT_REVISION + ', ' + env.LLM_CLIENT_GIT_REVISION) {
            withCredentials([gitUsernamePassword(credentialsId: "${env.LOG_ANALYZER_BDC_CREDENTIALS_ID}")]) {
                sh """#!/bin/bash
                    git clone --depth 1 --branch main https://github.boschdevcloud.com/lund-common/dev-assistant.git &
                    git clone --depth 1 --branch ${env.LOG_ANALYZER_GIT_REVISION} https://github.boschdevcloud.com/lund-common/log-analyzer.git &
                    git clone --depth 1 --branch ${env.LLM_CLIENT_GIT_REVISION} https://github.boschdevcloud.com/lund-common/llm-client.git &
                    git clone --depth 1 --branch master ${env.DEV_ASSISTANT_JENKINS_SHARED_LIB_GIT_URL}
                    wait
                """
            }
            if (!fileExists("log-analyzer") || !fileExists("llm-client")) {
                error("Failed to clone necessary git repos. Aborting.")
            }
        }
    }

    // For Code review task, download the code snippet
    // TODO: Use https://gerrit-review.googlesource.com/Documentation/rest-api-changes.html#get-patch
    // 'GET /changes/{change-id}/revisions/{revision-id}/patch'         Gets the formatted patch for one revision.
    // Request   GET /changes/myProject~master~I8473b95934b5732ac55d26311a706c9c2bde9940/revisions/current/patch HTTP/1.0
    if (task == "review" && params.GERRIT_CHANGE_NUMBER?.trim()) {
        // If no URL in trigger comment, look for it in the change comments
        reviewPatchURL = "https://rbcm-gerrit.de.bosch.com/changes/${GERRIT_CHANGE_NUMBER}/revisions/current/patch"
        sh """#!/bin/bash
            ssh -p 29418 rbcm-gerrit.de.bosch.com gerrit query ${GERRIT_CHANGE_NUMBER} --format=JSON --current-patch-set > change.json
        """
/*
        withCredentials([usernamePassword(credentialsId: env.DEV_ASSISTANT_LOG_DOWNLOAD_CREDENTIALS_ID, usernameVariable: 'USERNAME', passwordVariable: 'PASSWORD')]) {
            echo "${reviewPatchURL}"
            sh "curl -u ${USERNAME}:${PASSWORD} ${reviewPatchURL} -o review_patchfile"
        }
*/
        withEnv(["url=$reviewPatchURL"]) {
            try {
                def response = httpRequest(url: url)
                writeFile(file: 'review_patchfile', text: response.content)
            } catch (Exception e) {
                println("Error when downloading patchfile to review. Exception: " + e.message)
                // Make sure the file is cleared to not confuse the user. If there are leftovers in the file at download failures,
                // the Log Analyzer will analyze it and report it to the user as if it was the real file
                writeFile file: "review_patchfile", text: ""
            }
        }

        // Now read the change data for review comments and the patch for changed code snipets
        reviewChange = readJSON file: 'change.json'
        archiveArtifacts "change.json"
        if (fileExists("review_patchfile")) {
            archiveArtifacts "review_patchfile"
        } else {
            println("review_patchfile does not exist.")
        }
        /*
        def comments = change.comments.reverse()    // flip comments array to reverse order, to check comments from latest to oldest
        comments.find {
            // Ignore all comments from the bot except if from fakebuild
            for(acceptedURL in ACCEPTED_TASK_JOB_URLS) {
                if (it.message.contains(acceptedURL) && (it.reviewer.username != DEV_ASSISTANT_BOT_USERNAME || it.message.contains(FAKEBUILD_COMMENT_MARKER))) { // TODO: Add support for a list of known and supported (and trusted) log file locations
                    println "Found a review comment containing a job URL: $it.message"
                    // Extract the URL from message
                    Matcher matcher = urlPattern.matcher(it.message);
                    while (matcher.find()) {
                        taskJobURL = matcher.group()
                    }
                    return true // break as soon as the first URL is found because it is the latest we are after
                }
                return false // keep looping to the next review comment
            }
        }
        */
    }

    // Get the Jenkins job URL to analyze
    // Example: https://rb-jmaas.de.bosch.com/XC_CI1_CCS/job/utils/job/demo_and_test/job/jku1lud_pipelines/job/swacc_ai_poc1/216/
    // Reference: Json structure of comments: https://gerrit-review.googlesource.com/Documentation/rest-api-changes.html#list-change-comments
    if (task == "analyze" || task == "fakebuild") {
        stage('Get job URL') {
            for (acceptedURL in ACCEPTED_TASK_JOB_URLS) {
                if (inputCommand.contains(acceptedURL) || inputCommand.contains(ACCEPTED_GROUNDTRUTH_URL)) {
                    Matcher matcher = urlPattern.matcher(inputCommand);
                    while (matcher.find()) {
                        taskJobURL = matcher.group()
                        println("Found a build job URL in the trigger comment.");
                    }
                }
                if (taskJobURL) { // URL found, stop looping the ACCEPTED_TASK_JOB_URLS
                    break
                }
            }
            if (!taskJobURL && task == "analyze" && params.GERRIT_CHANGE_NUMBER?.trim()) {
                // If no URL in trigger comment, look for it in the change comments
                sh """#!/bin/bash
                    ssh -p 29418 rbcm-gerrit.de.bosch.com gerrit query ${GERRIT_CHANGE_NUMBER} --format=JSON --comments > change.json
                """
                // Now read the change data for comments
                def change = readJSON file: 'change.json'
                def comments = change.comments.reverse()
                // flip comments array to reverse order, to check comments from latest to oldest
                comments.find {
                    // Ignore all comments from the bot except if from fakebuild
                    for (acceptedURL in ACCEPTED_TASK_JOB_URLS) {
                        if (it.message.contains(acceptedURL) && (it.reviewer.username != env.USER || it.message.contains(FAKEBUILD_COMMENT_MARKER))) {
                            // TODO: Add support for a list of known and supported (and trusted) log file locations
                            println "Found a review comment containing a job URL: $it.message"
                            // Extract the URL from message
                            Matcher matcher = urlPattern.matcher(it.message);
                            while (matcher.find()) {
                                taskJobURL = matcher.group()
                            }
                            return true // break as soon as the first URL is found because it is the latest we are after
                        }
                        return false // keep looping to the next review comment
                    }
                }
            }

            // Clean the job URL, so that it points to the job's base URL
            if (taskJobURL) {
                taskJobURL -= "consoleText" // If user wrote the URL to the raw console text (..jobname/30/consoleText)
                taskJobURL -= "console" // If user wrote the URL to the console (..jobname/30/console)
                taskJobURL -= "?format=TEXT"
                // If pointing to a log file in groundtruth repo, clean potential format parameter
                println("Identified taskJobURL: " + taskJobURL);

                // Set the log file URL to download
                taskLogfileURL = taskJobURL
                if (taskLogfileURL.contains(JENKINS_URL_IDENTIFYER)) {
                    // For Jenkins log URLs add consoleText to get raw text
                    if (!taskLogfileURL.endsWith(".txt") && !taskLogfileURL.endsWith(".log") && !taskLogfileURL.contains("consoleText")) {
                        if (!taskLogfileURL.endsWith("/")) {
                            taskLogfileURL += "/"
                        }
                        taskLogfileURL += "consoleText"
                    }
                } else if (taskLogfileURL.contains(ACCEPTED_GROUNDTRUTH_URL)) {
                    // For log files in ground truth repo, add URL parameter to download as text
                    if (!taskLogfileURL.endsWith(".txt") && !taskLogfileURL.endsWith(".log") && !taskLogfileURL.endsWith("?format=TEXT")) {
                        taskLogfileURL += "?format=TEXT"
                    }
                }
            }
        }
    }

    // Download the build log if needed
    // Write it to a file: logfile_full
    if (task == "analyze") {
        stage('Download build log') {
            if (params.RAW_LOG != null && params.RAW_LOG.length() > 0) {
                println("RAW_LOG was set, use it as input for analysis. Ignore any previously identified URL.")
                writeFile(file: 'logfile_full', text: params.RAW_LOG)
                archiveArtifacts "logfile_full"
                return
            }
            if (env.LOG_ANALYZER_DISABLE_LOGFILE_DOWNLOAD == "true") {
                println "DISABLE_LOGFILE_DOWNLOAD set to true. Skipping download. Using hard-coded log content:"
                sh """#!/bin/bash
                    echo "[2021-08-30T06:13:50.311Z] 000FFFFCDA0  00180048A2A (00000000000, 00000000000, 00000000000, 00000000000)" >> logfile_full
                    echo "[2021-08-30T06:13:50.311Z] 000FFFFCE50  00180048AEC (00000000000, 00000000000, 00000000000, 00000000000)" >> logfile_full
                    echo "[2021-08-30T06:13:50.311Z] End of stack trace" >> logfile_full
                    echo "[2021-08-30T06:13:50.311Z] fatal: Could not read from remote repository." >> logfile_full
                    echo "[2021-08-30T06:13:50.311Z] " >> logfile_full
                    echo "[2021-08-30T06:13:50.311Z] Please make sure you have the correct access rights" >> logfile_full
                    echo "[2021-08-30T06:13:50.311Z] and the repository exists." >> logfile_full
                    cat logfile_full
                """
                return
            }
            if (!taskJobURL) {
                responseMessage += "I could not find any URL that I recognize for logs in the comments.\n" +
                        "I can look for logs in jobs at ${ACCEPTED_TASK_JOB_URLS}."
                return
            }
            if (taskJobURL.contains(DEMO_DFS_FOLDER)) {
                println("Log URL is a mounted DEMO DFS, copying log file from mountpoint")
                filePath = taskJobURL.split(DEMO_DFS_FOLDER).last() // Keep the text after the DFS url
                if (filePath.contains("..")) {
                    responseMessage += "Log file path is unpermitted. Contains .."
                    return
                }
                localPath = "/mnt/knutmount/" + filePath
                sh """#!/bin/bash
                    cd /mnt/
                    cd knutmount/
                """
                sh """#!/bin/bash
                    echo "copying from ${localPath} "
                    cp ${localPath} "./logfile_full"
                """
                if (!fileExists("logfile_full")) {
                    println("Local copy failed.")
                }
                return
            }
            // Titan / Hydra specific download mechanism
            if (taskJobURL.contains("gitlab-apertispro.boschdevcloud.com")) {
                withCredentials([usernameColonPassword(credentialsId: 'titan_hydra_jenkins_userpass', variable: 'JENKINS_AUTH'), string(credentialsId: 'titan_gitlab_token', variable: 'GITLAB_AUTH')]) {
                    try {
                        sh "cd dev-assistant/utils && python3 get_log.py --job_url ${taskJobURL}"
                        sh "cd dev-assistant/utils && python3 preparse_log.py"
                        // Preparse job log to remove xml characters, bug
                        sh "cd dev-assistant/utils && mv job_parsed.log ../../logfile_full"
                        sh "ls"
                    } catch (Exception e) {
                        println("Error when downloading logfile. Exception: " + e.message)
                    }
                    return
                }
            }
            // General Jenkins Mechanism
            withEnv(["url=$taskLogfileURL"]) {
                try {
                    def response = httpRequest(url: url, authentication: env.DEV_ASSISTANT_LOG_DOWNLOAD_CREDENTIALS_ID)
                    writeFile(file: 'logfile_full', text: response.content)
                } catch (Exception e) {
                    println("Error when downloading logfile. Exception: " + e.message)
                    // Make sure the file is cleared to not confuse the user. If there are leftovers in the file at download failures,
                    // the Log Analyzer will analyze it and report it to the user as if it was the real file
                    writeFile file: "logfile_full", text: ""
                }
            }
        }
    }
    stage('Validate logfile for neural training mode use') {
        if (params.LOG_ANALYZER_METHOD == "neural_training" && inputCommand.contains("analyze")) {
            def filePath = 'logfile_full'
            def fileContent = readFile(filePath)

            if (fileContent.contains('Finished: FAILURE') || fileContent.contains('Finished: UNSTABLE')) {
                println "Logfile from failed build detected, aborting the build..."
                currentBuild.result = 'ABORTED'
                error("Pipeline aborted due to failed logfile type found")
            } else
                println "Good logfile detected, proceed to neural training stage..."
        }
    }

    stage('Setup conda') {
        sh """#!/bin/bash
            set -uex
            source ${env.DEV_ASSISTANT_CONDA_PATH}
            conda create --name test_env
            conda activate test_env
        """
        // TODO: This can be done with a propper git.insteadof configuration. Then no variable is needed.
    }

    // Execute the Developer Assistant task
    stage('Execute dev-assistant task: ' + task) {
        if (task == "help") {
            responseMessage += HELP_MESSAGE
        } else if (task == "fakebuild") {
            responseMessage += "Build Failed\n" +
                    "${taskJobURL} : FAILURE\n" +
                    "${FAKEBUILD_COMMENT_MARKER}"
            response_vote = "--verified -1" // TODO: Check groundtruth file for return status and vote accordingly
        } else if (task == "benchtest") {
            sh """#!/bin/bash
                source /opt/conda/etc/profile.d/conda.sh
                conda activate test_env
                conda install pip
                python3 -m pip install -U -r ./log-analyzer/requirements.txt --proxy http://rb-proxy-de.bosch.com:8080 --index-url https://pypi.python.org/simple --trusted-host pypi.python.org
                sh ./log-analyzer/src/run/linux/testsuite.sh ./log-analyzer/log_analyzer/samples/ -t html -o ${BENCHTEST_FILE} -v
            """
            if (fileExists("${BENCHTEST_FILE}")) {
                archiveArtifacts "${BENCHTEST_FILE}"
            }
        } else if (task == "analyze") {
            withCredentials([string(credentialsId: env.DEV_ASSISTANT_AZURE_OPENAI_CREDENTIALS_ID, variable: 'AZURE_OPENAI_KEY'), string(credentialsId: env.LOG_ANALYZER_NEURAL_SEARCH_API_KEY_CREDENTIALS_ID, variable: 'NEURAL_SEARCH_API_KEY')]) {
                // If the logfile was not downloaded, give up
                if (!fileExists("logfile_full") || readFile("logfile_full").length() <= 0) {
                    responseMessage += "I was unable to download the log file from this location: ${taskLogfileURL}.\nPlease make sure that it exists and is accessible."
                    return
                }

                // Perform the analysis
                // Generate the files needed for the analysis response text: analysis_payload.json, llm_response_message and VISUAL_LOG_FILE
                methodParams = analyzerMethodMap[params.LOG_ANALYZER_METHOD] // Set analysis method
                ragParams = (env.LOG_ANALYZER_USE_RAG == "true") ? analyzerRagParameter : ""
                skipLLMQuery = (env.LOG_ANALYZER_DISABLE_LLM_QUERY == "true") || params.LOG_ANALYZER_METHOD == "neural_training"
                fastEmbedCachePath = "" + env.WORKSPACE + "/jenkinssharedlib/resources/models/"
                println("Log file exists. Sending it to log-analyzer. Method:" + params.LOG_ANALYZER_METHOD + " RAG:" + (env.LOG_ANALYZER_USE_RAG == "true"))
                sh """#!/bin/bash
                    source ${env.DEV_ASSISTANT_CONDA_PATH}
                    conda activate test_env
                    conda install pip
                    python3 -m pip install -U -r ./log-analyzer/requirements.txt --proxy http://rb-proxy-de.bosch.com:8080 --index-url https://pypi.python.org/simple --trusted-host pypi.python.org
                    export NEURAL_SEARCH_GOOD_COLLECTION=${collectionName}
                    export FASTEMBED_CACHE_PATH=${fastEmbedCachePath}
                    sh log-analyzer/src/run/linux/analyser.sh ./logfile_full --output ./analysis_payload.json --prompt_output ./prompt_input_file --verbose ${methodParams} ${ragParams} --streamer file
                    if [ "${skipLLMQuery}" = true ] ; then
                        echo "LLM query skipped."
                    else
                        export http_proxy=http://127.0.0.1:3128
                        export https_proxy=http://127.0.0.1:3128
                        export no_proxy=\"127.0.0.1,127.*,10.*,169.254.*,172.16.*,172.17.*,172.18.*,172.19.*,172.20.*,172.21.*,172.22.*,172.23.*,172.24.*,172.25.*,172.26.*,172.27.*,172.28.*,172.29.*,172.30.*,172.31.*,192.168.*,::1,localhost,de.bosch.com\"
                        python3 -m pip install -U -r ./llm-client/requirements.txt --proxy http://rb-proxy-de.bosch.com:8080 --index-url https://pypi.python.org/simple --trusted-host pypi.python.org
                        python3 llm-client/src/client/azure-openai-client.py --file ./prompt_input_file --output ./llm_response_text >> ./llm_return_value
                        unset http_proxy
                        unset https_proxy
                        unset no_proxy
                    fi
                """

                // Compose response text intro paragraph
                // Summarize if log indicates error or success, number of error lines, etc.
                // TODO: Consider moving this to a helper function in log-analyzer
                if (params.LOG_ANALYZER_METHOD == "neural_training") {
                    responseMessage = "I trained knowledge collection " + collectionName + " with this log file: " + (taskLogfileURL ?: "RAW_LOG") + "."
                } else if (params.LOG_ANALYZER_METHOD == "neural") {
                    responseMessage = "I analyzed this log file: ${taskLogfileURL} using the ${params.LOG_ANALYZER_METHOD} method and knowledge collection " + collectionName + ".\n\n"
                } else {
                    responseMessage = "I analyzed this log file: ${taskLogfileURL} using the ${params.LOG_ANALYZER_METHOD} method.\n\n"
                }

                // Add the LLM response message paragraph if it exists
                if (params.LOG_ANALYZER_METHOD == "neural_training") {
                    println("Training mode, nothing else to respond")
                } else if (fileExists("./llm_response_text") && readFile("./llm_response_text").length() > 0) {
                    responseMessage += readFile("./llm_response_text")
                } else { // If no LLM response message, compose a fallback response text
                    println("WARNING: LLM response message was missing or empty. Composing a fallback response text manually.")
                    if (fileExists("./llm_return_value")) {
                        llmReturnValue = readFile("./llm_return_value")
                        println("LLM Return value:")
                        println(llmReturnValue)
                    }
                    if (fileExists("./analysis_payload.json") && readFile("./analysis_payload.json").length() > 0) {
                        if (env.LOG_ANALYZER_DISABLE_LLM_QUERY == "true") {
                            responseMessage += "LLM usage disabled in settings, but here are some findings:\n"
                        } else {
                            responseMessage += "I was not able to summarize it due to an issue with my LLM.\n"
                        }
                        payload = readJSON file: "./analysis_payload.json"
                        successLines = payload["success_lines"]
                        errorLines = payload["root_causes"] ? (payload["root_causes"][0]["error_lines"]) : null
                        println("payload: " + payload)
                        println("successLines: " + successLines)
                        println("errorLines: " + errorLines)
                        if (successLines && successLines.size() > 0) { // Success line exists
                            responseMessage += "Log line #" + successLines[0] + " indicates that the overall execution was successful."
                            if (errorLines && errorLines.size() > 0) {
                                responseMessage += " However I also found log lines that indicate potential errors."
                            }
                        } else {
                            if (errorLines && errorLines.size() == 1) {
                                responseMessage += "Log line #" + errorLines[0] + " indicates an error."
                            } else if (errorLines && errorLines.size() > 1) {
                                responseMessage += "" + errorLines.size() + " log lines indicate errors."
                            }
                        }
                    } else {
                        responseMessage += "Unfortunately I encountered an error that prenvented me to complete the analysis. I apologize for the inconvenience :("
                    }
                }
                writeFile file: "./analysis_message", text: responseMessage

                // Generate the visual log, include the responseMessage text in it
                sh """#!/bin/bash
                    source ${env.DEV_ASSISTANT_CONDA_PATH}
                    conda activate test_env
                    conda install pip
                    python3 -m pip install -U -r ./log-analyzer/requirements.txt  --proxy http://rb-proxy-de.bosch.com:8080 --index-url https://pypi.python.org/simple --trusted-host pypi.python.org
                    sh log-analyzer/src/run/linux/visualiser.sh ./logfile_full ./analysis_payload.json --output ./${VISUAL_LOG_FILE} -l ./analysis_message
                """
                // TODO: Add config parameters for GZIP and COMPACT values
                // python3 log-analyzer/log_visualizer/main.py ./logfile_full ./analysis_payload.json --output ./${VISUAL_LOG_FILE} -l ./llm_response --gzip --compact_from 6000

                // Add a link to the visual log file artifact as the final paragraph
                if (fileExists("${VISUAL_LOG_FILE}") && readFile("${VISUAL_LOG_FILE}").length() > 0) {
                    archiveArtifacts "${VISUAL_LOG_FILE}"
                    responseMessage += "\n\nSee visual log here:\n${BUILD_URL}artifact/${VISUAL_LOG_FILE}"
                }
            }
        } else if (task == "prompt") {
            withCredentials([string(credentialsId: "${env.DEV_ASSISTANT_AZURE_OPENAI_CREDENTIALS_ID}", variable: 'AZURE_OPENAI_KEY')]) {
                if (env.LOG_ANALYZER_DISABLE_LLM_QUERY == "true") {
                    responseMessage += "LLM query was skipped (DISABLE_LLM_QUERY is set to true in job configuration). The input comment was interpreted as a general prompt."
                    return
                }
                // Note: There should not be a need to use a file as input, but seems to manage special characters better
                writeFile file: "./prompt_input_file", text: inputCommand
                sh """#!/bin/bash
                    source ${env.DEV_ASSISTANT_CONDA_PATH}
                    conda activate test_env
                    conda install pip
                    python3 -m pip install -U -r ./log-analyzer/log_analyzer/requirements.txt --proxy http://rb-proxy-de.bosch.com:8080 --index-url https://pypi.python.org/simple --trusted-host pypi.python.org
                    pip install openai
                    export http_proxy=http://127.0.0.1:3128
                    export https_proxy=http://127.0.0.1:3128
                    export no_proxy=\"127.0.0.1,127.*,10.*,169.254.*,172.16.*,172.17.*,172.18.*,172.19.*,172.20.*,172.21.*,172.22.*,172.23.*,172.24.*,172.25.*,172.26.*,172.27.*,172.28.*,172.29.*,172.30.*,172.31.*,192.168.*,::1,localhost,de.bosch.com\"
                    python3 -m pip install -U -r ./llm-client/requirements.txt --proxy http://rb-proxy-de.bosch.com:8080 --index-url https://pypi.python.org/simple --trusted-host pypi.python.org
                    python3 llm-client/src/client/azure-openai-client.py --file ./prompt_input_file --output ./llm_response_text >> ./llm_return_value
                    unset http_proxy
                    unset https_proxy
                    unset no_proxy
                """

                llmResponseText = readFile("./llm_response_text")
                if (llmResponseText.length() > 0) {
                    responseMessage += llmResponseText
                } else {
                    responseMessage += "Unfortunately I encountered an error that prenvented me to respond. I apologize for the inconvenience :("
                    println("LLM return value:")
                    println(readFile("./llm_return_value"))
                }
            }
        } else if (task == "review") {
            withCredentials([string(credentialsId: "${env.DEV_ASSISTANT_AZURE_OPENAI_CREDENTIALS_ID}", variable: 'AZURE_OPENAI_KEY')]) {
                if (env.LOG_ANALYZER_DISABLE_LLM_QUERY == "true") {
                    responseMessage += "No review performed. LLM query was skipped (DISABLE_LLM_QUERY is set to true in job configuration)."
                    return
                }
                println("Performing a code review of the following data: ")
                println(reviewChange)
                // Note: There should not be a need to use a file as input, but seems to manage special characters better
                writeFile file: "./prompt_input_file", text: "Review this code change. Find errors and propose how to fix these.\n" + reviewChange
                sh """#!/bin/bash
                    source ${env.DEV_ASSISTANT_CONDA_PATH}
                    conda activate test_env
                    conda install pip
                    python3 -m pip install -U -r ./log-analyzer/log_analyzer/requirements.txt  --proxy http://rb-proxy-de.bosch.com:8080 --index-url https://pypi.python.org/simple --trusted-host pypi.python.org
                    pip install openai
                    export http_proxy=http://127.0.0.1:3128
                    export https_proxy=http://127.0.0.1:3128
                    export no_proxy=\"127.0.0.1,127.*,10.*,169.254.*,172.16.*,172.17.*,172.18.*,172.19.*,172.20.*,172.21.*,172.22.*,172.23.*,172.24.*,172.25.*,172.26.*,172.27.*,172.28.*,172.29.*,172.30.*,172.31.*,192.168.*,::1,localhost,de.bosch.com\"
                    python3 -m pip install -U -r ./llm-client/requirements.txt --proxy http://rb-proxy-de.bosch.com:8080 --index-url https://pypi.python.org/simple --trusted-host pypi.python.org
					python3 llm-client/src/client/azure-openai-client.py --file ./prompt_input_file --output ./llm_response_text >> ./llm_return_value
                    unset http_proxy
                    unset https_proxy
                    unset no_proxy
                """

                llmResponseText = readFile("./llm_response_text")
                if (llmResponseText.length() > 0) {
                    responseMessage += llmResponseText
                } else {
                    responseMessage += "Unfortunately I encountered an error that prenvented me to respond. I apologize for the inconvenience :("
                    println("LLM return value:")
                    println(readFile("./llm_return_value"))
                }
            }
        }
        if (task == "feedback" || task == "analyze") {
            def feedbackResponse = null
            println("Log dev-assistant session in database")
            node('mysql') {
                fb_date = sh(script: 'date "+%Y-%m-%d %H:%M:%S"', returnStdout: true).toString().trim()
                if (params.GERRIT_EVENT_COMMENT_TEXT) {
                    sh """#!/bin/bash
                            echo "${GERRIT_EVENT_COMMENT_TEXT}" > user_comment_file
                        """
                    commit_id = "${params.GERRIT_CHANGE_NUMBER}/${params.GERRIT_PATCHSET_NUMBER}"
                } else {
                    sh """#!/bin/bash
                            echo "${inputCommand}" > user_comment_file
                        """
                    commit_id = "jenkins_build"
                }
                def sql = null
                def project_url = "${env.HUDSON_URL}"
                def index = project_url.indexOf('com/') + 4
                def project_id = project_url.substring(index).replace('/', '')
                // Figure out what type of feedback is given
                if (inputCommand.startsWith("feedback helpful")) {
                    verdict = sh(script: "grep -o helpful.* user_comment_file |  cut -d' ' -f2-", returnStdout: true).toString().toLowerCase().trim()
                    sql = "INSERT INTO knutgpt_statistics (fb_date,verdict,commit_id,project_id) VALUES('$fb_date','$verdict','$commit_id','$project_id');"
                } else if (inputCommand.startsWith("feedback comment")) {
                    comment = sh(script: "grep -o comment.* user_comment_file | cut -d' ' -f2-", returnStdout: true).toString().trim().replace('"', '')
                    sql = "INSERT INTO knutgpt_statistics (fb_date,comment,commit_id,project_id) VALUES('$fb_date','$comment','$commit_id','$project_id');"
                } else if (inputCommand.startsWith("feedback savedtime")) {
                    time_save = sh(script: "grep -o savedtime.* user_comment_file |  cut -d' ' -f2-", returnStdout: true).toString().toLowerCase().trim()
                    sql = "INSERT INTO knutgpt_statistics (fb_date,time_save,commit_id,project_id) VALUES('$fb_date','$time_save','$commit_id','$project_id');"
                } else if (inputCommand.startsWith("feedback deletehelpful")) {
                    sql = "DELETE FROM knutgpt_statistics WHERE commit_id LIKE '$commit_id' AND verdict IN ('yes', 'no');"
                } else if (inputCommand.startsWith("feedback deletecomment")) {
                    sql = "DELETE FROM knutgpt_statistics WHERE comment IS NOT NULL AND commit_id LIKE '$commit_id';"
                } else if (inputCommand.startsWith("feedback deletesavedtime")) {
                    sql = "DELETE FROM knutgpt_statistics WHERE time_save IS NOT NULL AND commit_id LIKE '$commit_id';"
                } else if (task == "analyze") {
                    sql = "INSERT INTO knutgpt_statistics (fb_date,commit_id,project_id) VALUES('$fb_date','$commit_id','$project_id');"
                } else {
                    feedbackResponse = FEEDBACK_HELP_MESSAGE
                }
                if (sql) {
                    try {
                        rbGrafana.runSQL(sql_cmd: sql, database: 'KNUTGPT')
                        println("Feedback saved to database")
                        feedbackResponse = "Your feedback for this analysis session was updated, thank you!"
                    } catch (Exception e) {
                        errorMessage = "An error occurred while executing the SQL command: ${e.message}"
                        println(errorMessage)
                        feedbackResponse = "I'm sorry, I was unable to store your feedback in the database. Please try again later."
                    }
                }
            }
            if (task == "feedback") {
                responseMessage = feedbackResponse
            }
        }
    }

    // Post a response comment to Gerrit
    // TODO: Use json to post comment, current solution seems sensitive to double and single quotes
    // Example: cat ${RESPONSE_FILE}.json | ssh -p 29418 rbcm-gerrit.de.bosch.com gerrit review ${GERRIT_CHANGE_NUMBER},${GERRIT_PATCHSET_NUMBER} --json
    // Tip: How to use ssh key credentials: //withCredentials([sshUserPrivateKey(credentialsId: "638e2d31-60d2-45d8-9cae-8661bcc278b2", keyFileVariable: 'KEYFILE')]) {
    stage('Send response comment') {
        if (responseMessage == null || responseMessage.length() <= 0) {
            println("responseMessage string is null or empty. Nothing to respond.")
        } else {
            // Archive the response message
            println("Response message:")
            println(responseMessage)
            writeFile file: RESPONSE_FILE, text: responseMessage, encoding: "UTF-8"
            archiveArtifacts RESPONSE_FILE

            // If the job was triggered by a Gerrit user comment, send the response as a comment to the same change review session
            if (wasTriggeredByGerritComment) {
                // Replace " with ´´ to avoid errors. The ssh command otherwise gets messed up
                gerritResponse = readFile("${RESPONSE_FILE}").replaceAll('"', '´´').replaceAll(':', "").replaceAll('\'', "´")
                // TODO: Do smarter...
                sh """#!/bin/bash
                    set -eux
                    ssh -p 29418 rbcm-gerrit.de.bosch.com gerrit review ${params.GERRIT_CHANGE_NUMBER},${params.GERRIT_PATCHSET_NUMBER} ${response_vote} --message \'"${gerritResponse}"\'
                """
            }
        }
    }
}
