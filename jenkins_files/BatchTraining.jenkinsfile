/* groovylint-disable LineLength */

// BatchTraining pipeline - simplified version for training neural models with folders of log files
def DEFAULT_GOOD_KNOWLEDGE_COLLECTION = "at_" + env.DEV_ASSISTANT_KNOWLEDGE_COLLECTION_PREFIX

// Ensure required environment variables are set
def assertMandatory(varValue, failMessage) {
    assert varValue != null && varValue.length() > 0: "ERROR. Jenkins configuration is missing a mandatory environment variable: " + failMessage
}

assertMandatory(env.DEV_ASSISTANT_KNOWLEDGE_COLLECTION_PREFIX, "DEV_ASSISTANT_KNOWLEDGE_COLLECTION_PREFIX. Name of the default collection prefix for good log lines")

properties([
    parameters([
        string(name: 'FOLDER_PATH', defaultValue: '/mnt/knutmount/demo_logs/hydra/successful/',
               description: 'Path to the folder containing log files for training', trim: true),
        string(name: 'FILE_PATTERN', defaultValue: 'log-1000*',
               description: 'File pattern to match within the folder (e.g., log-*.txt)', trim: true),
        choice(name: 'OPERATION', choices: ['training', 'analysis'],
               description: 'Operation to performon the files', defaultValue: 'training'),
        choice(name: 'ANALYSIS_TYPE', choices: ['neural','keywords','regexps'],
               description: 'Analysis type to use for training', defaultValue: 'neural'),
        string(name: 'MATCH_AT', defaultValue: '0.98',
               description: 'Match threshold (recommended: 0.98 for training)', trim: true),
        string(name: 'COLLECTION', defaultValue: DEFAULT_GOOD_KNOWLEDGE_COLLECTION,
               description: 'The Vector DB collection name to train', trim: true),
        booleanParam(name: 'VERBOSE', defaultValue: false,
                    description: 'Enable verbose output'),
    ]),
    buildDiscarder(logRotator(numToKeepStr: '10')),
])

node(env.DEV_ASSISTANT_JENKINS_NODE_LABEL) {
    cleanWs()

    if (params.OPERATION == 'training') {
        currentBuild.displayName = "Training ${params.ANALYSIS_TYPE} ${params.COLLECTION} with ${params.FOLDER_PATH} files ${params.FILE_PATTERN}"
    }
    else {
        def collectionStr = params.ANALYSIS_TYPE == 'neural' ? " collection ${params.COLLECTION}" : ""
        currentBuild.displayName = "Analyze ${params.FOLDER_PATH} with ${params.ANALYSIS_TYPE}${collectionStr} files ${params.FILE_PATTERN}"
    }

    stage('Clone repositories') {
        withCredentials([gitUsernamePassword(credentialsId: "${env.LOG_ANALYZER_BDC_CREDENTIALS_ID}")]) {
            sh """#!/bin/bash
                git clone --depth 1 --branch ${env.LOG_ANALYZER_GIT_REVISION} https://github.boschdevcloud.com/lund-common/log-analyzer.git
                git clone --depth 1 --branch ${env.LLM_CLIENT_GIT_REVISION} https://github.boschdevcloud.com/lund-common/llm-client.git
            """
        }

        if (!fileExists("log-analyzer") || !fileExists("llm-client")) {
            error("Failed to clone necessary git repos. Aborting.")
        }
    }

    stage('Setup conda environment') {
        sh """#!/bin/bash
            source ${env.DEV_ASSISTANT_CONDA_PATH}

            # Define environment name and paths
            ENV_NAME="log_analyzer_env"
            ENV_ARCHIVE="/mnt/knutmount/envs/conda_env_py312.tar.gz"
            WORKSPACE_ENV_PATH="${env.WORKSPACE}/conda_env"

            # First check if environment already exists and remove it
            conda env remove -n \${ENV_NAME} -y || true

            # Check if archive exists in read-only DFS and use it
            if [ -f "\${ENV_ARCHIVE}" ]; then
                echo "Using cached environment from \${ENV_ARCHIVE}"
                # Extract to workspace env path instead of conda envs dir
                mkdir -p \${WORKSPACE_ENV_PATH}
                tar -xzf "\${ENV_ARCHIVE}" -C "${env.WORKSPACE}"
                # Activate using path instead of name
                conda activate \${WORKSPACE_ENV_PATH}

                # Quick update of packages
                # pip install -U -r ./log-analyzer/requirements.txt --proxy http://rb-proxy-de.bosch.com:8080 --index-url https://pypi.python.org/simple --trusted-host pypi.python.org
            else
                echo "Cached environment not found, creating new environment in workspace"
                # Create new environment directly in the workspace
                mkdir -p \${WORKSPACE_ENV_PATH}
                conda create --prefix \${WORKSPACE_ENV_PATH} python=3.12 -y
                conda activate \${WORKSPACE_ENV_PATH}

                # Install packages
                pip install -U -r ./log-analyzer/requirements.txt --proxy http://rb-proxy-de.bosch.com:8080 --index-url https://pypi.python.org/simple --trusted-host pypi.python.org

                # Create a tar.gz of the environment directly in the workspace
                WORKSPACE_ARCHIVE="${env.WORKSPACE}/conda_env_py312.tar.gz"
                echo "Creating archive of the environment at \${WORKSPACE_ARCHIVE}"
                tar -czf "\${WORKSPACE_ARCHIVE}" -C "${env.WORKSPACE}" conda_env
                echo "Environment archived to \${WORKSPACE_ARCHIVE}"
                echo "You can download this file from Jenkins artifacts and manually copy to /mnt/knutmount/envs/ for future use"
            fi
        """
    }

    stage('Run batch training') {
        withCredentials([string(credentialsId: env.LOG_ANALYZER_NEURAL_SEARCH_API_KEY_CREDENTIALS_ID, variable: 'NEURAL_SEARCH_API_KEY')]) {
            // Create output directory
            sh "mkdir -p ./results"

            // Prepare the command
            def verboseFlag = params.VERBOSE ? "--verbose" : ""
            def trainingFlag = params.OPERATION == 'training' ? "--training" : ""

            if (params.OPERATION == 'training' && params.ANALYSIS_TYPE != "neural") {
                error("Training operation is possible just for 'neural' analysis type. Use 'neural' for training.")
            }

            // Run the training script using the extracted environment
            sh """#!/bin/bash
                source ${env.DEV_ASSISTANT_CONDA_PATH}

                # Activate using the workspace path instead of name
                conda activate ${env.WORKSPACE}/conda_env

                export NEURAL_SEARCH_GOOD_COLLECTION=${params.COLLECTION}
                export FASTEMBED_CACHE_PATH=/mnt/knutmount/models/

                cd log-analyzer
                python3 src/batch.py "${params.FOLDER_PATH}" "${params.FILE_PATTERN}" \
                    --output_dir ${env.WORKSPACE}/results \
                    --analysis_type ${params.ANALYSIS_TYPE} \
                    --match_at ${params.MATCH_AT} \
                    --collection ${params.COLLECTION} \
                    ${trainingFlag} ${verboseFlag}
            """
        }
    }

    stage('Print results') {
        def fileCount = sh(script: "ls -1 results/*.html | wc -l", returnStdout: true).trim()

        if (params.OPERATION == 'training') {
            def descriptionMessage = "Trained ${params.COLLECTION} with files matching '${params.FILE_PATTERN}', ${fileCount} files"
        }
        else {
            def descriptionMessage = "Analyzed files in ${params.FOLDER_PATH} with pattern '${params.FILE_PATTERN}', ${fileCount} files"
        }

        // Archive artifacts
        archiveArtifacts artifacts: 'results/*.html', allowEmptyArchive: true
        archiveArtifacts artifacts: 'results/*.txt', allowEmptyArchive: true
        archiveArtifacts artifacts: 'results/*.json', allowEmptyArchive: true
        archiveArtifacts artifacts: 'conda_env_py312.tar.gz', allowEmptyArchive: true
    }

    stage('Cleanup') {
        // Clean up the extracted environment to save workspace storage
        sh """#!/bin/bash
            echo "Cleaning up conda environment..."
            rm -rf ${env.WORKSPACE}/conda_env
            echo "Cleanup completed"
        """
    }
}
