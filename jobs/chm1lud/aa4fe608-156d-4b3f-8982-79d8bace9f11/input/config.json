{"sessionType": "analyze", "method": "keywords", "knowledgeCollection": "", "errorKeywords": ["abort", "bad", "break", "cannot open", "corrupt", "crash", "critical", "damage", "defect", "degrade", "deny", "disconnect", "disrupt", "diverge", "does not exist", "errno", "error", "exceed", "exception", "expire", "fail", "failure", "fatal", "fault", "faulty", "forbidden", "halt", "impair", "inaccessible", "incompatible", "inoperative", "interrupt", "invalid", "malformed", "malfunction", "misalign", "misbehave", "miscalculate", "mismatch", "traceback", "missing", "cannot run", "not found", "null", "obstruct", "out of range", "overflow", "problem", "refuse", "raise", "reject", "stop", "terminate", "timeout", "unable", "unauthorized", "unavailable", "underflow", "unexpected", "unreachable", "unsound", "unstable", "violate", "build failed", "compilation failed", "compile error", "linker error", "linking failed", "dependency not found", "syntax error", "parse error", "import error", "module not found", "package not found", "permission denied", "access denied", "file not found", "out of memory", "segmentation fault", "assertion failed", "test failed", "deployment failed", "connection failed", "killed", "aborted", "core dumped"], "falsePositiveKeywords": ["fault interf", "default", "default value", "default setting", "error handling", "error recovery", "error message", "fault tolerance", "test error", "expected error", "error log", "error report", "checking for errors", "error check"], "hintsKeywords": ["anomaly", "deprecate", "disallow", "discrepancy", "flaw", "glitch", "ineffective", "infeasible", "insufficient", "inconsistent", "issue", "mishap", "misstep", "neglect", "oversight", "stale", "unsuccessful", "warning", "block", "blunder", "deprecated", "obsolete", "outdated", "experimental", "beta", "performance warning", "memory warning", "compatibility warning", "security warning", "disk space warning", "unused", "unreachable", "todo", "fixme", "0 errors", "no errors", "no issues", "not an issue", "not a problem", "success", "complete"], "successKeywords": ["lll"], "errorRegexes": [], "falseRegexes": [], "hintsRegexes": [], "successRegexes": []}