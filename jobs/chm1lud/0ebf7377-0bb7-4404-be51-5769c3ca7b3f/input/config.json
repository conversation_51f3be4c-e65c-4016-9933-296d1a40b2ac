{"sessionType": "analyze", "method": "keywords", "knowledgeCollection": "", "errorKeywords": ["abort", "bad", "break", "cannot open", "corrupt", "crash", "critical", "damage", "defect", "degrade", "deny", "disconnect", "disrupt", "diverge", "does not exist", "errno", "error", "exceed", "exception", "expire", "fail", "failure", "fatal", "fault", "faulty", "forbidden", "halt", "impair", "inaccessible", "incompatible", "inoperative", "interrupt", "invalid", "malformed", "malfunction", "misalign", "misbehave", "miscalculate", "mismatch", "traceback", "missing", "cannot run", "not found", "null", "obstruct", "out of range", "overflow", "problem", "refuse", "raise", "reject", "stop", "terminate", "timeout", "unable", "unauthorized", "unavailable", "underflow", "unexpected", "unreachable", "unsound", "unstable", "violate", "build failed", "compilation failed", "compile error", "linker error", "linking failed", "dependency not found", "syntax error", "parse error", "import error", "module not found", "package not found", "permission denied", "access denied", "file not found", "out of memory", "segmentation fault", "assertion failed", "test failed", "deployment failed", "connection failed", "killed", "aborted", "core dumped"], "falsePositiveKeywords": [], "hintsKeywords": [], "successKeywords": [], "errorRegexes": [], "falseRegexes": [], "hintsRegexes": [], "successRegexes": []}