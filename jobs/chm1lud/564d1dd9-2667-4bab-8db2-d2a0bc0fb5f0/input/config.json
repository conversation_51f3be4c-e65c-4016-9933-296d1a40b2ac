{"sessionType": "analyze", "method": "regex", "knowledgeCollection": "", "errorKeywords": [], "falsePositiveKeywords": [], "hintsKeywords": [], "successKeywords": [], "errorRegexes": ["\\b(abort|aborted)\\b", "\\bbad\\b", "\\bbreak\\b", "cannot\\s+open", "\\bcorrupt(ed)?\\b", "\\bcrash(ed)?\\b", "\\bcritical\\b", "\\bdamage(d)?\\b", "\\bdefect(s)?\\b", "\\bdegrade(d)?\\b", "\\bdeny\\b", "disconnect(ed)?", "\\bdisrupt(ed)?\\b", "\\bdiverge(d)?\\b", "does\\s+not\\s+exist", "\\berrno\\b", "\\berror(s)?\\b", "\\bexceed(ed)?\\b", "\\bexception(s)?\\b", "\\bexpire(d)?\\b", "\\bfail(ed)?\\b", "\\bfailure(s)?\\b", "\\bfatal\\b", "\\bfault(s)?\\b", "\\bfaulty\\b", "\\bforbidden\\b", "\\bhalt(ed)?\\b", "\\bimpair(ed)?\\b", "\\binaccessible\\b", "\\bincompatible\\b", "\\binoperative\\b", "\\binterrupt(ed)?\\b", "\\binvalid\\b", "\\bmalformed\\b", "\\bmalfunction(ed)?\\b", "\\bmisalign(ed)?\\b", "\\bmisbehave(d)?\\b", "\\bmiscalculate(d)?\\b", "\\b<PERSON><PERSON>(ed)?\\b", "\\btraceback\\b", "\\bmissing\\b", "cannot\\s+run", "not\\s+found", "\\bnull\\b", "\\bobstruct(ed)?\\b", "out\\s+of\\s+range", "\\boverflow\\b", "\\bproblem(s)?\\b", "\\brefuse(d)?\\b", "\\braise(d)?\\b", "\\breject(ed)?\\b", "\\bstop(ped)?\\b", "\\bterminate(d)?\\b", "\\btimeout\\b", "\\bunable\\b", "\\bunauthorized\\b", "\\bunavailable\\b", "\\bunderflow\\b", "\\bunexpected(ly)?\\b", "\\bunreachable\\b", "\\bunsound\\b", "\\bunstable\\b", "\\bviolate(d)?\\b", "build\\s+failed", "compilation\\s+failed", "compile\\s+error", "linker\\s+error", "linking\\s+failed", "dependency\\s+not\\s+found", "syntax\\s+error", "parse\\s+error", "import\\s+error", "module\\s+not\\s+found", "package\\s+not\\s+found", "permission\\s+denied", "access\\s+denied", "file\\s+not\\s+found", "out\\s+of\\s+memory", "segmentation\\s+fault", "assertion\\s+failed", "test\\s+failed", "deployment\\s+failed", "connection\\s+failed", "\\bkilled\\b", "core\\s+dumped"], "falseRegexes": ["fault\\s+interf", "\\bdefault\\b", "default\\s+value", "default\\s+setting", "error\\s+handling", "error\\s+recovery", "error\\s+message", "fault\\s+tolerance", "test\\s+error", "expected\\s+error", "error\\s+log", "error\\s+report", "checking\\s+for\\s+errors", "error\\s+check"], "hintsRegexes": ["\\banomaly\\b", "\\bdeprecate(d)?\\b", "\\b<PERSON><PERSON><PERSON>(ed)?\\b", "\\bdiscrepancy\\b", "\\bflaw(s)?\\b", "\\bglitch(es)?\\b", "\\bineffective\\b", "\\binfeasible\\b", "\\binsufficient\\b", "\\binconsistent\\b", "\\bissue(s)?\\b", "\\bmishap(s)?\\b", "\\bmisstep(s)?\\b", "\\bneglect(ed)?\\b", "\\boversight(s)?\\b", "\\bstale\\b", "\\bunsuccessful\\b", "\\bwarning(s)?\\b", "\\bblock(ed)?\\b", "\\bblunder(s)?\\b", "\\bobsolete\\b", "\\boutdated\\b", "\\bexperimental\\b", "\\bbeta\\b", "performance\\s+warning", "memory\\s+warning", "compatibility\\s+warning", "security\\s+warning", "disk\\s+space\\s+warning", "\\bunused\\b", "\\bunreachable\\b", "\\btodo\\b", "\\bfixme\\b"], "successRegexes": ["aaa"]}