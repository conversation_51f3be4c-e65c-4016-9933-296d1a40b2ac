# Batch Processing Tool Documentation

## Overview

`batch.py` is a command-line tool that performs batch processing of log files for analysis. It processes multiple files matching a specified pattern in a given folder and generates analysis results, prompts, and HTML visualizations.

## Features

- Process multiple log files in a single command
- Support for various analysis types (default: neural)
- Configurable matching threshold
- HTML visualization generation
- Training mode support
- Verbose and debug output options

## Usage

python batch.py <folder> [filepattern] [options]

## Arguments

| Argument         | Description                        | Default                |
|------------------|------------------------------------|------------------------|
| folder           | Folder containing files to process | (Required)             |
| filepattern      | File pattern to match              | log-*                  |
| --analysis_type  | Analysis type                      | neural                 |
| --match_at       | Match threshold                    | 0.95                   |
| --output_dir     | Directory for output files         | ../output              |
| --collection     | Collection to use                  | good_c                 |
| --verbose        | Enable verbose output              | False                  | |
| --quiet          | Suppress non-essential output      | False                  |
| --training       | Run in training mode               | False                  |
| --ini_config     | Path to configuration file         | DEFAULT_CONFIG_FILE    |

## Output Files

For each processed file, the script generates:

- JSON results: <basename>_result_<analysis_type>.json
- Prompt output: <basename>_prompt.txt
- HTML visualization: <basename>_visualisation.html

## Examples

### Analysis Mode

```bash
python batch.py "C:\development\data\hydra\failed" log-* --output_dir "../res/analysis" --analysis_type neural --match_at 0.95 --collection at_hydra_dupli
```

### Training Mode

```bash
python batch.py "C:\development\data\hydra\successful" log-* --output_dir "../res/training" --analysis_type neural --match_at 0.98 --collection at_hydra_dupli --training
```

## Process Flow

1. Parse command-line arguments
2. Find files matching the pattern in the specified folder
3. Process each file:
   - Analyze the file using LogAnalyser
   - Generate JSON results and prompt output
   - Create HTML visualization
4. Display processing summary
