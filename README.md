<div align="center">

# Knut GPT

  <img src="src/visualiser/static/Knut_logo_simplified_compacted.svg" alt="KnutGPT Logo" width="150">

## Log Analyzer Tool Suite

*Advanced log analysis tools providing foundational analysis capabilities for KnutGPT*

[![Python 3.12+](https://img.shields.io/badge/Python-3.12+-blue.svg)](https://www.python.org/downloads/)
[![Experimental](https://img.shields.io/badge/Status-MVP-orange.svg)]()
</div>

## 🔍 Overview

The Log Analyzer Tools comprise Python classes and utilities capable of analyzing log files and producing actionable insights.

## 🧰 Tools

| Tool                                        | Description                                                                                                                                                                           |
|---------------------------------------------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| [**Analyzer**](src/analyser/README.md)      | Detects anomalies in log files using statistical methods, AI models, and clustering techniques. Now supports unified vocabulary files via the `--vocabulary` argument.                |
| [**Prompter**](src/prompter/README.md)      | Generates context-specific LLM Prompts from log analysis results, including step-by-step problem analysis prompts. Supports unified vocabulary files via the `--vocabulary` argument. |
| [**Visualiser**](src/visualiser/README.md)  | Creates visual representations of log files for easier interpretation.                                                                                                                |
| [**Test Suites**](src/testsuites/README.md) | Operates the Log Analyzer with all analysis methods for testing and evaluation purposes.                                                                                              |
| [**API**](src/api/README.md)                | REST API for the log analyzer tools (in development).                                                                                                                                 |

## ⚠️ Warning

> **Note**: The current implementation is experimental and may have known or unknown bugs or limitations. Users are encouraged to share bugs or improvement suggestions via GitHub's
> Issue Tracker or by emailing the development team. A more polished pre-release will be available soon.

## 📋 Installation and Setup

> **Important**: If you ran a previous version, make sure to reinstall the dependencies in `requirements.txt`.

### Prerequisites

- Python 3.12.0 or newer
- Several GB of disk space (for ML packages)

### Setup Instructions

1. Install Black formatter (mandatory for consistent code style).
2. Set up line length to 180 characters (for consistency) in your IDE or editor settings to match the Black formatter's default.
    - Why? Because the 88 charter standards were set by guys nostalgic for the 80s CGA and VGA monitors, and we are not in the 80s anymore, we are in 2025+. We have HD, 4K and 8K
      monitors now, so let's use them efficiently. Secondly, because you can have better insights with long lines when you search in folders and the code is simply more readable
      with log lines on high-resolution monitors.
3. Install [PDM](https://pdm-project.org/latest/#__tabbed_1_2) (optional but highly recommended)
    - PDM is a modern Python package manager that simplifies dependency management and project setup.
    - Follow the [PDM installation guide](https://pdm-project.org/latest/installation/) for your platform.
4. Create a virtual environment (mandatory):

```bash
python -m venv .venv
```

**Activate virtual environment**

> **Note**: Always remember to activate the virtual environment each time before running the script!

Linux:

```bash
.venv/bin/activate
```

Windows:

```
.venv\Scripts\activate.bat
```

**Install the dependencies using pip**

```bash
pip install -r requirements.txt
```

### Update history

| Date       | Author               | Reference |
|------------|----------------------|-----------|
| 2025-01-24 | Mihai-Ciprian Chezan | CHM1LUD   |
